// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError,UserInputError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../../common/appconstants');
// require table alias function
const { ehrTables } = require('../../../common/tablealias');

// variable declarations
let errResult ={};
let organizationDbConnection = '';

// resolver definition
const resolvers = {
    Query:{
        // function to retrieve share vesting history
        retrieveShareVestHistory:async (parent, args, context, info)=>{
            let validationError={};
            try{
                console.log('Inside retrieveShareVestHistory function');
                let source=(args.source)?(args.source).toLowerCase():''
                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                let logInEmpId = context.Employee_Id;
                // Check ESOP form view access rights exist for employee or not
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.esop, '', 'UI');   
                if(Object.keys(checkRights).length>0 && checkRights.Role_View === 1) {
                    /** Validate input source params*/
                    if(source){
                        if((source!== 'esop') && (source!== 'myesop')){
                            validationError['IVE0120'] = commonLib.func.getError('', 'IVE0120').message;
                        }
                    }
                    else{
                        validationError['IVE0120'] = commonLib.func.getError('', 'IVE0120').message;
                    }
                    // validate allocated shareId
                    if(!args.allocatedShareId){
                        validationError['IVE0163'] = commonLib.func.getError('', 'IVE0163').message;
                    }
                    /** If source is esop then request from admin view. Then check whether loggedIn employee have admin access to benefits admin form */
                    if(source==='esop'){
                        // Check benefits admin form - edit access rights exist for employee or not
                        let checkBenefitsAdminRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.benefitsAdmin, '', 'UI');
                        if(Object.keys(checkBenefitsAdminRights).length<0 || checkBenefitsAdminRights.Role_Update === 0) {
                            throw '_DB0109';
                        }
                    }
                    // check validation error exist or not
                    if (Object.keys(validationError).length === 0) {
                        // get the value of a share from configuration table
                        return(
                            organizationDbConnection(ehrTables.benefitsConfiguration)
                            .select('Value_Of_Share as shareValue')
                            .then(async (getConfiguration) =>{
                                if(getConfiguration.length > 0){
                                    let shareValue=getConfiguration[0].shareValue;
                                    // get the share vesting details based on input shareId
                                    return(
                                        organizationDbConnection(ehrTables.employeeShareVestHistory)
                                        .select(
                                            'Vested_Date as vestedDate',
                                            'Vested_Share as vestedShare',
                                            organizationDbConnection.raw('Vested_Share * ? as totalShare', [shareValue])
                                        )                                                                                  
                                        .where('Employee_Allocated_Share_Id',args.allocatedShareId)
                                        .then(getDetails => {
                                            if(getDetails.length>0){
                                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                return {errorCode:'',message:'Share vesting history retrieved successfully.',vestedDetails:getDetails};
                                            }
                                            else{
                                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                return {errorCode:'',message:'Share vesting history does not exist.',vestedDetails:[]};
                                            }
                                        })
                                    );
                                }
                                else{
                                    throw 'BES0006';
                                }
                            })
                            .catch(function (catchError) {
                                console.log('Error in retrieveShareVestHistory function .catch block',catchError);
                                errResult = commonLib.func.getError(catchError, 'BES0114');
                                // destroy database connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                throw new ApolloError(errResult.message,errResult.code);
                            })
                        );
                    }
                    else {
                        throw 'IVE0000';
                    }
                }
                else{
                    throw '_DB0100';
                }
            }catch(mainCatchError){
                console.log('Error in retrieveShareVestHistory function main catch block.', mainCatchError);
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                if(mainCatchError==='IVE0000'){
                    errResult = commonLib.func.getError('', 'IVE0000');
                    throw new UserInputError(errResult.message, { validationError: validationError });
                }
                else{
                    errResult = commonLib.func.getError(mainCatchError, 'BES0011');
                    // return response
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        }        
    }
};

exports.resolvers = resolvers;
