// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require common constant files
const constants = require('../../../common/appconstants');

// resolver definition
const resolvers = {
    Query: {
        // function to list my payroll actions in dashboard
        listPayrollActions: async (parent, args, context, info) => {
            // variable declarations
            let organizationDb='';
            let payrollList={};
            let finalResponse={};            
            try{   
                console.log('Inside my payroll actions function');

                // make database connection
                organizationDb = knex(context.connection.OrganizationDb);
                let employeeId=context.Employee_Id;

                // function to get advance salary related actions
                let advanceSalaryData = await commonLib.employees.listPayrollActionsInDashboard(employeeId, organizationDb,constants.formIds.advanceSalary);
                payrollList['advanceSalary']=advanceSalaryData;

                // function to get bonus related actions
                let bonusData = await commonLib.employees.listPayrollActionsInDashboard(employeeId, organizationDb,constants.formIds.bonus);
                payrollList['bonus']=bonusData;

                // function to get commission related actions
                let commissionData = await commonLib.employees.listPayrollActionsInDashboard(employeeId, organizationDb,constants.formIds.commission);
                payrollList['commission']=commissionData;

                // function to get deductions related actions
                let deductionData = await commonLib.employees.listPayrollActionsInDashboard(employeeId, organizationDb,constants.formIds.deductions);
                payrollList['deduction']=deductionData;

                // function to get loan related actions
                let loanData = await commonLib.employees.listPayrollActionsInDashboard(employeeId, organizationDb,constants.formIds.loan);
                payrollList['loan']=loanData;

                // function to get deferred loan related actions
                let deferredLoanData = await commonLib.employees.listPayrollActionsInDashboard(employeeId, organizationDb,constants.formIds.deferredLoan);
                payrollList['deferredLoan']=deferredLoanData;                

                // function to get reimbursement related actions
                let reimbursementData = await commonLib.employees.listPayrollActionsInDashboard(employeeId, organizationDb,constants.formIds.reimbursement);
                payrollList['reimbursement']=reimbursementData;

                // function to get shiftAllowance related actions
                let shiftAllowanceData = await commonLib.employees.listPayrollActionsInDashboard(employeeId, organizationDb,constants.formIds.shiftAllowance);
                payrollList['shiftAllowance']=shiftAllowanceData;

                // function to get selfOccupiedProperty related actions
                let selfOccupiedIncomeData = await commonLib.employees.listPayrollActionsInDashboard(employeeId, organizationDb,constants.formIds.incomeUnderSection24,'Self Occupied');
                payrollList['selfOccupiedProperty']=selfOccupiedIncomeData;

                // function to get rentedProperty related actions
                let rentedPropertyData = await commonLib.employees.listPayrollActionsInDashboard(employeeId, organizationDb,constants.formIds.incomeUnderSection24,'Rented Property');
                payrollList['rentedProperty']=rentedPropertyData;

                // form final response json
                finalResponse['payrollActionList']=payrollList;

                // destroy database connection
                organizationDb ? organizationDb.destroy() : null;
                // return success response
                return { errorCode: '', message: 'Payroll actions retrieved successfully.', payrollActionsList: (Object.keys(finalResponse).length>0)?JSON.stringify(finalResponse):''};
            }
            catch(mainCatchError)
            {
                console.log('Error in listPayrollActions function catch block',mainCatchError);
                // destroy database connection
                organizationDb ? organizationDb.destroy() : null;
                // get the code and message from common function based on returned error code
                let errResult = commonLib.func.getError(mainCatchError, 'DB0018');
                // return error response
                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, payrollActionsList : ''}));                
            }
        },
        // function to list my employee actions in dashboard
        listEmployeeActions: async (parent, args, context, info) => {
            // variable declarations
            let organizationDb='';
            let employeeActions={};
            let outputResponse={};            
            try{   
                console.log('Inside my employee actions function');

                // make database connection
                organizationDb = knex(context.connection.OrganizationDb);
                let employeeId=context.Employee_Id;

                // function to get employee travel related actions
                let employeeTravelData = await commonLib.employees.listEmployeesActionsInDashboard(employeeId, organizationDb,constants.formIds.employeeTravel);
                employeeActions['employeeTravel']=employeeTravelData;

                // function to get attendance related actions
                let attendanceData = await commonLib.employees.listEmployeesActionsInDashboard(employeeId, organizationDb,constants.formIds.attendance);
                employeeActions['attendance']=attendanceData;

                // function to get performance assesment related actions
                let performanceData = await commonLib.employees.listEmployeesActionsInDashboard(employeeId, organizationDb,constants.formIds.performanceAssesment);
                employeeActions['performanceAssessment']=performanceData;

                // function to get timesheets related actions
                let timesheetData = await commonLib.employees.listEmployeesActionsInDashboard(employeeId, organizationDb,constants.formIds.timesheets);
                employeeActions['timesheets']=timesheetData;

                // function to get transfer related actions
                let transferData = await commonLib.employees.listEmployeesActionsInDashboard(employeeId, organizationDb,constants.formIds.transfer);
                employeeActions['transfer']=transferData;

                // function to get resignation related actions
                let resignationData = await commonLib.employees.listEmployeesActionsInDashboard(employeeId, organizationDb, constants.formIds.resignation);
                employeeActions['resignation'] = resignationData;

                // form final response json
                outputResponse['employeeActionList']=employeeActions;

                // destroy database connection
                organizationDb ? organizationDb.destroy() : null;
                // return success response
                return { errorCode: '', message: 'Employee actions retrieved successfully.', employeeActionsList: (Object.keys(outputResponse).length>0)?JSON.stringify(outputResponse):''};
            }
            catch(mainCatchError)
            {
                console.log('Error in listEmployeeActions function catch block',mainCatchError);
                // destroy database connection
                organizationDb ? organizationDb.destroy() : null;
                // get the code and message from common function based on returned error code
                let errResult = commonLib.func.getError(mainCatchError, 'DB0019');
                // return error response
                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, employeeActionsList : ''}));                
            }
        }        
    }
}

exports.resolvers = resolvers;
