// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require table alias
const {ehrTables} = require('./tablealias');
const { validateWithRules } = require('@cksiva09/validationlib/src/validator');

//Validation Lib Common Function
async function validateCommonRuleInput(args, fieldValidations) {
    try {
        let validationError = {};
        for (const field in fieldValidations) {
            let fieldName = field; // By default, use the current field name
            if (field && args.hasOwnProperty(field)) {
                const validation = validateWithRules(args[field], fieldName);
                if (validation && validation !== 'Validation not found' && (!validation.validation || !validation.length)) {

                    validationError[fieldValidations[field]] = validation && validation.length ? commonLib.func.getError('', fieldValidations[field]).message : commonLib.func.getError('', fieldValidations[field]).message1;
                }
            }
        }
        return validationError;
    }
    catch (err) {
        console.log('Error in the validateCommonRuleInput function in the main catch block.', err);
        throw err;
    }
}

// function to insert system logs
async function insertSystemLogs(organizationDbConnection,action,ipAddress,loggedInEmpId,formName,trackingColumn,uniqueId=null,message){
    try{
        // formation of system log params
        let systemLogParams = {
            action : action,
            userIp : ipAddress,
            employeeId: loggedInEmpId,
            formName : formName,
            trackingColumn: trackingColumn,
            organizationDbConnection: organizationDbConnection,
            uniqueId : (uniqueId)?(uniqueId):'',
            message:(message)?message:''
        }
        let createLog= await commonLib.func.createSystemLogActivities(systemLogParams);
        return createLog;
    }
    catch(catchError){
        console.log('Error in insertSystemLogs function main catch block',catchError);
        return '';
    }
};

// function to validate service provider name already exist
async function validateServiceProviderNameExist(organizationDb,serviceProviderName,isEditAction='',serviceProviderId=''){
    try{
        let subQuery;
        subQuery=organizationDb
        .count('Service_Provider_Name as count')
        .from(ehrTables.serviceProvider)
        .where('Service_Provider_Name',serviceProviderName)

        if(isEditAction){
            subQuery=subQuery
            .whereNot('Service_Provider_Id',serviceProviderId)
        }
        return(
            subQuery
            .then(isNameExists => {
                if(isNameExists[0].count>0){
                    throw 'IVE0006';
                }
                else{
                    return 'success'
                }
            })
            .catch(function (error) {
                console.log('Error in validateServiceProviderNameExist function .catch block',error);
                throw  (error==='IVE0006')?'IVE0006':'SGE0108';
            })
        )
    }
    catch(error){
        console.log('Error in validateServiceProviderNameExist function .catch block',error);
        throw  (error==='IVE0006')?'IVE0006':'SGE0108';
    }
}

//Get the employee list for the alerts based on the alert settings
async function getAlertCondition(orgDb, query, alertName, todayDate, isEmailNotification=0){
    try{
        //Require moment package
        const moment = require('moment-timezone');

        /** get the number of days & before/after(period) for award alert**/
        let alertSettingsRow = await orgDb.select('AS.No_Of_Days','AS.Period')
                                .from(ehrTables.alertSettings + ' as AS')
                                .innerJoin(ehrTables.alertTypes + ' as AT', 'AT.Alert_Type_Id', 'AS.Alert_Type_Id')
                                .where('AT.Alert_Type', alertName)
                                .then((res)=>{
                                    return res[0] ? res[0] : null;
                                });

        if(alertSettingsRow && Object.keys(alertSettingsRow).length > 0)
        {
            /* based on the alert period, alert date will be calculated. 
            * For ex, for alert period before, add number of days to the current date,
            * For alert period after, subtract number of days to the current date
            */
            let alertDate;

            if(alertSettingsRow.Period === 'Before')
            {
                alertDate = moment(todayDate).add(alertSettingsRow.No_Of_Days, 'days').format("YYYY-MM-DD");
            }
            else
            {
                alertDate =  moment(todayDate).subtract(alertSettingsRow.No_Of_Days,'days').format('YYYY-MM-DD');
            }

            // Find the field name in the table based on the alert name
            let fieldLabel;
            switch(alertName)
            {
                case 'Birthday':            fieldLabel = 'DOB'; break;
                case 'New Hires':
                case 'Work Anniversary':    fieldLabel = 'Date_Of_Join'; break;
                case 'Award Announcement':  fieldLabel = 'Award_Date'; break;
                case 'License Expiry':      fieldLabel = 'License_Expiry_Date'; break;
                case 'Passport Expiry':     fieldLabel = 'Expiry_Date'; break;
                case 'Warnings':            fieldLabel = 'Warning_Date'; break;
                case 'Probation End Date':  fieldLabel = 'J.Probation_Date'; break;
                case 'Employees On Notice': fieldLabel = 'Resignation_Date'; break;
                case 'Confirmation Of Employment':  fieldLabel = 'Confirmation_Date'; break;
                case 'Long Leave Notification':  fieldLabel = 'L.End_Date'; break;
                default: return null;
            }

            if(alertName === 'Confirmation Of Employment' ||alertName === 'Award Announcement'|| 
                alertName === 'Probation End Date' || alertName === 'License Expiry' || alertName === 'Passport Expiry' ||
                alertName === 'Warnings' || alertName  === 'Employees On Notice' || alertName  === 'New Hires'
                || alertName === 'Long Leave Notification')
            {
                if(isEmailNotification === 0){
                    if(alertDate <= todayDate)
                    {
                        // If alert period is after
                        query = query.whereRaw(fieldLabel + '>= ?', [alertDate])
                                    .whereRaw(fieldLabel + '<= ?', [todayDate])
                    }
                    else
                    {
                        // If alert period is before
                        query = query.whereRaw(fieldLabel + '<= ?', [alertDate])
                                    .whereRaw(fieldLabel + '>= ?', [todayDate])
                    }
                }else{
                    if(alertDate <= todayDate)
                    {   
                        // If alert period is after
                        query = query.whereRaw(fieldLabel + '= ?', [alertDate])
                    }
                    else
                    {
                        // If alert period is before
                        query = query.whereRaw(fieldLabel + '= ?', [alertDate])
                    }
                }
            }
            else if(alertName === 'Work Anniversary')
            {
                if(alertDate <= todayDate)
                {
                    // If alert period is after
                    query = query.whereRaw("Year(Current_DATE()) > Year("+fieldLabel+")")
                                .whereRaw("CONCAT(Year(Current_DATE()),'-', Date_format("+fieldLabel+",'%m-%d')) >= ?", [alertDate])
                                .whereRaw("CONCAT(Year(Current_DATE()),'-', Date_format("+fieldLabel+", '%m-%d')) <= ?", [todayDate])
                }
                else
                {
                    // If alert period is before
                    query = query.whereRaw("Year(Current_DATE()) > Year("+fieldLabel+")")
                                .whereRaw("CONCAT(Year(Current_DATE()),'-', Date_format("+fieldLabel+",'%m-%d')) <= ?", [alertDate])
                                .whereRaw("CONCAT(Year(Current_DATE()),'-', Date_format("+fieldLabel+", '%m-%d')) >= ?", [todayDate])
                }
            }
            else
            {//If the alertName is Birthday
                if(alertDate <= todayDate)
                {
                    // If alert period is after
                    query = query.whereRaw("CONCAT(Year(Current_DATE()),'-', Date_format("+fieldLabel+",'%m-%d')) >= ?", [alertDate])
                                .whereRaw("CONCAT(Year(Current_DATE()),'-', Date_format("+fieldLabel+", '%m-%d')) <= ?", [todayDate])
                }
                else
                {
                    // If alert period is after
                    query = query.whereRaw("CONCAT(Year(Current_DATE()),'-', Date_format("+fieldLabel+",'%m-%d')) <= ?", [alertDate])
                                .whereRaw("CONCAT(Year(Current_DATE()),'-', Date_format("+fieldLabel+", '%m-%d')) >= ?", [todayDate])
                }
            }
            query = query.whereRaw(fieldLabel + ' != ?', ['0000-00-00'])
            return await query.select();
        }
        else
        {
            return null;
        }
    }catch(mainCatchError){
        console.log('Error in the getAlertCondition() function main catch block.',mainCatchError);
        throw mainCatchError;
    }
}

//Function to get the probation employees list
async function getProbationEmployeesList(organizationDbConnection,isTeamDashboard,loginEmployeeId,isEmailNotification=0,todayDate){
    try{
        let selectFields = ['J.Employee_Id as employeeId',
        organizationDbConnection.raw('CONCAT_WS(" ",P.Emp_First_Name, P.Emp_Middle_Name, P.Emp_Last_Name) as employeeName'),
        'J.Date_Of_Join as dateOfJoin','J.Probation_Date as probationDate','D.Probation_Days as probationDays',
        organizationDbConnection.raw('CASE WHEN J.User_Defined_EmpId IS NOT NULL THEN J.User_Defined_EmpId ELSE J.Employee_Id END as userDefinedEmployeeId')];
        let emailSelectFields = [];

        //If the email has to be sent, get the additional fields
        if(isEmailNotification === 1){
            emailSelectFields = [organizationDbConnection.raw('DATE_FORMAT(J.Probation_Date, "%D %M %Y") as displayProbationDate'),
            organizationDbConnection.raw('CONCAT_WS(" ",MPI.Emp_First_Name, MPI.Emp_Middle_Name, MPI.Emp_Last_Name) as managerName'),
            'EJ.Emp_Email as managerEmailAddress'];//Get the manager email address
            selectFields = selectFields.concat(emailSelectFields);
        }
        let getProbationEmpQuery = organizationDbConnection.select(selectFields)
        .from(ehrTables.empJob + ' as J')
        .innerJoin(ehrTables.empPersonalInfo + ' as P', 'P.Employee_Id', 'J.Employee_Id')
        .innerJoin(ehrTables.designation + ' as D', 'D.Designation_Id', 'J.Designation_Id');
        //Join to get manager email address
        if(isEmailNotification === 1){
            getProbationEmpQuery = getProbationEmpQuery
                                    .leftJoin(ehrTables.empPersonalInfo + ' as MPI', 'J.Manager_Id', 'MPI.Employee_Id')
                                    .leftJoin(ehrTables.empJob + ' as EJ', 'J.Manager_Id', 'EJ.Employee_Id');
        }
        getProbationEmpQuery = getProbationEmpQuery
        .where('P.Form_Status', 1)
        .where('J.Emp_Status', 'Active')
        .where(qb =>{
            qb.where('J.Confirmed', '!=', 1)
        })
        .where(qb1 =>{
            qb1.where('J.Confirmation_Date', '0000-00-00')
            .orWhereNull('J.Confirmation_Date')
        })
        .orderBy('J.Probation_Date', 'asc');

        if(isTeamDashboard === 1){
            getProbationEmpQuery = getProbationEmpQuery
            .where(qb2 =>{
                qb2.where('J.Manager_Id',loginEmployeeId)
                .orWhere('J.Employee_Id',loginEmployeeId)
            });
        }
        //Call the function to get the probation employee details based on the alert settings
        let probationEmployeesDetails = await getAlertCondition(organizationDbConnection, getProbationEmpQuery, 'Probation End Date', todayDate, isEmailNotification);
        return probationEmployeesDetails;
    }catch(listCatchError){
        console.log('Error in the getProbationEmployeesList() function main catch block.',listCatchError);
        throw listCatchError;
    }
}

//Function to get organization details based on the notification name
async function getBatchNotificationDetails(appManagerDbConnection,notificationName,keyName,emailNotificationStatus){
    try{
        console.log('Inside getBatchNotificationDetails function');
        return(
            appManagerDbConnection(appManagerTables.batchEmailNotification+' as BN')
            .select('BN.Org_Code','HRU.Data_Region')
            .leftJoin(appManagerTables.hrappRegisteruser+' as HRU','BN.Org_Code','HRU.Org_Code')
            .where('Notification_For',notificationName)
            .where(keyName,emailNotificationStatus)
            .then((notificationOrgDetails)=>{
                if(notificationOrgDetails && notificationOrgDetails.length > 0){
                    return notificationOrgDetails;
                }else{
                    return [];
                }
            })
            .catch(catchError => {
                console.log('Error in getBatchNotificationDetails function .catch block',catchError);
                throw catchError;
            })
        );
    }
    catch(mainCatchError){
        console.log('Error in getBatchNotificationDetails function main catch block.', mainCatchError);
        throw mainCatchError;
    }
};

//Function to update the notification final status for that organization code
async function updateOrgBatchNotificationStatus(appManagerDbConnection,orgCode,keyName,status,notificationName){
    try{
        console.log('Inside updateOrgBatchNotificationStatus function.');
        //Require moment package
        const moment=require('moment');
        //Update details
        let updateParams ={
            Updated_On: moment().tz('Asia/Kolkata').format('YYYY-MM-DD HH:mm:ss')
        };
        updateParams[keyName] = status;
        return(
            appManagerDbConnection(appManagerTables.batchEmailNotification)
            .update(updateParams)
            .where('Org_Code',orgCode)
            .where('Notification_For',notificationName)
            .then(()=>{
                return true;
            })
            .catch(catchError => {
                console.log('Error in updateOrgBatchNotificationStatus function .catch block',catchError);
                throw catchError;
            })                
        );
    }
    catch(mainCatchError){
        console.log('Error in updateOrgBatchNotificationStatus function main catch block.', mainCatchError);
        throw mainCatchError;
    }
}

//Group the inputArray by using the key
async function groupInputsByKey(inputArray,keyName){
    try{
        return inputArray.reduce((tempDetails, currItem) => {
            const groupKey = currItem[keyName];
            if (!tempDetails[groupKey]) {
                tempDetails[groupKey] = [currItem];
            } else {
                tempDetails[groupKey].push(currItem);
            }
            return tempDetails;
        }, {});
    }catch(mainCatchError){
        console.log('Error in the groupInputsByKey() function.',mainCatchError);
        throw mainCatchError;
    }
}

async function formHTMLDesign(action,inputArray){
    try{
        let htmlTableDesign = '';
        if(action === 'probationemailtohr'){
            let probationRowDesign = '';
            let managerName = '';
            for(let g=0;g<inputArray.length;g++){
                employeeDetails = inputArray[g];
                managerName = employeeDetails.managerName ? employeeDetails.managerName : '-';

                probationRowDesign += `
                <tr style="border-top:1px solid #e2e7ee">
                    <td style="font-weight: bold;">${employeeDetails.userDefinedEmployeeId}</td>
                    <td style="font-weight: bold;">${employeeDetails.employeeName}</td>
                    <td style="font-weight: bold;">${managerName}</td>
                </tr>
                `;
            }
            //Form final html for table design
            htmlTableDesign = `
            <table cellpadding="10" cellspacing="0" style="border-collapse:collapse;color:#260029;font-size:12px" width="95%">
            <tbody>
                <tr>
                    <td></td>
                </tr>
                <tr align="left" style="border-top:1px solid #e2e7ee">
                    <th style="color: #595C68;">ID</th>
                    <th style="color: #595C68;">Name</th>
                    <th style="color: #595C68;">Manager</th>
                </tr>
                ${probationRowDesign}
            </tbody>
            </table>
            `;
        }else if(action === 'probationemailtomanager'){
            let probationRowDesign = '';
            for(let g=0;g<inputArray.length;g++){
                employeeDetails = inputArray[g];
                probationRowDesign += `
                <tr style="border-top:1px solid #e2e7ee">
                    <td style="font-weight: bold;">${employeeDetails.userDefinedEmployeeId}</td>
                    <td style="font-weight: bold;">${employeeDetails.employeeName}</td>
                </tr>
                `;
            }
            //Form final html for table design
            htmlTableDesign = `
            <table cellpadding="10" cellspacing="0" style="border-collapse:collapse;color:#260029;font-size:12px" width="95%">
            <tbody>
                <tr>
                    <td></td>
                </tr>
                <tr align="left" style="border-top:1px solid #e2e7ee">
                    <th style="color: #595C68;">ID</th>
                    <th style="color: #595C68;">Name</th>
                </tr>
                ${probationRowDesign}
            </tbody>
            </table>
            `;
        }else if(action === 'emponnoticeemailtomanager'){
            let empOnNoticeDesign = '',
            employeeDetails = '';
            for(let g=0;g<inputArray.length;g++){
                employeeDetails = inputArray[g];
                empOnNoticeDesign += `
                <tr style="border-top:1px solid #e2e7ee">
                    <td style="font-weight: bold;">${employeeDetails.userDefinedEmployeeId}</td>
                    <td style="font-weight: bold;">${employeeDetails.employeeName}</td>
                </tr>
                `;
            }
            //Form final html for table design
            htmlTableDesign = `
            <table cellpadding="10" cellspacing="0" style="border-collapse:collapse;color:#260029;font-size:12px" width="95%">
            <tbody>
                <tr>
                    <td></td>
                </tr>
                <tr align="left" style="border-top:1px solid #e2e7ee">
                    <th style="color: #595C68;">ID</th>
                    <th style="color: #595C68;">Name</th>
                </tr>
                ${empOnNoticeDesign}
            </tbody>
            </table>
            `;
        }else if(action === 'emponnoticeemailtohr'){
            let empOnNoticeDesign = '',
            employeeDetails = '';
            for(let g=0;g<inputArray.length;g++){
                employeeDetails = inputArray[g];
                let assigneeName = employeeDetails.assigneeName ? employeeDetails.assigneeName : (employeeDetails.groupName ? employeeDetails.groupName : '-');
                empOnNoticeDesign += `
                <tr align="center" style="border-top:1px solid #e2e7ee">
                    <td style="font-weight: bold;">${employeeDetails.userDefinedEmployeeId}</td>
                    <td style="font-weight: bold;">${employeeDetails.employeeName}</td>
                    <td style="font-weight: bold;">${assigneeName}</td>
                </tr>
                `;
            }
            //Form final html for table design
            htmlTableDesign = `
            <table cellpadding="10" cellspacing="0" style="border-collapse:collapse;color:#260029;font-size:12px" width="95%">
            <tbody>
                <tr>
                    <td></td>
                </tr>
                <tr align="center" style="border-top:1px solid #e2e7ee">
                    <th style="color: #595C68;">ID</th>
                    <th style="color: #595C68;">Name</th>
                    <th style="color: #595C68;">Approver/Group</th>
                </tr>
                ${empOnNoticeDesign}
            </tbody>
            </table>
            `;
        }else if(action === 'longleaveemailtohr'){
            let longLeaveRowDesign = '';
            let managerName = '';
            for(let g=0;g<inputArray.length;g++){
                employeeDetails = inputArray[g];
                managerName = employeeDetails.managerName ? employeeDetails.managerName : '-';

                longLeaveRowDesign += `
                <tr style="border-top:1px solid #e2e7ee">
                    <td style="font-weight: bold;">${employeeDetails.userDefinedEmployeeId}</td>
                    <td style="font-weight: bold;">${employeeDetails.employeeName}</td>
                    <td style="font-weight: bold;">${employeeDetails.leaveName}</td>
                    <td style="font-weight: bold;">${managerName}</td>
                </tr>
                `;
            }
            //Form final html for table design
            htmlTableDesign = `
            <table cellpadding="10" cellspacing="0" style="border-collapse:collapse;color:#260029;font-size:12px" width="95%">
            <tbody>
                <tr>
                    <td></td>
                </tr>
                <tr align="left" style="border-top:1px solid #e2e7ee">
                    <th style="color: #595C68;">ID</th>
                    <th style="color: #595C68;">Name</th>
                    <th style="color: #595C68;">Leave Name</th>
                    <th style="color: #595C68;">Manager</th>
                </tr>
                ${longLeaveRowDesign}
            </tbody>
            </table>
            `;
        }else if(action === 'longleaveemailtomanager'){
            let longLeaveTable = '',
            employeeDetails = '';
            for(let g=0;g<inputArray.length;g++){
                employeeDetails = inputArray[g];
                longLeaveTable += `
                <tr style="border-top:1px solid #e2e7ee">
                    <td style="font-weight: bold;">${employeeDetails.userDefinedEmployeeId}</td>
                    <td style="font-weight: bold;">${employeeDetails.employeeName}</td>
                    <td style="font-weight: bold;">${employeeDetails.leaveName}</td>
                </tr>
                `;
            }
            //Form final html for table design
            htmlTableDesign = `
            <table cellpadding="10" cellspacing="0" style="border-collapse:collapse;color:#260029;font-size:12px" width="95%">
            <tbody>
                <tr>
                    <td></td>
                </tr>
                <tr align="left" style="border-top:1px solid #e2e7ee">
                    <th style="color: #595C68;">ID</th>
                    <th style="color: #595C68;">Name</th>
                    <th style="color: #595C68;">Leave Name</th>
                </tr>
                ${longLeaveTable}
            </tbody>
            </table>
            `;
        }
        return htmlTableDesign;
    }catch(catchError){
        console.log('Error in the groupInputsByKey() function.',catchError);
        throw catchError;
    }
}

//Get the HR group email
async function getHrGroupEmployeeEmail(organizationDbConnection){
    try{
        return(
            organizationDbConnection
            .pluck('Emp_Email')
            .from(ehrTables.empJob)
            .innerJoin('hr_group', 'HR_Group_Id', 'Employee_Id')
            .where('Emp_Status', 'Active')
            .whereNot('Emp_Email','')
            .whereNotNull('Emp_Email')
            .then((hrEmailResult)=>{
                return (hrEmailResult && hrEmailResult.length > 0) ? hrEmailResult : [];
            })
            .catch(catchError => {
                console.log('Error in getHrGroupEmployeeEmail function .catch block',catchError);
                throw catchError;
            })                
        );
    }
    catch(mainCatchError){
        console.log('Error in getHrGroupEmployeeEmail function main catch block.', mainCatchError);
        throw mainCatchError;
    }
}

//Get the pending resignation approval employees list
async function getEmployeesOnNoticeForEmail(organizationDbConnection, todayDate)
{
    try
    {
        let selectFields = ['RP.Employee_Id as employeeId','CG.Group_Name as groupName','UTJ.Emp_Email as assigneeEmailAddress',
        organizationDbConnection.raw('CASE WHEN RJ.User_Defined_EmpId IS NOT NULL THEN RJ.User_Defined_EmpId ELSE RJ.Employee_Id END as userDefinedEmployeeId'),
        organizationDbConnection.raw('CONCAT_WS(" ",RP.Emp_First_Name, RP.Emp_Middle_Name, RP.Emp_Last_Name) as employeeName'),
        organizationDbConnection.raw('CONCAT_WS(" ",UTP.Emp_First_Name, UTP.Emp_Middle_Name, UTP.Emp_Last_Name) as assigneeName'),
        organizationDbConnection.raw('DATE_FORMAT(R.Resignation_Date, "%D %M %Y") as displayResignationDate')];
        
        let query = organizationDbConnection.select(selectFields)
                    .from(ehrTables.resignation + ' as R')
                    .innerJoin(ehrTables.taUserTask + ' as UT', 'R.Workflow_Instance_Id', 'UT.process_instance_id')
                    .leftJoin(ehrTables.empPersonalInfo + ' as UTP', 'UT.assignee', 'UTP.Employee_Id')
                    .leftJoin(ehrTables.empJob + ' as UTJ', 'UT.assignee', 'UTJ.Employee_Id')
                    .leftJoin(ehrTables.cusEmpGroup + ' as CG', 'UT.custom_group_id', 'CG.Group_Id')
                    .innerJoin(ehrTables.empPersonalInfo + ' as RP', 'R.Employee_Id', 'RP.Employee_Id')
                    .innerJoin(ehrTables.empJob + ' as RJ', 'R.Employee_Id', 'RJ.Employee_Id')
                    .where('RP.Form_Status', 1)
                    .where('RJ.Emp_Status', 'Active')
                    .where('R.Approval_Status', 'Applied')
                    .orderBy('R.Employee_Id', 'asc');

        let employeesOnNoticeDetails = await getAlertCondition(organizationDbConnection, query, 'Employees On Notice', todayDate,1);
        return employeesOnNoticeDetails;
    }
    catch(catchError){
        console.log("Error in the getEmployeesOnNoticeForEmail function main catch block.", catchError);
        throw catchError;
    }
}

//Function to get the employee details for long leave notification
async function getLongLeaveEmployeeDetails(organizationDbConnection,leaveTypeIds,todayDate,longLeaveTotalDays,isEmailNotification){
    try{
        let selectFields = ['J.Employee_Id as employeeId','LT.Leave_Name as leaveName',
        organizationDbConnection.raw('CONCAT_WS(" ",P.Emp_First_Name, P.Emp_Middle_Name, P.Emp_Last_Name) as employeeName'),
        'L.End_Date as endDate',organizationDbConnection.raw('CASE WHEN J.User_Defined_EmpId IS NOT NULL THEN J.User_Defined_EmpId ELSE J.Employee_Id END as userDefinedEmployeeId')];
        let emailSelectFields = [];

        //If the email has to be sent, get the additional fields
        if(isEmailNotification === 1){
            emailSelectFields = [organizationDbConnection.raw('DATE_FORMAT(L.End_Date, "%D %M %Y") as displayLeaveEndDate'),
            organizationDbConnection.raw('CONCAT_WS(" ",MPI.Emp_First_Name, MPI.Emp_Middle_Name, MPI.Emp_Last_Name) as managerName'),
            'EJ.Emp_Email as managerEmailAddress'];//Get the manager email address
            selectFields = selectFields.concat(emailSelectFields);
        }

        let longLeaveEmpQuery = organizationDbConnection.select(selectFields)
        .from(ehrTables.empLeaves + ' as L')
        .innerJoin(ehrTables.leavetype + ' as LT', 'L.LeaveType_Id', 'LT.LeaveType_Id')
        .innerJoin(ehrTables.empPersonalInfo + ' as P', 'L.Employee_Id', 'P.Employee_Id')
        .innerJoin(ehrTables.empJob + ' as J', 'L.Employee_Id', 'J.Employee_Id');

        //Join to get manager email address
        if(isEmailNotification === 1){
            longLeaveEmpQuery = longLeaveEmpQuery
                                    .leftJoin(ehrTables.empPersonalInfo + ' as MPI', 'J.Manager_Id', 'MPI.Employee_Id')
                                    .leftJoin(ehrTables.empJob + ' as EJ', 'J.Manager_Id', 'EJ.Employee_Id');
        }
        longLeaveEmpQuery = longLeaveEmpQuery
        .whereIn('L.LeaveType_Id',leaveTypeIds)
        .where('L.Total_Days','>',longLeaveTotalDays)
        .where('P.Form_Status', 1)
        .where('J.Emp_Status', 'Active');

        //Call the function to get the long leave employee details based on the alert settings
        let employeesDetails = await getAlertCondition(organizationDbConnection, longLeaveEmpQuery, 'Long Leave Notification', todayDate, isEmailNotification);
        return employeesDetails;
    }catch(getEmployeeMainCatchError){
        console.log('Error in the getLongLeaveEmployeeDetails function main catch block.',getEmployeeMainCatchError);
        throw getEmployeeMainCatchError;
    }
}

// function to get the employeeId based on report access rights
async function checkAccessGetEmployeeIdsForReports(organizationDbConnection,logInEmpId,employeeIdsArrayFromFilter,isAdmin,isManager){
    try{
        let employeeIdsArray = [];
        if(employeeIdsArrayFromFilter.length===0)
        {
            if (isAdmin) {
                employeeIdsArray = await commonLib.func.getEmployeeIdBasedOnRoles(organizationDbConnection, logInEmpId, 1);
            }
            else if(isManager)
            {
                employeeIdsArray = await commonLib.func.getManagerHierarchy(organizationDbConnection, logInEmpId, 0); 
            }
            else{
                employeeIdsArray.push(logInEmpId)  
            }  
        }
        else{
            employeeIdsArray=employeeIdsArrayFromFilter;
        }
        return { employeeIdsArray };
    }
    catch (catchError) {
        console.log('Error in checkAccessGetEmployeeIdsForReports function main catch block', catchError);
        throw  catchError; 
    }    
}
async function fetchJobRoleDetails(jobInfoDetails, organizationDbConnection) {
    try {
        // Get all unique job role IDs
        const jobRoleIds = Array.from(
            new Set(
                jobInfoDetails.flatMap(
                    ({ Job_Role_Ids }) => JSON.parse(Job_Role_Ids || '[]')
                )
            )
        );

        // Fetch all job roles
        const jobRoles = jobRoleIds.length ? await organizationDbConnection('job_roles'+ " as JR")
            .select(
                organizationDbConnection.raw(`
                    CASE WHEN JR.Job_Role_Code IS NOT NULL AND TRIM(JR.Job_Role_Code) != '' 
                    THEN CONCAT(JR.Job_Role_Code, ' - ', JR.Job_Role) 
                    ELSE JR.Job_Role END AS Job_Role_Name
                `),
                'JR.Job_Role_Id'
            )
            .whereIn('JR.Job_Role_Id', jobRoleIds) : [];

        // Create lookup object
        const jobRolesMap = Object.fromEntries(
            jobRoles.map(role => [role.Job_Role_Id, role])
        );

        // Map results with job roles
        return jobInfoDetails.map(record => ({
            ...record,
            Job_Role_Ids: JSON.parse(record.Job_Role_Ids || '[]'),
            Job_Role_Details: JSON.parse(record.Job_Role_Ids || '[]')
                .map(id => jobRolesMap[id])
                .filter(Boolean)
        }));
        
    } catch (e) {
        console.log('Error in fetchJobRoleDetails:', e);
        throw e;
    }
}
exports.insertSystemLogs=insertSystemLogs;
exports.validateServiceProviderNameExist=validateServiceProviderNameExist;
exports.getAlertCondition = getAlertCondition;
exports.getProbationEmployeesList=getProbationEmployeesList;
exports.getBatchNotificationDetails = getBatchNotificationDetails;
exports.updateOrgBatchNotificationStatus = updateOrgBatchNotificationStatus;
exports.groupInputsByKey = groupInputsByKey;
exports.formHTMLDesign=formHTMLDesign;
exports.getHrGroupEmployeeEmail=getHrGroupEmployeeEmail;
exports.getEmployeesOnNoticeForEmail=getEmployeesOnNoticeForEmail;
exports.getLongLeaveEmployeeDetails=getLongLeaveEmployeeDetails;
exports.checkAccessGetEmployeeIdsForReports=checkAccessGetEmployeeIdsForReports;
exports.validateCommonRuleInput = validateCommonRuleInput;
exports.fetchJobRoleDetails = fetchJobRoleDetails;