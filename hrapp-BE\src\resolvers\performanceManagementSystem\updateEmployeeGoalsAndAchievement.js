// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex package
const knex = require('knex');
// require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require common constant files
const { formName,systemLogs } = require('../../../common/appconstants');
// require table alias
const { ehrTables } = require('../../../common/tablealias');
// require validation file
const {updateEmployeeGoalsAndAchievementInputValidation,employeeGoalsAndRatingInputValidation} = require('../../../common/performanceManagementValidation');
// require common function file
const {updateGoalsAndAchievement} = require('../../../common/performanceManagementCommonFunction');


// variable declarations
let errResult = {};
let organizationDbConnection = '';
let validationError={};
let assessmentId;
let currentReviewerId;

// resolver definition
const resolvers = {
    Mutation:{
        // function to update performance goals and achievements
        updateEmployeeGoalsAndAchievement: async(parent, args, context, info) =>{
            try{
                console.log('Inside updateEmployeeGoalsAndAchievement function',args);
                // get database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                // variable declarations
                let logInEmpId = context.Employee_Id;
                let userIp= context.User_Ip;
                // check whether the employee has edit access to goalsAndAchievement form or not & loggedIn employee should be either admin or manager
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.goalsAndAchievement, '', 'UI');
                // check whether loggedIn employee is manager/admin
                if ((Object.keys(checkRights).length >0 && checkRights.Role_Update===1) && (checkRights.Is_Manager===1 || checkRights.Employee_Role==='admin')) {
                    // function to validate inputs            
                    validationError=await employeeGoalsAndRatingInputValidation(args,'validateMonthYearEmployee');
                    if(Object.keys(validationError).length ===0){
                        let isAdmin=(checkRights.Employee_Role==='admin')?1:0;
                        // function to check the current status of the record based on which we will validate the inputs
                        let updateAction=await getGoalsAndRatingStatus(organizationDbConnection,args);
                        // function to validate inputs based on current record status
                        validationError=await updateEmployeeGoalsAndAchievementInputValidation(args,updateAction,isAdmin,logInEmpId);
                        if(Object.keys(validationError).length ===0){
                            // get the employee timezone based on location
                            let empCurrentTime = await commonLib.func.getEmployeeTimeZone(logInEmpId, organizationDbConnection, 1);
                            // based on the action update either goals/ratings
                            if(updateAction==='updateGoals'){
                                // function to update the new goals assigned to the employee
                                await updateEmployeeGoals(organizationDbConnection,args,assessmentId,logInEmpId,empCurrentTime);
                                // if loggedIn employee is admin and reviewerId does not match then update it.
                                if(isAdmin && (currentReviewerId!==args.reviewerId)){
                                    await updateGoalsAndAchievement(organizationDbConnection,assessmentId,args,empCurrentTime,logInEmpId,'updateReviewer');
                                }else{
                                    //Update updated on and updated by
                                    await updateGoalsAndAchievement(organizationDbConnection,assessmentId,args,empCurrentTime,logInEmpId,'updateUpdatedOnDetails');
                                }
                            }
                            else{
                                // function to update the ratings for the assigned goals
                                await updateEmployeeRatings(organizationDbConnection,args.goalsAndRating,assessmentId,logInEmpId,empCurrentTime);
                                // function to update the status and overall ratings in performance goal achievement
                                await updateGoalsAndAchievement(organizationDbConnection,assessmentId,args,empCurrentTime,logInEmpId,'updateStatus');
                            }
                            // update system logs
                            await includeSystemLogs(organizationDbConnection,args.employeeId,userIp,logInEmpId,updateAction);
                            // destroy database connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode:'',message:'Employee goals and achievements updated successfully.'};
                        }
                        else{
                            throw 'IVE0000';
                        }
                    }
                    else{
                        throw 'IVE0000';
                    }
                }
                else{
                    throw '_DB0102'; // when user does not have edit access
                }
            }catch(mainCatchError){
                console.log('Error in updateEmployeeGoalsAndAchievement function main catch block',mainCatchError);
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                if (mainCatchError === 'IVE0000') {
                    errResult = commonLib.func.getError('', 'IVE0000');
                    console.log('Validation error in updateEmployeeGoalsAndAchievement function - ',validationError);
                    // return response
                    throw new UserInputError(errResult.message, { validationError: validationError });
                } else {
                    errResult = commonLib.func.getError(mainCatchError, 'EPM0011');
                    // return response
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        }
    }
};
exports.resolvers = resolvers;

// function to get status for the input month/year
async function getGoalsAndRatingStatus(organizationDbConnection,args){
    try{
        return(
            // get the assessment id,status for the input month/year and employee id
            organizationDbConnection(ehrTables.performanceGoalAchievement)
            .select('Performance_Assessment_Id as assessmentId','Assessment_Status as assessmentStatus','Goal_Publish_Status as goalPublishStatus','Rating_Publish_Status as ratingPublishStatus','Reviewer_Employee_Id as reviewerId')
            .where('Performance_Assessment_Month',args.month)
            .where('Performance_Assessment_Year',args.year)
            .where('Employee_Id',args.employeeId)
            .then(getData=>{
                // check whether data exist or not
                if(getData.length>0){
                    // if current status is 'goal assigned' and if it is not published then user can update the goals and reviewers
                    if(getData[0].assessmentStatus==='Goals Assigned' && getData[0].goalPublishStatus==='Unpublished'){
                        currentReviewerId=getData[0].reviewerId;
                        assessmentId=getData[0].assessmentId;
                        return 'updateGoals';
                    }
                    // if current status is 'goal assigned' and if it is published then user can update the ratings
                    else if(getData[0].assessmentStatus==='Goals Assigned' && getData[0].goalPublishStatus==='Published'){
                        assessmentId=getData[0].assessmentId;
                        return 'addRatings';
                    }
                    // if current status is 'rated' and if it is not published then user can update the ratings
                    else if(getData[0].assessmentStatus==='Rated' && getData[0].ratingPublishStatus==='Unpublished'){
                        assessmentId=getData[0].assessmentId;
                        return 'updateRatings';
                    }
                    // if current status is 'rated' and if it is published,then user cannot edit the details
                    else if(getData[0].assessmentStatus==='Rated' && getData[0].ratingPublishStatus==='Published'){
                        throw 'EPM0112';
                    }
                    else{
                        console.log('Record found with status as '+getData[0].assessmentStatus+ 'goal publish status as '+getData[0].goalPublishStatus+ 'rating publish status as '+getData[0].ratingPublishStatus);
                        throw 'EPM0113';
                    }
                }
                else{
                    console.log('No goals and achievement record found for the employee.');
                    throw '_EC0001';
                }
            })
            .catch(catchError => {
                console.log('Error in getGoalsAndRatingStatus function .catch block',catchError);
                throw catchError;
            })
        );
    }
    catch(error){
        console.log('Error in getGoalsAndRatingStatus function main catch block',error);
        throw error;
    }
};

// function to update goals for the employee
async function updateEmployeeGoals(organizationDbConnection,args,assessmentId,logInEmpId,empCurrentTime){
    try{
        if(assessmentId){
            return (
                organizationDbConnection
                .transaction(function (trx) {
                    // delete the previous goal assigned to the employee and insert the new goals
                    return(
                        organizationDbConnection(ehrTables.assessmentCycleGoalRatings)
                        .delete()
                        .where('Performance_Assessment_Id',assessmentId)
                        .transacting(trx)
                        .then(deleteRecord => {
                            console.log('Goals corressponding to the assessment id deleted successfully',deleteRecord);
                            let goalData=args.goalIdArray;
                            // form params to update new goals
                            let insertEmployeeGoals = goalData.map(field => ({
                                Performance_Assessment_Id:assessmentId,
                                Goal_Id: field,
                                Added_On: empCurrentTime,
                                Added_By: logInEmpId
                            }));
                            return(
                                organizationDbConnection(ehrTables.assessmentCycleGoalRatings)
                                .insert(insertEmployeeGoals)
                                .transacting(trx)
                                .then(insertGoals => {
                                    console.log('Goals inserted successfully',insertGoals);
                                    return 'success';
                                })
                            )        
                        })
                    )
                })
                .then(result => {
                    return result;
                })
                .catch(catchError => {
                    console.log("Error in updateEmployeeGoals function .catch block", catchError);
                    throw 'EPM0113';
                })
            );
        }
        else{
            console.log('Performance assessment id does not exist.');
            throw '_EC0001';
        }
    }
    catch(error){
        console.log('Error in updateEmployeeGoals function main catch block.',error);
        throw 'EPM0113';
    }
};

// function to update ratings for the input employeess
async function updateEmployeeRatings(organizationDbConnection,goalRatings,assessmentId,logInEmpId,empCurrentTime){
    try{
        const queries = [];
        return (
            organizationDbConnection
            .transaction(function (trx) {
                // update the ratings for the corressponding goalId
                goalRatings.forEach(data => {
                    const query=organizationDbConnection(ehrTables.assessmentCycleGoalRatings)
                    .update({
                        Rating:data.rating,
                        Updated_On:empCurrentTime,
                        Updated_By:logInEmpId
                    })
                    .where('Goal_Id',data.goalId)
                    .where('Performance_Assessment_Id',assessmentId)
                    .transacting(trx); // This makes every update be in the same transaction
                    queries.push(query);
                });
                Promise.all(queries) // Once every query is written
                    .then(trx.commit) // We try to execute all of them
                    .catch(trx.rollback); // And rollback in case any of them goes wrong
            })
        );
    }
    catch(error){
        console.log('Error in updateEmployeeRatings function main catch block',error);
        throw 'EPM0113'; 
    }
};

// function to insert systemlogs
async function includeSystemLogs(organizationDbConnection,employeeId,userIp,logInEmpId,action){
    try{
        // form params to update system logs
        let systemLogParams = {
            action: systemLogs.roleUpdate,
            userIp: userIp,
            employeeId: logInEmpId,
            formName: formName.goalsAndAchievement,
            trackingColumn: (action==='updateGoals')?'- Performance goals':'- Performance ratings',
            organizationDbConnection: organizationDbConnection,
            uniqueId: employeeId
        };
        // call function to update system log activities
        await commonLib.func.createSystemLogActivities(systemLogParams);
    }
    catch(error){
        console.log('Error in includeSystemLogs function catch block',error);
        return '';
    }
}