const { ehrTables } = require('../../../common/tablealias');
// require moment package
let moment = require('moment-timezone');
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { formName, roles } = require('../../../common/appconstants');
const knex = require('knex');
// require common function
const {getAlertCondition} = require('../../../common/remindersCommonFunctions');

module.exports.getReminders  = async(parent, args, context, info) => {
    // get the organization data base connection
    let orgDb = knex(context.connection.OrganizationDb);
    try
    {
        let orgCode=context.Org_Code;
        let payslipMonth=1;
        // variable declarations
        let showLeaveClosureWarningPopup=0;
        let leaveClosure={};

        let minLeaveClosureStarAndEndDate=await getMinLeaveClosureEndDate(orgDb);

        let logInEmpId = context.Employee_Id;

        let timezone = await commonLib.func.getEmployeeTimeZone(logInEmpId, orgDb, 0);

        let todayDate = moment().tz(timezone).format('YYYY-MM-DD');

        let birthday = await getBirthdays(orgDb, todayDate, context.Org_Code);

        let workAnniversary = await getDOJAnniversary(orgDb, todayDate, context.Org_Code);

        let awards = await getAwards(orgDb, todayDate, context.Org_Code);
         
        let leaveClosureStartDate=minLeaveClosureStarAndEndDate.minLeaveClosureStartDate;

        let leaveClosureEndDate=minLeaveClosureStarAndEndDate.minLeaveClosureEndDate;

        // check date exist or not
        if(leaveClosureStartDate && leaveClosureEndDate){
            // get the current date
            let currentDate=moment().format('YYYY-MM-DD');
            // check whether current date is greater than leave closure end date
            if(currentDate>leaveClosureEndDate)
            {
                showLeaveClosureWarningPopup=1;
            }
            else{
                showLeaveClosureWarningPopup=0;
            }
        }
        // if leave closure date is not updated then no need to present leave closure popup
        else{
            console.log('Leave closure date is not updated');
        }
        // form response
        leaveClosure={leaveClosureStartDate,leaveClosureEndDate,showLeaveClosureWarningPopup};
        // return response to UI
        return {
            errorCode: '',
            message: 'Reminders retrieved successfully',
            reminders: {
                birthday : (birthday && birthday[0]) ? JSON.stringify(birthday) : null,
                workAnniversary : (workAnniversary && workAnniversary[0]) ? JSON.stringify(workAnniversary) : null,
                awards : (awards && awards[0]) ? JSON.stringify(awards) : null,
                leaveClosure:(Object.keys(leaveClosure).length>0)?JSON.stringify(leaveClosure):null
            }
        }
    }
    catch(getReminderErr)
    {
        console.log('Error in getReminder() function main catch block.',getReminderErr);
        errResult = commonLib.func.getError('', 'DB0024');
        throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message}));
    }
    finally
    {
        orgDb.destroy();
    }
}

// Function to get the birthday details based on alert settings
// Returns the array of JSON with Employee_Name and Date_of_Birth as keys
// Date of birth format must be 2 Aug (Day of the month as a numeric value(%e) and Abbreviated month name(%b))
async function getBirthdays(orgDb, todayDate, orgCode)
{
    try
    {
        let query = orgDb.select({
                                    Employee_Name: orgDb.raw('CONCAT_WS(" ",P.Emp_First_Name, P.Emp_Middle_Name, P.Emp_Last_Name)'),
                                    Employee_Id: 'J.Employee_Id',
                                    User_Defined_EmpId: 'J.User_Defined_EmpId',
                                    Designation: 'DES.Designation_Name',
                                    Department: 'DEP.Department_Name',
                                    Employee_First_Name: 'P.Emp_First_Name',
                                    Profile_Pic: 'P.Photo_Path',
                                    Date_of_Birth: orgDb.raw("DATE_FORMAT(P.DOB,'%e %b')")
                                })
                                .from(ehrTables.empPersonalInfo + ' as P')
                                .innerJoin(ehrTables.empJob + ' as J', 'J.Employee_Id', 'P.Employee_Id')
                                .innerJoin(ehrTables.designation+ ' as DES', 'DES.Designation_Id', 'J.Designation_Id')
                                .innerJoin(ehrTables.department+ ' as DEP', 'DEP.Department_Id', 'J.Department_Id')
                                .where('J.Emp_Status', 'Active')
                                .where('P.Form_Status', 1)
                                .orderByRaw("STR_TO_DATE(CONCAT(YEAR(NOW()), '-', DATE_FORMAT(P.DOB, '%m-%d')), '%Y-%m-%d') ASC");

        let birthday = await getAlertCondition(orgDb, query, 'Birthday', todayDate);

        if(birthday.length>0){
            return await getProfilePic(birthday, orgCode);
        }else{
            return [];
        }
    }
    catch(err)
    {
        console.log("Error while retrieving getBirthdays details", err);
        return null;
    }
}

// Function to get the work anniversary details based on alert settings
// Returns the array of JSON with Employee_Name and Date_of_Join as keys
// Date_of_Join format must be 2 Aug 2015 (Day of the month as a numeric value(%e) and Abbreviated month name(%b) and Year(%Y))
async function getDOJAnniversary(orgDb, todayDate, orgCode)
{
    try
    {
        let query = orgDb.select({
                                    Employee_Name: orgDb.raw('CONCAT_WS(" ",P.Emp_First_Name, P.Emp_Middle_Name, P.Emp_Last_Name)'),
                                    Employee_Id: 'J.Employee_Id',
                                    User_Defined_EmpId: 'J.User_Defined_EmpId',
                                    Designation: 'DES.Designation_Name',
                                    Department: 'DEP.Department_Name',
                                    Employee_First_Name: 'P.Emp_First_Name',
                                    Profile_Pic: 'P.Photo_Path',
                                    Date_of_Join: orgDb.raw("DATE_FORMAT(J.Date_Of_Join,'%e %b %Y')")
                                })
                                .from(ehrTables.empPersonalInfo + ' as P')
                                .innerJoin(ehrTables.empJob + ' as J', 'J.Employee_Id', 'P.Employee_Id')
                                .innerJoin(ehrTables.designation+ ' as DES', 'DES.Designation_Id', 'J.Designation_Id')
                                .innerJoin(ehrTables.department+ ' as DEP', 'DEP.Department_Id', 'J.Department_Id')
                                .where('Emp_Status', 'Active')
                                .where('Form_Status', 1)
                                .orderByRaw("STR_TO_DATE(CONCAT(YEAR(NOW()), '-', DATE_FORMAT(J.Date_Of_Join, '%m-%d')), '%Y-%m-%d') ASC");

        let workAnniversary = await getAlertCondition(orgDb, query, 'Work Anniversary', todayDate);

        return await getProfilePic(workAnniversary, orgCode);
    }
    catch(err)
    {
        console.log("Error while retrieving getDOJAnniversary details", err);
        return null;
    }
}

// Function to get the awards details based on alert settings
// Returns the array of JSON with Employee_Name and Award_Name as keys
async function getAwards(orgDb, todayDate, orgCode)
{
    try
    {
        let query = orgDb.select(orgDb.raw('CONCAT_WS(" ",EP.Emp_First_Name, EP.Emp_Middle_Name, EP.Emp_Last_Name) as Employee_Name'),
                                    'AT.Award_Name', 'EP.Emp_First_Name as Employee_First_Name', 'EP.Employee_Id','J.User_Defined_EmpId', 'DES.Designation_Name', 'DEP.Department_Name', 'EP.Photo_Path as Profile_Pic')
                        .from(ehrTables.awards + ' as A')
                        .innerJoin(ehrTables.empPersonalInfo + ' as EP', 'EP.Employee_Id', 'A.AwardTo_Id')
                        .innerJoin(ehrTables.empJob + ' as J', 'J.Employee_Id', 'EP.Employee_Id')
                        .innerJoin(ehrTables.designation+ ' as DES', 'DES.Designation_Id', 'J.Designation_Id')
                        .innerJoin(ehrTables.department+ ' as DEP', 'DEP.Department_Id', 'J.Department_Id')
                        .innerJoin(ehrTables.awardTypes + ' as AT', 'A.AwardType_Id', 'AT.AwardType_Id')

        let awards = await getAlertCondition(orgDb, query, 'Award Announcement', todayDate);

        return await getProfilePic(awards, orgCode);
    }
    catch(err)
    {
        console.log("Error while retrieving getAwards details", err);
        return null;
    }
}

// Function to retrieve the probation, New Hires and offboarding details
module.exports.myTeamUpdates = async (parent, args, context, info) =>{
    // get the organization data base connection
    const orgDb = knex(context.connection.OrganizationDb);
    try
    {
        let logInEmpId = context.Employee_Id;

        let timezone = await commonLib.func.getEmployeeTimeZone(logInEmpId, orgDb, 0);
    
        let todayDate = moment().tz(timezone).format('YYYY-MM-DD');

        let isAdmin = await commonLib.func.checkEmployeeAccessRights(orgDb, logInEmpId, formName.admin, roles.roleUpdate);
        let isEmployeeAdmin = await commonLib.func.checkEmployeeAccessRights(orgDb, logInEmpId, formName.employeeAdmin, roles.roleUpdate);

        let probation = await getProbation(orgDb, todayDate, context.Org_Code, logInEmpId, isAdmin, isEmployeeAdmin);

        let offboarding = await getEmployeesOnNotice(orgDb, todayDate, context.Org_Code, logInEmpId, isAdmin, isEmployeeAdmin);
      
        let newJoining = await getNewHires(orgDb, todayDate, context.Org_Code, logInEmpId, isAdmin, isEmployeeAdmin);

        return {
            errorCode: '',
            message: 'My team updates retrieved successfully',
            myTeamUpdates:{
                probation: (probation && probation[0]) ? JSON.stringify(probation) : null,
                newJoining: (newJoining && newJoining[0]) ? JSON.stringify(newJoining) : null,
                offboarding: (offboarding && offboarding[0]) ? JSON.stringify(offboarding) : null
            }
        }
    }
    catch(err)
    {
        console.log('Error in myTeamUpdates', err);
        let errResult = commonLib.func.getError('', 'DB0026');
        return {
            errorCode: errResult.code,
            message: errResult.message,
            myTeamUpdates: null
        }
    }
    finally
    {
        orgDb.destroy();
    }
}

async function getProbation(orgDb, todayDate, orgCode, logInEmpId, isAdmin, isEmployeeAdmin)
{
    try
    {
        let query = orgDb.select({
                                    Employee_Name: orgDb.raw('CONCAT_WS(" ",P.Emp_First_Name, P.Emp_Middle_Name, P.Emp_Last_Name)'),
                                    Employee_First_Name: 'P.Emp_First_Name',
                                    Profile_Pic: 'P.Photo_Path',
                                    Probation_Date: orgDb.raw("DATE_FORMAT(J.Probation_Date,'%e %b')"),
                                    Date_of_Join: orgDb.raw("DATE_FORMAT(J.Date_Of_Join,'%e %b')")
                                })
                                .from(ehrTables.empJob + ' as J')
                                .innerJoin(ehrTables.empPersonalInfo + ' as P', 'P.Employee_Id', 'J.Employee_Id')
                                .where(qb =>{
                                    qb.where('P.Form_Status', 1)
                                        .where('J.Confirmed', '!=', 1)
                                        .where('J.Emp_Status', 'Active')
                                })
                                .where(qb1 =>{
                                    qb1.where('J.Confirmation_Date', '0000-00-00')
                                        .orWhereNull('J.Confirmation_Date')
                                })
                                .orderBy('J.Probation_Date', 'asc')

        if(!isAdmin && !isEmployeeAdmin)
        {
            let employeeIds = await orgDb.pluck('Employee_Id')
                                        .from(ehrTables.empJob)
                                        .where('Manager_Id',logInEmpId)

            let isManager = await orgDb.select('Is_Manager')
                                        .from(ehrTables.empPersonalInfo)
                                        .where('Employee_Id', logInEmpId)
                                        .first()
                                        .then((res)=>{
                                            return res.Is_Manager;
                                        })
            // If logged In employee is Manager
            if(isManager && employeeIds.length > 0)
            {
                query = query.where(qb2 =>{
                                qb2.where('J.Employee_Id', logInEmpId)
                                    .orWhereIn('J.Employee_Id', employeeIds)
                            })
            }
            else
            {
                // If logged In employee is not Admin or Manager
                query = query.where('J.Employee_Id', logInEmpId);
            }
        }

        let probation = await getAlertCondition(orgDb, query, 'Probation End Date', todayDate);

        return await getProfilePic(probation, orgCode);
    }
    catch(err)
    {
        console.log("Error while retrieving getProbation details", err);
        return null;
    }
}

async function getEmployeesOnNotice(orgDb, todayDate, orgCode, logInEmpId, isAdmin, isEmployeeAdmin, isEmailNotification=0)
{
    try
    {
        let selectFields = [orgDb.raw('CONCAT_WS(" ",P.Emp_First_Name, P.Emp_Middle_Name, P.Emp_Last_Name) as Employee_Name'),
        'P.Emp_First_Name as Employee_First_Name','P.Photo_Path as Profile_Pic',orgDb.raw("DATE_FORMAT(R.Notice_Date,'%e %b') as Notice_Start_Date"),
        orgDb.raw("DATE_FORMAT(R.Resignation_Date,'%e %b') as Resignation_Date")
        ];
        let emailSelectFields = [];

        //If the email has to be sent, get the additional fields
        if(isEmailNotification === 1){
            emailSelectFields = ['P.Employee_Id',orgDb.raw('DATE_FORMAT(R.Resignation_Date, "%D %M %Y") as displayResignationDate'),'EJ.Emp_Email as managerEmailAddress'];//Get the manager email address
            selectFields = selectFields.concat(emailSelectFields);
        }

        let query = orgDb.select(selectFields)
                    .from(ehrTables.resignation + ' as R')
                    .innerJoin(ehrTables.empPersonalInfo + ' as P', 'P.Employee_Id', 'R.Employee_Id')
                    .innerJoin(ehrTables.empJob + ' as J', 'P.Employee_Id', 'J.Employee_Id')
                    .where('P.Form_Status', 1)
                    .where('J.Emp_Status', 'Active')
                    .whereIn('R.Approval_Status', ['Applied','Approved'])
                    .orderBy('R.Resignation_Date', 'asc')

        if(isEmailNotification === 0){
            if(!isAdmin && !isEmployeeAdmin)
            {
                let employeeIds = await orgDb.pluck('Employee_Id')
                                            .from(ehrTables.empJob)
                                            .where('Manager_Id',logInEmpId)

                let isManager = await orgDb.select('Is_Manager')
                                            .from(ehrTables.empPersonalInfo)
                                            .where('Employee_Id', logInEmpId)
                                            .first()
                                            .then((res)=>{
                                                return res.Is_Manager;
                                            })
                // If logged In employee is Manager
                if(isManager && employeeIds.length > 0)
                {
                    query = query.where(qb2 =>{
                                    qb2.where('J.Employee_Id', logInEmpId)
                                        .orWhereIn('J.Employee_Id', employeeIds)
                                })
                }
                else
                {
                    // If logged In employee is not Admin or Manager
                    query = query.where('J.Employee_Id', logInEmpId);
                }
            }
        }else{
            query = query.leftJoin(ehrTables.empJob + ' as EJ', 'J.Manager_Id', 'EJ.Employee_Id');
        }

        let offboarding = await getAlertCondition(orgDb, query, 'Employees On Notice', todayDate,isEmailNotification);

        if(isEmailNotification === 0){
            return await getProfilePic(offboarding, orgCode);
        }else{
            return offboarding;
        }
    }
    catch(err)
    {
        console.log("Error while retrieving getEmployeesOnNotice details", err);
        return null;
    }
}

async function getNewHires(orgDb, todayDate, orgCode, logInEmpId, isAdmin, isEmployeeAdmin)
{
    try
    {
        let query = orgDb.select({
                                    Employee_Name: orgDb.raw('CONCAT_WS(" ",P.Emp_First_Name, P.Emp_Middle_Name, P.Emp_Last_Name)'),
                                    Employee_First_Name: 'P.Emp_First_Name',
                                    Profile_Pic: 'P.Photo_Path',
                                    Date_of_Join: orgDb.raw("DATE_FORMAT(J.Date_Of_Join,'%e %b')")
                                })
                                .from(ehrTables.empJob + ' as J')
                                .innerJoin(ehrTables.empPersonalInfo + ' as P', 'P.Employee_Id', 'J.Employee_Id')
                                .where(qb =>{
                                    qb.where('P.Form_Status', 1)
                                    .where('J.Emp_Status', 'Active')
                                })
                                .orderBy('J.Date_Of_Join', 'asc')

        if(!isAdmin && !isEmployeeAdmin)
        {
            let employeeIds = await orgDb.pluck('Employee_Id')
                                        .from(ehrTables.empJob)
                                        .where('Manager_Id',logInEmpId)

            let isManager = await orgDb.select('Is_Manager')
                                        .from(ehrTables.empPersonalInfo)
                                        .where('Employee_Id', logInEmpId)
                                        .first()
                                        .then((res)=>{
                                            return res.Is_Manager;
                                        })
            // If logged In employee is Manager
            if(isManager && employeeIds.length > 0)
            {
                query = query.where(qb2 =>{
                                qb2.where('J.Employee_Id', logInEmpId)
                                    .orWhereIn('J.Employee_Id', employeeIds)
                            })
            }
            else
            {
                // If logged In employee is not Admin or Manager
                query = query.where('J.Employee_Id', logInEmpId);
            }
        }

        let newJoining = await getAlertCondition(orgDb, query, 'New Hires', todayDate);

        return await getProfilePic(newJoining, orgCode);
    }
    catch(err)
    {
        console.log("Error while retrieving getNewHires details", err);
        return null;
    }
}

//generate presigned url for employee profile pic
async function getProfilePic(data, orgCode)
{
    let count = data.length;
    for(let i=0; i<count; i++)
    {
        if(data[i].Profile_Pic)
        {
            let fileName = await commonLib.func.formS3FilePath(data[i].Profile_Pic, orgCode, 'profile', '', process.env.domainName);

            data[i].Profile_Pic = fileName ? await commonLib.func.getFileURL(process.env.region, process.env.hrappProfileBucket, fileName) : null;
        }
        else
        {
            data[i].Profile_Pic = null;
        }
    }
    return data;
}

async function getMinLeaveClosureEndDate(organizationDbConnection)
{
    try{
        return(
            organizationDbConnection(ehrTables.leavetype + ' as LT')
            .min('LT.Leave_Closure_Start_Date as minLeaveClosureStartDate')
            .min('LT.Leave_Closure_End_Date as minLeaveClosureEndDate')
            .where('LT.Leave_Closure_Based_On','Selected Month')
            .where('LT.Leave_Status', 'Active')
            .then(data=>{
                if(data.length>=0)
                {
                    return data[0];
                }
                return "";
            })
            .catch(e=>{
                console.log('Error in getMinLeaveClosureEndDate .catch block',e);
                return "";
            })
        )
    }
    catch(e)
    {
        console.log('Error in getMinLeaveClosureEndDate main catch block',e);
        return "";
    }
}