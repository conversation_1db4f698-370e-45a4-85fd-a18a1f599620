// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formIds } = require('../../../common/appconstants');
// require common constant files
const { evaluateAndProcessSwap } = require('./statusApprovalCommonFunction');

//Function to validate the input fields
async function validateInputs(args){
    try{
        if(args.swapId){
            return 'success';
        }else{
            handleError('validationError','_EC0007', args);
        }
    }catch(error){
        console.log('Error in validateInputs() function main catch block', error);
        throw error;
    }
}

function handleError(error,errorCode, args){
    let errResult;
    console.log('Inside in evaluateAndProcessShiftSwapStatus handleError function.', error,errorCode,args);
    let customErrorCodes = ['SS0158','SS0160'];
    if(customErrorCodes.includes(errorCode)){
        throw new ApolloError(error, errorCode);
    }else{
        errResult = commonLib.func.getError(errorCode, 'SS0008');
        throw new ApolloError(errResult.message, errResult.code);
    }
}

//function to validate and update the shift swap status
module.exports.evaluateAndProcessShiftSwapStatus = async (parent, args, context, info) => {
    let organizationDbConnection;
    let catchCustomResponse = '';

    try {
        console.log("Inside evaluateAndProcessShiftSwapStatus function.")
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        //Check login employee access for shift swap form
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, null, 'ui', null, formIds.shiftSwap, {orgCode: context.Org_Code });
        if (!checkRights || !checkRights.Role_Update) {
            throw '_DB0102';
        }

        await validateInputs(args);

        let { shiftSwapResponse, matchingSwapIds, overlapErrorCode,overlapErrorMessage } = await evaluateAndProcessSwap(organizationDbConnection, args, context);
        
        if(shiftSwapResponse){
            catchCustomResponse = shiftSwapResponse;
            throw 'SS0158';
        }else if( overlapErrorCode) {
            catchCustomResponse = overlapErrorMessage;
            throw 'SS0160';
        }else{
            return { errorCode: "", message: `${checkRights.Custom_Form_Name} status updated successfully.` , matchingSwapIds: matchingSwapIds };
        }
    }
    catch (e) {
        handleError(catchCustomResponse,e, args);
    }
    finally {
        organizationDbConnection ? organizationDbConnection.destroy : null;//Destroy DB connection
    }
}