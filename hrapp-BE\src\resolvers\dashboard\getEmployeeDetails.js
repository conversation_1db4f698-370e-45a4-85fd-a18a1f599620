const { ehrTables } = require('../../../common/tablealias');

module.exports.getEmployeeDetails  = async(parent, args, context, info) => {

    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // Organization database connection
    const knex = require('knex');
    // get the organization data base connection
    let moment = require('moment-timezone');
    let orgDb='';

    try
    {
        let orgDb = knex(context.connection.OrganizationDb);
        let logInEmpId = context.Employee_Id;

        let employeeInfo = await orgDb.select({
                                employeeName: orgDb.raw('CONCAT_WS(" ",P.Emp_First_Name, P.Emp_Middle_Name, P.Emp_Last_Name)'),
                                designation: 'Des.Designation_Name',
                                department: 'Dep.Department_Name',
                                photoPath: 'P.Photo_Path',
                                userDefinedEmployeeId: 'J.User_Defined_EmpId',
                                employeeId: 'J.Employee_Id'
                            })
                            .from(ehrTables.empPersonalInfo + ' as P')
                            .leftJoin(ehrTables.empJob + ' as J', '<PERSON>.Employee_Id', 'P.Employee_Id')
                            .leftJoin(ehrTables.designation + ' as Des', 'Des.Designation_Id', 'J.Designation_Id')
                            .leftJoin(ehrTables.department + ' as Dep', 'Dep.Department_Id', 'J.Department_Id')
                            .where('P.Employee_Id', logInEmpId)
                            .then((res)=>{
                                return res[0] ? res[0] : null;
                            })

        if(employeeInfo && Object.keys(employeeInfo).length > 0)
        {
            if(employeeInfo.photoPath)
            {
                // form s3file name
                let fileName = await commonLib.func.formS3FilePath(employeeInfo.photoPath, context.Org_Code, 'profile', '', process.env.domainName);
                // get the employee profile picture signed url
                employeeInfo.photoPath = fileName ? await commonLib.func.getFileURL(process.env.region, process.env.hrappProfileBucket, fileName) : '';
            }

            let employeeType = await orgDb.select('ET.Work_Schedule as workSchedule')
                                            .from(ehrTables.empJob +' as J')
                                            .leftJoin(ehrTables.empType +' as ET', 'ET.EmpType_Id', 'J.EmpType_Id')
                                            .where('J.Employee_Id', logInEmpId)
                                            .then((res)=>{
                                                return res[0].workSchedule;
                                            })

            let timezone = await commonLib.func.getEmployeeTimeZone(logInEmpId,orgDb,0);

            let todayDate = moment().tz(timezone).format('YYYY-MM-DD');

            if(employeeType === 'Employee Level')
            {
                let { startTime, endTime, shiftType } = await getWorkScheduleHrs(orgDb, logInEmpId);

                employeeInfo.startTime = startTime;
                employeeInfo.endTime = endTime;
                employeeInfo.shiftType = shiftType;
            }
            else
            {
                let shiftDetails = await orgDb.select({
                                                    startTime: 'WS.Regular_Work_Start_Time',
                                                    endTime: 'WS.Regular_Work_End_Time',
                                                    overlapDay: 'Twodays_Flag'
                                                })
                                                .from(ehrTables.shiftEmpMapping + ' as SM')
                                                .leftJoin(ehrTables.empShiftType + ' as ST', 'ST.Shift_Type_Id', 'SM.Shift_Type_Id')
                                                .leftJoin(ehrTables.workSchedule + ' as WS', 'WS.WorkSchedule_Id', 'ST.WorkSchedule_Id')
                                                .where('SM.Shift_Start_Date', '<=', todayDate)
                                                .where('SM.Shift_End_Date', '>=', todayDate)
                                                .where('SM.Employee_Id', logInEmpId)

                if(shiftDetails[0])
                {
                    employeeInfo.startTime = shiftDetails[0].startTime;
                    employeeInfo.endTime = shiftDetails[0].endTime;
                    employeeInfo.shiftType = shiftDetails[0].overlapDay ? 'Night Shift' : 'Day Shift'
                }
                else
                {
                    let { startTime, endTime, shiftType } = await getWorkScheduleHrs(orgDb, logInEmpId);
                    employeeInfo.startTime = startTime;
                    employeeInfo.endTime = endTime;
                    employeeInfo.shiftType = shiftType;
                }
            }
            orgDb ? orgDb.destroy() : null;
            return {
                errorCode: null,
                message: 'Employee details retrieved successfully',
                employeeDetails: JSON.stringify(employeeInfo)
            }
        }
        else
        {
            throw('DB0017');
        }
    }
    catch(getEmployeeDetailsErr)
    {
        orgDb ? orgDb.destroy() : null;
        console.log('Error in getEmployeeDetails() function main catch block.',getEmployeeDetailsErr);
        if(getEmployeeDetailsErr === 'DB0017')
        {
            errResult = commonLib.func.getError('', 'DB0017');
        }
        else
        {
            errResult = commonLib.func.getError('', 'DB0016');
        }
        // return response
        throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, employeeDetails:""}));
    }
}

async function getWorkScheduleHrs(orgDb, logInEmpId)
{
    try
    {
        let shiftDetails = await orgDb.select({
                                                startTime: 'WS.Regular_Work_Start_Time',
                                                endTime: 'WS.Regular_Work_End_Time',
                                                overlapDay: 'Twodays_Flag'
                                            })
                                        .from(ehrTables.empJob + ' as EJ')
                                        .leftJoin(ehrTables.workSchedule + ' as WS', 'WS.WorkSchedule_Id', 'EJ.Work_Schedule')
                                        .where('EJ.Employee_Id', logInEmpId)

        if(shiftDetails[0] && shiftDetails[0].startTime && shiftDetails[0].endTime)
        {
            return {
                startTime: shiftDetails[0].startTime,
                endTime: shiftDetails[0].endTime,
                shiftType: shiftDetails[0].overlapDay ? 'Night Shift' : 'Day Shift'
            }
        }

        return {
            startTime: null,
            endTime: null,
            shiftType: null
        }
    }
    catch(workScheduleErr)
    {
        console.log('Error in getWorkScheduleHrs()', workScheduleErr);
        return {
            startTime: null,
            endTime: null,
            shiftType: null
        }
    }
}