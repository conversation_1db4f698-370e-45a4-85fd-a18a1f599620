// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require file to access constant values
const { formName,systemLogs } = require('../../common/appconstants');
// require validation file
const commonValidation = require('../../common/commonvalidation');
// require file to access common functions
const {insertSystemLogs} = require('../../common/commonFunction');
// require table alias
const { ehrTables } = require('../../common/tablealias');

// resolver definition
const resolvers = {
    Mutation:{
        // function to update service provider location details
        updateServiceProviderLocation: async (parent, args, context, info) => {
            // variable declarations
            let organizationDb='';
            let validationError={};
            try{
                console.log('Inside updateServiceProviderLocation function');
                // variable declarations
                let loggedInEmpId=context.Employee_Id;
                let ipAddress=context.User_Ip;
                // get the organization database connection
                organizationDb = knex(context.connection.OrganizationDb);
                // check general form access rights
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDb, loggedInEmpId, formName.general,'','UI');
                if (Object.keys(checkRights).length === 0) {
                    throw '_DB0102'; // throw error if employee does not have access
                }
                else if(checkRights.Employee_Role.toLowerCase() !== 'admin'){
                    throw '_DB0109';
                }
                else {
                    if(checkRights.Role_Update === 1)
                    {
                        // validate the input service providerId
                        if(args.serviceProviderId){
                            if (!commonValidation.numberValidation(args.serviceProviderId)) {
                                validationError['IVE0182'] = commonLib.func.getError('', 'IVE0182').message;
                            }
                        }
                        else{
                            validationError['IVE0182'] = commonLib.func.getError('', 'IVE0182').message;
                        }
                        // validate the input location Id
                        if(args.locationId){
                            if (!commonValidation.numberValidation(args.locationId)) {
                                validationError['IVE0181'] = commonLib.func.getError('', 'IVE0181').message;
                            }
                        }
                        else{
                            validationError['IVE0181'] = commonLib.func.getError('', 'IVE0181').message;
                        }
                        if(Object.keys(validationError).length === 0){
                            // get the fieldforce and PT location from org details table
                            return(
                                organizationDb
                                .select('Field_Force','PT_Compliance_Location')
                                .from(ehrTables.orgDetails)
                                .then(async (getOrgDetails) =>{
                                    // check whether record exist or not
                                    if(getOrgDetails.length>0){
                                        // check whether field force enabled or not
                                        if(getOrgDetails[0].Field_Force){
                                            // If PT_Compliance_Location is professional tax then check whether payslip generated for any one of the service provider employees
                                            if(getOrgDetails[0].PT_Compliance_Location && getOrgDetails[0].PT_Compliance_Location.toLowerCase() === 'professional tax'){
                                                // check whether monthly payslip record exist if it is not exist then check hourly wages payslip record
                                                return(
                                                    organizationDb
                                                    .count('SP.Payslip_Id as count')
                                                    .from(ehrTables.empJob + ' as EJ')
                                                    .innerJoin(ehrTables.salaryPayslip + ' as SP','EJ.Employee_Id','SP.Employee_Id')
                                                    .innerJoin(ehrTables.salaryDeduction + ' as SD','SP.Payslip_Id','SD.Payslip_Id')
                                                    .where('EJ.Service_Provider_Id',args.serviceProviderId)
                                                    .where('SD.Deduction_Name','Professional Tax')
                                                    .then(async (getDetails) =>{
                                                        // if payslip record exist then location details cannot be updated. So return the error response
                                                        if(getDetails[0].count){
                                                            throw 'SGE0107'; // Location cannot be updated since payslip record generated with PT compliance location.
                                                        }
                                                        else{
                                                            return(
                                                                organizationDb
                                                                .count('HP.Payslip_Id as count')
                                                                .from(ehrTables.empJob + ' as EJ')
                                                                .innerJoin(ehrTables.hourlywagesPayslip + ' as HP','EJ.Employee_Id','HP.Employee_Id')
                                                                .innerJoin(ehrTables.salaryDeduction + ' as SD','HP.Payslip_Id','SD.Payslip_Id')
                                                                .where('EJ.Service_Provider_Id',args.serviceProviderId)
                                                                .where('SD.Deduction_Name','Professional Tax')
                                                                .then(async (getPayslipDetails) =>{
                                                                    // if payslip record exist then location details cannot be updated. So return the error response
                                                                    if(getPayslipDetails[0].count){
                                                                        throw 'SGE0107'; // Location cannot be updated since payslip record generated with PT compliance location.
                                                                    }
                                                                    else{
                                                                        // update the locationId for the input service providerId
                                                                        await updateLocationId(organizationDb,args,loggedInEmpId);
                                                                        // update the system logs
                                                                        let message = 'Update service provider location details' + ' - ' + args.serviceProviderId;
                                                                        await insertSystemLogs(organizationDb,systemLogs.roleUpdate,ipAddress,loggedInEmpId,'','','',message);
                                                                        organizationDb ? organizationDb.destroy() : null;
                                                                        return { errorCode:'',message:'Service provider location details updated successfully.'};
                                                                    }
                                                                })
                                                            )
                                                        }
                                                    })
                                                )
                                            }
                                            else{
                                                // update the locationId for the input service providerId
                                                await updateLocationId(organizationDb,args,loggedInEmpId);
                                                let message = 'Update service provider location details' + ' - ' + args.serviceProviderId;
                                                await insertSystemLogs(organizationDb,systemLogs.roleUpdate,ipAddress,loggedInEmpId,'','','',message);
                                                organizationDb ? organizationDb.destroy() : null;
                                                return { errorCode:'',message:'Service provider location details updated successfully.'};
                                            }
                                        }
                                        else{
                                            throw 'SGE0106'; // The field force is not enabled for this organization.
                                        }
                                    }
                                    else{
                                        throw 'SGE0105'; // Organization details does not exists.
                                    }
                                })
                                .catch(function (catchError) {
                                    console.log('Error in updateServiceProviderLocation function .catch block.',catchError);
                                    organizationDb ? organizationDb.destroy() : null;
                                    errResult = commonLib.func.getError(catchError, 'SGE0104');
                                    // throw error response to UI
                                    throw new ApolloError(errResult.message, errResult.code);
                                })    
                            );
                        }
                        else{
                            throw 'IVE0000'
                        }
                    }
                    else{
                        throw '_DB0102';
                    }
                }
            }
            catch(mainCatchError)
            {
                console.log('Error in updateServiceProviderLocation function main catch block',mainCatchError);
                // destroy database connection
                organizationDb ? organizationDb.destroy() : null;
                if (mainCatchError === 'IVE0000') {
                    console.log('Validation error in updateServiceProviderLocation function - ',validationError);
                    let errResult = commonLib.func.getError(mainCatchError, 'IVE0000');
                    throw new UserInputError(errResult.message, { validationError: validationError });
                }
                else{
                    let errResult = commonLib.func.getError(mainCatchError, 'SGE0003');
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        }
    }
};

exports.resolvers = resolvers;

// function to update location based on input providerId
async function updateLocationId(organizationDb,args,loggedInEmpId){
    try{
        // get the employee timezone based on location
        let employeeTimeZone = await commonLib.func.getEmployeeTimeZone(loggedInEmpId, organizationDb,1);
        return(
            organizationDb(ehrTables.serviceProvider)
            .update({
                Location_Id:args.locationId,
                Updated_On: employeeTimeZone,
                Updated_By: loggedInEmpId
            })
            .where('Service_Provider_Id',args.serviceProviderId)
            .then(() =>{
                return 'success';
            })
            .catch(function (catchError) {
                console.log('Error in updateLocationId function .catch block.',catchError);
                throw 'SGE0104';
            })
        );
    }
    catch(error){
        console.log('Error in updateLocationId function main catch block.',error);
        throw 'SGE0104';
    }
};
