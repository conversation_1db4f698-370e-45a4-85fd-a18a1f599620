//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require tables
const {ehrTables,appManagerTables} = require('./tablealias');

//Function to get organization details based on the notification name
async function getBatchNotificationDetails(appManagerDbConnection,notificationName,keyName,emailNotificationStatus){
    try{
        return(
            appManagerDbConnection(appManagerTables.batchEmailNotification+' as BN')
            .select('BN.Org_Code','HRU.Data_Region')
            .leftJoin(appManagerTables.hrappRegisteruser+' as HRU','BN.Org_Code','HRU.Org_Code')
            .where('Notification_For',notificationName)
            .where(keyName,emailNotificationStatus)
            .then((notificationOrgDetails)=>{
                if(notificationOrgDetails && notificationOrgDetails.length > 0){
                    return notificationOrgDetails;
                }else{
                    return [];
                }
            })
            .catch(catchError => {
                console.log('Error in getBatchNotificationDetails function .catch block',catchError);
                throw catchError;
            })
        );
    }
    catch(mainCatchError){
        console.log('Error in getBatchNotificationDetails function main catch block.', mainCatchError);
        throw mainCatchError;
    }
};

//Function to update the notification final status for that organization code
async function updateOrgBatchNotificationStatus(appManagerDbConnection,orgCode,keyName,status,notificationName){
    try{
        //Require moment package
        const moment=require('moment');
        //Update details
        let updateParams ={
            Updated_On: moment().tz('Asia/Kolkata').format('YYYY-MM-DD HH:mm:ss')
        };
        updateParams[keyName] = status;
        return(
            appManagerDbConnection(appManagerTables.batchEmailNotification)
            .update(updateParams)
            .where('Org_Code',orgCode)
            .where('Notification_For',notificationName)
            .then(()=>{
                return true;
            })
            .catch(catchError => {
                console.log('Error in updateOrgBatchNotificationStatus function .catch block',catchError);
                throw catchError;
            })                
        );
    }
    catch(mainCatchError){
        console.log('Error in updateOrgBatchNotificationStatus function main catch block.', mainCatchError);
        throw mainCatchError;
    }
}

//Group the inputArray by using the key
async function groupInputsByKey(inputArray,keyName){
    try{
        return inputArray.reduce((tempDetails, currItem) => {
            const groupKey = currItem[keyName];
            if (!tempDetails[groupKey]) {
                tempDetails[groupKey] = [currItem];
            } else {
                tempDetails[groupKey].push(currItem);
            }
            return tempDetails;
        }, {});
    }catch(mainCatchError){
        console.log('Error in the groupInputsByKey() function.',mainCatchError);
        throw mainCatchError;
    }
}

//Form the HTML design
async function formHTMLDesign(action,inputArray,redirectionUrl){
    try{
        let htmlTableDesign = '';
        if(action === 'probationemailtohr'){
            let probationRowDesign = '';
            let managerName = '';
            for(let g=0;g<inputArray.length;g++){
                employeeDetails = inputArray[g];
                managerName = employeeDetails.managerName ? employeeDetails.managerName : '-';

                probationRowDesign += `
                <tr style="border-top:1px solid #e2e7ee">
                    <td style="font-weight: bold;">${employeeDetails.userDefinedEmployeeId}</td>
                    <td style="font-weight: bold;">${employeeDetails.employeeName}</td>
                    <td style="font-weight: bold;">${managerName}</td>
                </tr>
                `;
            }
            //Form final html for table design
            htmlTableDesign = `
            <table cellpadding="10" cellspacing="0" style="border-collapse:collapse;color:#260029;font-size:12px" width="95%">
            <tbody>
                <tr>
                    <td></td>
                </tr>
                <tr align="left" style="border-top:1px solid #e2e7ee">
                    <th style="color: #595C68;">ID</th>
                    <th style="color: #595C68;">Name</th>
                    <th style="color: #595C68;">Manager</th>
                </tr>
                ${probationRowDesign}
            </tbody>
            </table>
            `;
        }else if(action === 'probationemailtomanager'){
            let probationRowDesign = '';
            for(let g=0;g<inputArray.length;g++){
                employeeDetails = inputArray[g];
                probationRowDesign += `
                <tr style="border-top:1px solid #e2e7ee">
                    <td style="font-weight: bold;">${employeeDetails.userDefinedEmployeeId}</td>
                    <td style="font-weight: bold;">${employeeDetails.employeeName}</td>
                </tr>
                `;
            }
            //Form final html for table design
            htmlTableDesign = `
            <table cellpadding="10" cellspacing="0" style="border-collapse:collapse;color:#260029;font-size:12px" width="95%">
            <tbody>
                <tr>
                    <td></td>
                </tr>
                <tr align="left" style="border-top:1px solid #e2e7ee">
                    <th style="color: #595C68;">ID</th>
                    <th style="color: #595C68;">Name</th>
                </tr>
                ${probationRowDesign}
            </tbody>
            </table>
            `;
        }else if(action === 'emponnoticeemailtomanager'){
            let empOnNoticeDesign = '',
            employeeDetails = '';
            for(let g=0;g<inputArray.length;g++){
                employeeDetails = inputArray[g];
                empOnNoticeDesign += `
                <tr style="border-top:1px solid #e2e7ee">
                    <td style="font-weight: bold;">${employeeDetails.userDefinedEmployeeId}</td>
                    <td style="font-weight: bold;">${employeeDetails.employeeName}</td>
                </tr>
                `;
            }
            //Form final html for table design
            htmlTableDesign = `
            <table cellpadding="10" cellspacing="0" style="border-collapse:collapse;color:#260029;font-size:12px" width="95%">
            <tbody>
                <tr>
                    <td></td>
                </tr>
                <tr align="left" style="border-top:1px solid #e2e7ee">
                    <th style="color: #595C68;">ID</th>
                    <th style="color: #595C68;">Name</th>
                </tr>
                ${empOnNoticeDesign}
            </tbody>
            </table>
            `;
        }else if(action === 'emponnoticeemailtohr'){
            let empOnNoticeDesign = '',
            employeeDetails = '';
            for(let g=0;g<inputArray.length;g++){
                employeeDetails = inputArray[g];
                let assigneeName = employeeDetails.assigneeName ? employeeDetails.assigneeName : (employeeDetails.groupName ? employeeDetails.groupName : '-');
                empOnNoticeDesign += `
                <tr align="center" style="border-top:1px solid #e2e7ee">
                    <td style="font-weight: bold;">${employeeDetails.userDefinedEmployeeId}</td>
                    <td style="font-weight: bold;">${employeeDetails.employeeName}</td>
                    <td style="font-weight: bold;">${assigneeName}</td>
                </tr>
                `;
            }
            //Form final html for table design
            htmlTableDesign = `
            <table cellpadding="10" cellspacing="0" style="border-collapse:collapse;color:#260029;font-size:12px" width="95%">
            <tbody>
                <tr>
                    <td></td>
                </tr>
                <tr align="center" style="border-top:1px solid #e2e7ee">
                    <th style="color: #595C68;">ID</th>
                    <th style="color: #595C68;">Name</th>
                    <th style="color: #595C68;">Approver/Group</th>
                </tr>
                ${empOnNoticeDesign}
            </tbody>
            </table>
            `;
        }else if(action === 'longleaveemailtohr'){
            let longLeaveRowDesign = '';
            let managerName = '';
            for(let g=0;g<inputArray.length;g++){
                employeeDetails = inputArray[g];
                managerName = employeeDetails.managerName ? employeeDetails.managerName : '-';

                longLeaveRowDesign += `
                <tr style="border-top:1px solid #e2e7ee">
                    <td style="font-weight: bold;">${employeeDetails.userDefinedEmployeeId}</td>
                    <td style="font-weight: bold;">${employeeDetails.employeeName}</td>
                    <td style="font-weight: bold;">${employeeDetails.leaveName}</td>
                    <td style="font-weight: bold;">${managerName}</td>
                </tr>
                `;
            }
            //Form final html for table design
            htmlTableDesign = `
            <table cellpadding="10" cellspacing="0" style="border-collapse:collapse;color:#260029;font-size:12px" width="95%">
            <tbody>
                <tr>
                    <td></td>
                </tr>
                <tr align="left" style="border-top:1px solid #e2e7ee">
                    <th style="color: #595C68;">ID</th>
                    <th style="color: #595C68;">Name</th>
                    <th style="color: #595C68;">Leave Name</th>
                    <th style="color: #595C68;">Manager</th>
                </tr>
                ${longLeaveRowDesign}
            </tbody>
            </table>
            `;
        }else if(action === 'longleaveemailtomanager'){
            let longLeaveTable = '',
            employeeDetails = '';
            for(let g=0;g<inputArray.length;g++){
                employeeDetails = inputArray[g];
                longLeaveTable += `
                <tr style="border-top:1px solid #e2e7ee">
                    <td style="font-weight: bold;">${employeeDetails.userDefinedEmployeeId}</td>
                    <td style="font-weight: bold;">${employeeDetails.employeeName}</td>
                    <td style="font-weight: bold;">${employeeDetails.leaveName}</td>
                </tr>
                `;
            }
            //Form final html for table design
            htmlTableDesign = `
            <table cellpadding="10" cellspacing="0" style="border-collapse:collapse;color:#260029;font-size:12px" width="95%">
            <tbody>
                <tr>
                    <td></td>
                </tr>
                <tr align="left" style="border-top:1px solid #e2e7ee">
                    <th style="color: #595C68;">ID</th>
                    <th style="color: #595C68;">Name</th>
                    <th style="color: #595C68;">Leave Name</th>
                </tr>
                ${longLeaveTable}
            </tbody>
            </table>
            `;
        }
        htmlTableDesign +=`<table border="0" cellpadding="0" cellspacing="0" width="95%" style="margin: 5px;border-top:1px solid #e2e7ee">
            <tbody>
                <tr align="center">
                    <td align="center">
                    <a href=${redirectionUrl} style="color: #1385FF; font-weight: bold; font-size: 12px;">View all ></a>
                    </td>
                </tr>
            </tbody>
        </table>`
        return htmlTableDesign;
    }catch(catchError){
        console.log('Error in the groupInputsByKey() function.',catchError);
        throw catchError;
    }
}

//Function to send the email to HR
async function sendEmailToHr(organizationDbConnection,appManagerDbConnection,emailInputs){
    try{
        let {orgCode,employeesList,htmlTableKeyName,emailSubject,emailContent,statusKey,notificationForKey,commonNotificationTemplate} = emailInputs;
        let htmlTable = '';
        let emailFailureCount = 0;
        let toEmailAddress = [];
        let status = '';
        let finalResponse = '';
        
        //Get organization details for sending mail
        let orgDetails = await commonLib.func.getOrgDetails(orgCode, organizationDbConnection);
        let redirectionUrl = await formRedirectionUrl(orgCode,notificationForKey);
        //Form the table with employee id, employee name and manager name
        htmlTable = await formHTMLDesign(htmlTableKeyName,employeesList,redirectionUrl);
        //Get the HR group email address
        let hrGroupEmailAddress = await getHrGroupEmployeeEmail(organizationDbConnection);        
        if((hrGroupEmailAddress && hrGroupEmailAddress.length > 0)){
            toEmailAddress=hrGroupEmailAddress;
        }
        if(orgDetails.hrAdminEmailAddress){
            toEmailAddress.push(orgDetails.hrAdminEmailAddress);
        }

        //If the to email address exist
        if(toEmailAddress.length > 0){
            //Remove duplicate email ids
            toEmailAddress = [...new Set(toEmailAddress)];
            let inputParams={
                emailFrom: process.env.emailFrom,
                orgLogo:orgDetails.logoPath ? orgDetails.logoPath : '',
                emailSubject:emailSubject,
                emailContent: emailContent,
                htmlTable: htmlTable,
                emailIdList: toEmailAddress
            };
            let mailListData;
            do{
                //Function to send the bulk emails
                mailListData=await commonLib.func.sendBulkMail(process.env.sesTemplatesRegion,inputParams,commonNotificationTemplate);
                console.log('Response after calling sendBulkMail function',mailListData);
            }
            // iterate the list till all the records are processed
            while(Object.keys(mailListData).length>0){
                console.log('Mail sent successfully for all hr group email ids.');
            }
        }else{
            emailFailureCount +=1;
            console.log('To or Bcc email address does not exist. So email notification is not sent for the org code -',orgCode,' and for employee - ',employeesList,' and the org details - ',orgDetails);
        }

        if(emailFailureCount > 0){
            status = 'Failure';
            finalResponse={
                nextStep:'End',
                message:'Email notification is not send for one or all the employee(s) .'
            };
        }else{
            status = 'Success';
            finalResponse={
                nextStep:'End',
                message:'Email notification send successfully.'
            };
        }
        //Update status
        await updateOrgBatchNotificationStatus(appManagerDbConnection,orgCode,statusKey,status,notificationForKey);
        let emailResponse = {
            finalResponse: finalResponse
        };
        return emailResponse;
    }catch(hrEmailCatchError){
        console.log('Error in the sendEmailToHr function main catch block.',hrEmailCatchError);
        throw hrEmailCatchError;
    }
}

//Function to send email to manager
async function sendEmailToManager(appManagerDbConnection,organizationDbConnection,emailInputs){
    try{
        let { orgCode, employeesList, managerGroupKey, htmlTableKeyName, emailSubject, emailContent, commonNotificationTemplate, statusKey, notificationForKey } = emailInputs;
        let groupEmployeesByManager = await groupInputsByKey(employeesList,managerGroupKey);
        
        let htmlTable = '',
        emailStatus = '',
        finalResponse = '';
        let emailFailureCount = 0;
        //Get organization details for sending mail
        let orgDetails = await commonLib.func.getOrgDetails(orgCode, organizationDbConnection);
        let redirectionUrl = await formRedirectionUrl(orgCode,notificationForKey);
        let groupByKey = 0;
        for (let key in groupEmployeesByManager) {
            groupByKey += 1;
            let employeesGroupByManager = groupEmployeesByManager[key];
            htmlTable = await formHTMLDesign(htmlTableKeyName,employeesGroupByManager,redirectionUrl);
            
            if(employeesGroupByManager[0].managerEmailAddress){
                //Form notification params
                let notificationParams = {
                    'Source': process.env.emailFrom,
                    'Template': commonNotificationTemplate,
                    'Destination': {
                        'ToAddresses': [employeesGroupByManager[0].managerEmailAddress]
                    },
                    'ReplyToAddresses': [process.env.emailFrom],
                    'TemplateData': JSON.stringify({
                        orgLogo: orgDetails.logoPath ? orgDetails.logoPath : '',
                        emailSubject: emailSubject,
                        emailContent: emailContent,
                        htmlTable: htmlTable
                    })
                };
                console.log('notificationParams',notificationParams, process.env.sesTemplatesRegion);
                //call the sendEmailNotifications function to send the email
                let response = await commonLib.func.sendEmailNotifications(notificationParams,process.env.sesTemplatesRegion);
                if (response !== true){
                    emailFailureCount += 1;
                    console.log('Error in sending email notification for the org code -',orgCode,' and for employee - ',employeesGroupByManager,' and the org details - ',orgDetails);
                }
            }else{
                emailFailureCount +=1;
                console.log('To or Bcc email address does not exist. So email notification is not sent for the org code -',orgCode,' and for employee - ',employeesGroupByManager,' and the org details - ',orgDetails);
            }
            //If the last record is processed
            if(groupByKey===Object.keys(groupEmployeesByManager).length){
                console.log('Notification is triggered completely for the org code -',orgCode);
                if(emailFailureCount > 0){
                    console.log('Notification is sent partially or it does not sent for the org code -',orgCode);
                    emailStatus = 'Failure';
                    finalResponse={
                        nextStep:'End',
                        message:'Email notification is not send for one or all the employee(s) .'
                    };
                }else{
                    console.log('Notification is sent completely for the org code -',orgCode);
                    emailStatus = 'Success';
                    finalResponse={
                        nextStep:'End',
                        message:'Email notification sent successfully.'
                    };
                }
                await updateOrgBatchNotificationStatus(appManagerDbConnection,orgCode,statusKey,emailStatus,notificationForKey);
                //Destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
            }
        }
        return finalResponse;
    }catch(managerEmailCatchError){
        console.log('Error in the sendEmailToManager function main catch block.', managerEmailCatchError);
        throw managerEmailCatchError;
    }
}

//Get the HR group email
async function getHrGroupEmployeeEmail(organizationDbConnection){
    try{
        return(
            organizationDbConnection
            .pluck('Emp_Email')
            .from(ehrTables.empJob)
            .innerJoin('hr_group', 'HR_Group_Id', 'Employee_Id')
            .where('Emp_Status', 'Active')
            .whereNot('Emp_Email','')
            .whereNotNull('Emp_Email')
            .then((hrEmailResult)=>{
                return (hrEmailResult && hrEmailResult.length > 0) ? hrEmailResult : [];
            })
            .catch(catchError => {
                console.log('Error in getHrGroupEmployeeEmail function .catch block',catchError);
                throw catchError;
            })                
        );
    }
    catch(mainCatchError){
        console.log('Error in getHrGroupEmployeeEmail function main catch block.', mainCatchError);
        throw mainCatchError;
    }
}

//Function to form the redirection URL
async function formRedirectionUrl(orgCode,notificationForKey){
    try{
        let redirectionURL = '';

        if(notificationForKey === 'probEndDate'){
            //Form redirection URL
            if (process.env.stageName.toLowerCase() === 'local') {
                redirectionURL = 'http://127.0.0.1:8080/hrapponline/employees/employees';
            } else {
                redirectionURL = 'https://' + orgCode + '.' + process.env.domainName + process.env.webAddress +'/employees/employees';
            }
        }else if(notificationForKey === 'empOnNotice'){
            //Form redirection URL
            if (process.env.stageName.toLowerCase() === 'local') {
                redirectionURL = 'http://127.0.0.1:8080/hrapponline/workflow/task-management';
            } else {
                redirectionURL = 'https://' + orgCode + '.' + process.env.domainName + process.env.webAddress +'/approvals/approval-management';
            }
        }else{
            //If the notification for key is long leave
            //Form redirection URL
            if (process.env.stageName.toLowerCase() === 'local') {
                redirectionURL = 'http://127.0.0.1:8080/hrapponline/employees/leaves';
            } else {
                redirectionURL = 'https://' + orgCode + '.' + process.env.domainName + process.env.webAddress +'/employees/leaves';
            }
        }
        return redirectionURL;
    }
    catch(mainCatchError){
        console.log('Error in formRedirectionUrl function main catch block.', mainCatchError);
        throw mainCatchError;
    }
}

//Function to get the active leave type ids where the total days should be greater than long leave constant value
async function getLongLeaveTypeIds(organizationDbConnection,longLeaveTotalDays){
    try{
        return(
            organizationDbConnection
            .pluck('LeaveType_Id')
            .from(ehrTables.leavetype)
            .where('Total_Days','>', longLeaveTotalDays)
            .where('Leave_Status', 'Active')
            .then((result)=>{
                return (result && result.length > 0) ? result : [];
            })
            .catch(catchError => {
                console.log('Error in getLongLeaveTypeIds function .catch block',catchError);
                throw catchError;
            })                
        );
    }
    catch(mainCatchError){
        console.log('Error in getLongLeaveTypeIds function main catch block.', mainCatchError);
        throw mainCatchError;
    }
}

module.exports = {
    getBatchNotificationDetails,
    updateOrgBatchNotificationStatus,
    groupInputsByKey,
    formHTMLDesign,
    sendEmailToHr,
    getHrGroupEmployeeEmail,
    sendEmailToManager,
    formRedirectionUrl,
    getLongLeaveTypeIds
};