//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make a database connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require common constant files
const { formName,systemLogs, defaultValues } = require('../../../common/appconstants');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
//Require common function
const {goalsPublishableOrDeletePerformanceIds} = require('../../../common/performanceManagementCommonFunction');

//Delete employee performance and goals for the assessment month and year
module.exports.deleteEmployeeGoalsAndAchievement = async (parent, args, context, info) => {
    console.log("Inside deleteEmployeeGoalsAndAchievement() function.");
    
    let organizationDbConnection;
    let errResult;
    let validationError = {};

    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const loginEmployeeId = context.Employee_Id;

        // Check Goals and achievement form access rights.
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.goalsAndAchievement, '', 'UI');

        // If the login employee is admin or manager and having delete access for the 'Goals and achievement' form
        if ((Object.keys(checkRights).length >0 && checkRights.Role_Delete===1) && (checkRights.Is_Manager===1 || checkRights.Employee_Role==='admin')) {
            if(!args.performanceAssessmentId){
                validationError['IVE0217'] = commonLib.func.getError('', 'IVE0217').message;
            }
            // check input validation error exist or not
            if(Object.keys(validationError).length ===0){
                let performanceAssessmentId = args.performanceAssessmentId;
                let deleteApplicableAssessmentIds = await goalsPublishableOrDeletePerformanceIds(organizationDbConnection,loginEmployeeId,checkRights.Employee_Role,args,'delete');
                if(deleteApplicableAssessmentIds && deleteApplicableAssessmentIds.length > 0 &&
                ((performanceAssessmentId.includes(deleteApplicableAssessmentIds)))){
                    return (
                    organizationDbConnection
                    .transaction(function (trx) {
                        //Delete the employee performance details
                        return(
                        organizationDbConnection(ehrTables.performanceGoalAchievement)
                        .delete()
                        .where('Performance_Assessment_Id',performanceAssessmentId)
                        .where('Goal_Publish_Status',defaultValues.goalsAndAchievementGoalUnPublishStatus)
                        .transacting(trx)
                        .then((deleteResponse) => {
                            if(deleteResponse){
                                //Delete the employee goals details
                                return(
                                organizationDbConnection(ehrTables.assessmentCycleGoalRatings)
                                .delete()
                                .where('Performance_Assessment_Id',performanceAssessmentId)
                                .transacting(trx)
                                .then(() => {
                                    return 'success';
                                }))
                            }else{
                                console.log('Employee goals and achievement already deleted. Response while deleting the performance details.',deleteResponse);
                                throw '_EC0006';
                            }
                        }))
                    })
                    .then(async() => {
                        //System log inputs. Example system log message: Delete Goals And Achievement  - 50
                        let systemLogParams = {
                            action: systemLogs.roleDelete,
                            userIp: context.User_Ip,
                            employeeId: loginEmployeeId,
                            formName: formName.goalsAndAchievement,
                            trackingColumn: '',
                            organizationDbConnection: organizationDbConnection,
                            uniqueId: performanceAssessmentId
                        };

                        //call the function to add the system log
                        await commonLib.func.createSystemLogActivities(systemLogParams);

                        //Destroy database connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;

                        return { errorCode: '', message: 'Employee goals and achievement deleted successfully.'};
                    })
                    .catch(deleteCatchError => {
                        console.log('Error occurred in the deleteEmployeeGoalsAndAchievement() function .catch block. ',deleteCatchError,' for the performance assessment id: ',performanceAssessmentId);
                        errResult = commonLib.func.getError(deleteCatchError, 'EPM0131');
                        //Destroy database connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        //Return error response
                        throw new ApolloError(errResult.message,errResult.code);
                    }));
                }else{
                    console.log('Login employee is not the eligible approver to delete the performance id,',args.performanceAssessmentId);
                    throw('EPM0038');
                }
            }else{
                throw 'IVE0000';
            }
        }else{
            throw('_DB0103');
        }
    }
    catch(deleteGoalsAndAchievementMainCatchError) {
        console.log('Error in the deleteEmployeeGoalsAndAchievement() function main catch block. ',deleteGoalsAndAchievementMainCatchError);
        //Destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        //If validation error exist
        if (deleteGoalsAndAchievementMainCatchError === 'IVE0000') {
            console.log('Validation error in the deleteEmployeeGoalsAndAchievement() function',validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            //Return error response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else{
            errResult = commonLib.func.getError(deleteGoalsAndAchievementMainCatchError, 'EPM0032');
            //Return error response
            throw new ApolloError(errResult.message,errResult.code);
        }
    }
};