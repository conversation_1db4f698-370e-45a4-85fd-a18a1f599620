// resolver function to calculate the compensatory off balance from the additional wage claim form
module.exports.calculateOvertimeCompOffBalance = async (parent, args, context, info) => {
    console.log('Inside calculateOvertimeCompOffBalance() function');
    // require apollor server errors
    const { UserInputError,ApolloError } = require('apollo-server-lambda');
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // Organization database connection
    const knex = require('knex');
    // require validation file
    const {validateInputs,getOvertimeSettings,getCompensatoryOffBalanceDetailsForOvertime,validateOvertimeWorkSchedule} = require('./overtimeCommonFunctions');
    // require moment 
    const moment = require('moment-timezone');
    //variable declaration
    let orgDb;
    let errResult;
    let validationError = {};
    try{
        let compensatoryOffBalance = 0;
        // get db connection
        orgDb = knex(context.connection.OrganizationDb);        
        // get variable values
        let { employeeId,otStartTime,otEndTime} = args;
        // validate inputs
        validationError = validateInputs(args);
        // check validation error is exist or not
        if (Object.keys(validationError).length === 0) {
            // get the overtime settings
            let overtimeSettings = await getOvertimeSettings(orgDb);
            /** If overtime settings JSON length is greater than zero */
            if(Object.keys(overtimeSettings).length > 0){
                /** If the compensatory off is applicable for overtime */
                if(overtimeSettings['Comp_Off_Applicable_For_Overtime']){
                    let validateWorkScheduleInputs = {
                        employeeId: employeeId,
                        otStartTime: otStartTime,
                        allowRegularHoursOvertimeForWeekoffHoliday:overtimeSettings.Allow_Regular_Hours_Overtime_For_Weekoff_holiday
                    }
                    let validateOTWorkscheduleResponse = await validateOvertimeWorkSchedule(orgDb,validateWorkScheduleInputs);
                    /** If the overtime start date-time is valid */
                    if(!validateOTWorkscheduleResponse.error){
                        let isCalculateCompensatoryOffBalance = 0;
                        let compOffBalanceCalculationInputs = args;
                        let regularTo = validateOTWorkscheduleResponse.currentWorkScheduleDetails.regularTo;
                        
                        /** If the overtime start date time is greater than or equal to 'regular to' date-time then the
                         *  compensatory balance can be calculated from otStartDateTime to otEndDateTime */
                        if(otStartTime >= regularTo){
                            isCalculateCompensatoryOffBalance = 1;
                        }else{
                            /** If overtime end date-time is less than the 'regular to' date-time then the compensatory balance should not 
                             * be calculated here as the regular hours compensatory off balance will be calculated in the attendance 
                             * form during approval. */
                            if (otEndTime < regularTo){
                                isCalculateCompensatoryOffBalance = 0;
                            }else{
                                /** If overtime end date-time is greater than or equal to the 'regular to' date-time then the compensatory balance can 
                                 * be calculated from the 'regular to' date-time to the overtime end date-time. */
                                isCalculateCompensatoryOffBalance = 1;
                                compOffBalanceCalculationInputs.otStartTime = regularTo;// replace the ot start time to 'regular to' date time
                            }
                        }

                        // if the compensatory off balance can be calculated using the employee special wages configuration
                        if(isCalculateCompensatoryOffBalance === 1){
                            compOffBalanceCalculationInputs.workScheduleDetail = validateOTWorkscheduleResponse.currentWorkScheduleDetails;
                            compOffBalanceCalculationInputs.action = 'calculateovertimeCompOffBalance';
                            compOffBalanceCalculationInputs.fetchEmployeeWorkingDaySpecialWageConfig = 1;
                            compOffBalanceCalculationInputs.regularFromDate = moment(validateOTWorkscheduleResponse.currentWorkScheduleDetails.regularFrom).format("YYYY-MM-DD");// convert date time to date format.
                            
                            /** Send employeeId,otStartTime,otEndTime,workScheduleDetail,action,regularFromDate as an input 
                             * to calculate the compensatory off balance for the overtime hours */
                            let compensatoryOffBalanceResponse = await getCompensatoryOffBalanceDetailsForOvertime(orgDb,compOffBalanceCalculationInputs);
                            compensatoryOffBalance = compensatoryOffBalanceResponse.compensatoryOffBalance;
                        }else{
                            compensatoryOffBalance = 0;
                        }

                        // destroy database connection
                        orgDb ? orgDb.destroy() : null;
                        return { errorCode: '', message: 'Compensatory off balance is calculated successfully for the overtime hours.',compensatoryOffBalance: compensatoryOffBalance};
                    }else{
                        console.log('Response from the validateOvertimeWorkSchedule() function. ',validateOTWorkscheduleResponse);
                        /** If an error occured during overtime work schedule validation or overtime start date-time is not valid throw an error returned from the function. */
                        throw(validateOTWorkscheduleResponse.error);
                    }
                }else{
                    //compensatory off balance will be 0 as the the compensatory off applicable flag for overtime is not enabled
                    console.log("Compensatory off balance cannot be claimed for the overtime as the compensatory off applicable flag is disabled in the overtime settings.");
                    throw('OT0122');// throw error
                }
            }else{
                console.log('Empty or error response is returned from the getOvertimeSettings() function.', overtimeSettings);
                throw('OT0120');// throw error
            }
        }else{
            // throw validation error
            throw ('IVE0000');
        }
    }catch(calculateCompOffMainCatchError){
        console.log('Error in the calculateOvertimeCompOffBalance() function main catch block',calculateCompOffMainCatchError);
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
        if(calculateCompOffMainCatchError === 'IVE0000'){
            console.log('Validation error in the calculateOvertimeCompOffBalance() function.',validationError);
            errResult = commonLib.func.getError('', calculateCompOffMainCatchError);
            throw new UserInputError(errResult.message, { validationError: validationError });// return response
        }else{
            // get error and return it to UI
            let errResult = commonLib.func.getError(calculateCompOffMainCatchError, 'OT0121');
            throw new ApolloError(errResult.message, errResult.code);// return response
        }
    }
};