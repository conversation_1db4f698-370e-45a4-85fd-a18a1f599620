const { ApolloError} = require('apollo-server-lambda');
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
// require table names
const { ehrTables } = require('../../common/tablealias');
//Require moment
const moment = require('moment-timezone');

module.exports.getReportSetting = async (parent, args, context, info) => {
    let organizationDbConnection;
    try{
        // get the organization database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);
        return(
           organizationDbConnection(ehrTables.reportSettings + " as RS")
           .select('RS.Rep_Id as reportId','RS.Rep_Title as reportTitle','RS.Rep_Format as reportFormat','RS.Form_Name as formName','RS.Group_Title as 	groupTitle','RS.Report_Headers as reportHeaders','RS.Filters as filters','RS.Order_By as orderBy','RS.Allow_Same_Request_Timer as allowSameRequestTimer','RS.Timer_Second as timerSecond','RS.Retention_Period_Allowed as retentionPeriodAllowed','RS.Retention_Period_In_Month as retentionPeriodInMonth')
           .then(data=>{
               organizationDbConnection?organizationDbConnection.destroy():null;
               return {errorCode:"",message:"Report setting details retrieved successfully.",reportSettingDetails:data};
           })
           .catch(e=>{
               console.log("Error in getReportSetting() function .catch block.",e);
               throw('HBLC0112')
           })
        )
    }
    catch(e)
    {
        organizationDbConnection?organizationDbConnection.destroy():null;
        console.log("Error in getReportSetting() function main catch block.",e);
        let errResult = commonLib.func.getError(e,'HBLC0112');
        throw new ApolloError(errResult.message, errResult.code);
    }
}