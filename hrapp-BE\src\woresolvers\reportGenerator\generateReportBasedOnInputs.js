//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make database connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError,ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
const {callFunctionBasedOnInput}=require('./reportCommonFunction')
const {inputValidation}=require('../../../common/reportsInputValidation')
//Require moment
const moment = require('moment-timezone');

module.exports.generateReportBasedOnInputs= async (parent, args, context, info) => {
    let errResult = {};
    let validationError = {};
    let organizationDbConnection = '';
    let isAdmin=0;
    let isManager=0;
    try{
        console.log("Inside generateReportBasedOnInputs function.")
        let reportId=args.reportId;
        let orgCode=context.Org_Code;
        let logInEmpId = context.Employee_Id;
        let requestDateTime=moment.utc().format("YYYY-MM-DD HH:mm:ss");
        let inserHistoryParams={
            Rep_Id:reportId,
            Req_Params:JSON.stringify(args),
            Requested_By:logInEmpId,
            Request_Date_Time:requestDateTime,
            Report_Status:"InProgress",
            Email_Status:"InProgress"
        }
        // get the organization database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let reportDetails=await commonLib.func.getDataFromTableAccordingToStatus(organizationDbConnection,ehrTables.reportSettings,"Rep_Id",reportId);
        if(reportDetails && reportDetails.length>0)
        {
            reportDetails=reportDetails[0];
            if(reportDetails['Allow_Same_Request_Timer']==="No" && reportDetails['Report_Type']==='Asynchronous')
            {
                let previousRequestMaxDate=await getPrevRequestReportMaxDate(organizationDbConnection,reportId,logInEmpId,JSON.stringify(args));
                if(previousRequestMaxDate && previousRequestMaxDate.length>0)
                {
    
                    let timeDiff=moment.duration(moment(requestDateTime).diff(moment(previousRequestMaxDate)));
                    timeDiff=timeDiff.asSeconds();
                    if(timeDiff<reportDetails['Timer_Second'])
                    {
                        throw('HBLC0109');
                    }
                }
            }
            let formName=reportDetails['Form_Name'];
            // Check report view access rights exist for employee or not
            let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId, formName, '', 'UI');   
            if (Object.keys(checkRights).length === 0) {
                // throw error if view rights is not exists
                throw ('_DB0100');
            }
            else if(checkRights.Role_View === 1) {
                if(reportDetails['Retention_Period_Allowed']==='Yes')
                {
                    // validate the inputs
                    validationError=await inputValidation(args.filters,reportDetails['Retention_Period_In_Month']);
                }
                // Check validation error is exist or not
                if (Object.keys(validationError).length === 0) {
                    if(checkRights.Employee_Role && checkRights.Employee_Role.toLowerCase() === 'admin'){
                        isAdmin=1;
                    }
                    if(checkRights.Is_Manager===1)
                    {
                        isManager=1;
                    }
                    if(reportDetails['Report_Type']==='Asynchronous')
                    {
                        let insertHistoryResponse=await commonLib.func.insertIntoTable(organizationDbConnection,ehrTables.reportHistory,inserHistoryParams,1)
                        if(insertHistoryResponse && insertHistoryResponse.length>0)
                        {
                            let historyId=insertHistoryResponse[0];
                            // We will be triggering the step function to initiate the report generator function in background process.
                            let inputParams={args:args,reportDetails:reportDetails,logInEmpId:logInEmpId,orgCode:orgCode,isAdmin:isAdmin,isManager:isManager,historyId:historyId,requestDateTime:requestDateTime}
                            let triggerGenerateReportResponse= await commonLib.stepFunctions.triggerStepFunction(process.env.generateReportAsyncStepFunction,'generateReport','',inputParams);
                            console.log('Response after triggering generateReport step function',triggerGenerateReportResponse);
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return {errorCode:'',message: 'Report generator initiated successfully.',reportData:""};
                        }
                        else{
                            console.log("Error while inserting details in history table.");
                            throw('HBLC0110');
                        }
                    }
                    else{
                       let reportData= await callFunctionBasedOnInput(organizationDbConnection,args,reportDetails,logInEmpId,orgCode,isAdmin,isManager);
                       organizationDbConnection ? organizationDbConnection.destroy() : null;
                       return {errorCode:'',message: 'Report data retrieved successfully.',reportData:JSON.stringify(reportData)};
                    }
                }
                else{
                   console.log('Input validation error',validationError);
                   // throw validation error
                   throw 'IVE0000';
                }
            }
            else{
                console.log('User does not have view access to reports form');
                throw ('_DB0100');
            }
        }
        else{
            console.log('Error while fetching setings for report.');
            throw('HBLC0102');
        }
    }
    catch(mainCatchError)
    {
        console.log('Error in generateReportBasedOnInputs function main catch block.',mainCatchError);
        // destory dbconnection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        // check error based on that return response
        if (mainCatchError === 'IVE0000') {
            errResult = commonLib.func.getError(mainCatchError, 'IVE0000');
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        }else{
            errResult = commonLib.func.getError(mainCatchError, 'HBLC0101');
            // throw error response to UI
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}

async function getPrevRequestReportMaxDate(organizationDbConnection,reportId,loginEmpId,inputParams)
{
    try{
        return(
            organizationDbConnection(ehrTables.reportHistory)
            .max('Request_Date_Time as maxDate')
            .where('Rep_Id',reportId)
            .where('Requested_By',loginEmpId)
            .where('Req_Params',inputParams)
            .where('Report_Status','Success')
            .then(data=>{
                if(data.length>0)
                {
                    return  data[0]['maxDate'];
                }
                return "";
            })
            .catch(e=>{
                console.log("Error in getPrevRequestReportMaxDate function .catch block",e);
                return "";
            })
        )
    }
    catch(e)
    {
        console.log("Error in getPrevRequestReportMaxDate function main block",e);
        return "";
    }
}