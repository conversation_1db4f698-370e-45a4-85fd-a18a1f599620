// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../../common/appconstants');
const { fetchJobRoleDetails } = require('../../../common/commonFunction');

// variable declarations
let errResult ={};
let organizationDbConnection = '';

// resolver definition
const resolvers = {
    Query:{
        // function to retrieve all the active employee along with their details
        listAllEmployeeDetails:async (parent, args, context, info)=>{
            try{
                console.log('Inside listAllEmployeeDetails function');
                let filterList={};
                let orgCode=context.Org_Code;
                let logInEmpId = context.Employee_Id;
                let accessFormName = args.formName ? args.formName : formName.esop;
                let onlyActiveEmployees = args.onlyActiveEmployees == 0 ? args.onlyActiveEmployees : 1;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                // check whether employee have edit access for ESOP form
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , accessFormName, '', 'UI');  
                // check edit rights exist or not                 
                if(Object.keys(checkRights).length>0 && checkRights.Role_View === 1 ) {
                    if((accessFormName == formName.accreditation && checkRights.Employee_Role.toLowerCase() === 'admin') || accessFormName != formName.accreditation){
                    
                        // formation of filterJson based on which details need to be retrieved
                        filterList['departmentId']  = args.departmentId;
                        filterList['designationId'] = args.designationId;
                        filterList['empTypeId']     = args.empTypeId;
                        filterList['locationId']    = args.locationId;
                        filterList['workScheduleId']= args.workScheduleId;
                        /** We need all the employees so it is passed as empty array  */
                        filterList['employeeId']    = [];
                        let getUserDetails = [];
                        if(onlyActiveEmployees === 1){
                            // function to get active employeedetails based on filter condition
                            getUserDetails=await commonLib.func.getAllEmployeeDetails(organizationDbConnection,'getAllActiveEmployees',orgCode,process.env.domainName,process.env.region,process.env.hrappProfileBucket,filterList);
                        } else {
                            // function to get both active and inactive employeedetails based on filter condition
                            getUserDetails=await commonLib.func.getAllEmployeeDetails(organizationDbConnection,'getAllEmployees',orgCode,process.env.domainName,process.env.region,process.env.hrappProfileBucket,filterList);
                        }
                        // check whether record exist or not
                        if(getUserDetails.length>0)
                        {
                            // getUserDetails=fetchJobRoleDetails(getUserDetails, organizationDbConnection);
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode:'',message:'Employee list retrieved successfully',employeeList:getUserDetails};
                        }
                        else{
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode:'',message:'No employees found.',employeeList:[]};
                        }
                    }
                    else{
                        console.log('User does not have admin access to the '+accessFormName+' form');
                        throw ('_DB0109');
                    }
                }
                else{
                    console.log('User does not have view access to the '+accessFormName+' form');
                    throw ('_DB0100');
                }
            }
            catch(mainCatchError){
                console.log('Error in listAllEmployeeDetails function main catch block.', mainCatchError);
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                errResult = commonLib.func.getError(mainCatchError, 'EM0007');
                // return response
                throw new ApolloError(errResult.message,errResult.code);
            }
        }        
    }
};

exports.resolvers = resolvers;
