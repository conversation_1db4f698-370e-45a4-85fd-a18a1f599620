'use strict';
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make database connection
const knex = require('knex');
//Require moment package
const moment=require('moment');
//Require common function
const { getBatchNotificationDetails,updateOrgBatchNotificationStatus,sendEmailToHr }=require('../../../../common/batchNotificationCommonFunctions');
const { getPendingApprovalEmployeesOnNotice }=require('../../../../common/remindersCommonFunctions');
//Require file to access constant values
const { awsSesTemplates,defaultValues } = require('../../../../common/appconstants');


//Function to iterate the organization codes and get the resignation approval employee for the org code based on the configuration and send the reminder to respective employees
module.exports.sendResignationNotificationToHr = async (parent, args, context, info) => {
    console.log("Inside sendResignationNotificationToHr() function.");
    let appManagerDbConnection;
    let organizationDbConnection;
    let response;
    try{
        //Make database connection
        appManagerDbConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
        appManagerDbConnection=knex(appManagerDbConnection.AppManagerDb);
        let orgCodeList = await getBatchNotificationDetails(appManagerDbConnection,'empOnNotice','Hr_Email_Status',defaultValues.openEmailNotificationStatus);
        
        if(orgCodeList.length > 0){
            let currentDate = moment().tz('Asia/Kolkata').format('YYYY-MM-DD');
            let currentDateMinusTwo = moment(currentDate).subtract(2, 'days').format("YYYY-MM-DD");
            let updateParams = {};
            //Iterate for all the instances
            for(let i=0;i<orgCodeList.length;i++){
                updateParams = {};
                let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgCodeList[i]);
                
                //Make database connection
                let connection= await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCodeList[i].Org_Code,0,additionalHeaders);
                organizationDbConnection=knex(connection.OrganizationDb);
                //Get the resignation approval employee details
                let employeeOnNoticeDetails = await getPendingApprovalEmployeesOnNotice(organizationDbConnection,currentDateMinusTwo);
                if(employeeOnNoticeDetails.length > 0){
                    let emailInputs = {
                        orgCode: orgCodeList[i].Org_Code,
                        employeesList: employeeOnNoticeDetails,
                        htmlTableKeyName: 'emponnoticeemailtohr',
                        statusKey: 'Hr_Email_Status',
                        notificationForKey: 'empOnNotice',
                        commonNotificationTemplate: awsSesTemplates.commonNotification,
                        emailSubject: 'Outstanding approvals for employee resignation (Due on '+employeeOnNoticeDetails[0].displayResignationDate+')',
                        emailContent: 'Resignation approval is pending for the following employees',
                    };
                    let {finalResponse} = await sendEmailToHr(organizationDbConnection,appManagerDbConnection,emailInputs);
                    response = finalResponse;
                    //Destroy database connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                }else{
                    console.log('Resignation approval employee does not exist for the current date  for the org code - ',orgCodeList[i].Org_Code);
                    await updateOrgBatchNotificationStatus(appManagerDbConnection,orgCodeList[i].Org_Code,'Hr_Email_Status','Success','empOnNotice');
                    //Destroy database connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    response={
                        nextStep:'End',
                        message:'Email notification is not send as the resignation approval employees does not exist.'
                    };
                }
                //If the last organization is processed
                if(i===orgCodeList.length-1){
                    //Destroy database connection
                    appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                }
            }
        }else{
            console.log('Input org code list is empty');
            //Destroy database connection
            appManagerDbConnection ? appManagerDbConnection.destroy() : null;
            response={
                nextStep:'End',
                message:'No records found so quit the process.'
            }
        }
        return response;
    }catch(mainCatchError) {
        console.log('Error in the sendResignationNotificationToHr() function main catch block. ',mainCatchError);
        //Destroy database connection
        appManagerDbConnection ? appManagerDbConnection.destroy() : null;
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        response={
            nextStep:'End',
            message:'Error from sendResignationNotificationToHr.So quit the process.'
        }
        return response;
    }
};