// list the shift scheduling details for calendar view
module.exports.retrieveMonthlyCalenderShiftDetails = async (parent, args, context, info) => {
    console.log('Inside retrieveMonthlyCalenderShiftDetails() function');
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // require apollo errors
    const { ApolloError } = require('apollo-server-lambda');
    // require common constant files
    const { formName, roles } = require('../../../common/appconstants');
    // Organization database connection
    const knex = require('knex');
    // require common function
    const { getShiftDetails} = require('./weekOffCommonFunction');
    // require moment-timezone
    const moment = require('moment-timezone');

    let orgDb;
    try{
        // get db connection
        orgDb = knex(context.connection.OrganizationDb);
        // Assign variables
        let  { employeeId,shiftMonth } = args;
        //check employeeId and shiftMonth exists
        if(employeeId && shiftMonth){
            // variable declaration
            let response = {};
            let weekOffCount = 0;
            let shiftDates = [];
            let nonShiftDates = [];
            // Check form view access rights
            let checkRights = await commonLib.func.checkEmployeeAccessRights(orgDb, context.Employee_Id, formName.shiftScheduling, roles.roleView);

            // check if view access rights exit for the shift scheduling form
            if(checkRights){
                // get employee DOJ
                let dateOfJoin = await commonLib.func.getDateOfJoiningBasedOnEmpId(orgDb, employeeId);
                // append dateOfJoin with response
                response.dateOfJoin = dateOfJoin;
                // get employee exit date if they applied a resignation
                let exitDate = await commonLib.payroll.getEmployeeResignationDate(orgDb,employeeId);
                // append exitDate with response
                response.exitDate = exitDate;
                // get month and date
                let [month, year] = shiftMonth.split(',');
                // form filer start and end date based on the month chosen from the UI (Check payscycle type as well)
                let { Salary_Date, Last_SalaryDate } = await commonLib.func.getSalaryDay(context.Org_Code, orgDb, month, year);
                // get shiftDetails
                let shiftDetails = await getShiftDetails(employeeId, Salary_Date, Last_SalaryDate, orgDb);
                // append shiftDetails with response
                response.shiftDetails = shiftDetails;
                // get timeOff details
                let leaveResponse = await commonLib.employees.getLeaves(employeeId, Salary_Date, Last_SalaryDate,'shift-calendar',orgDb);
                // check compensatory_off exist for this
                let compOffResponse = await commonLib.employees.getCompensatoryOff(employeeId, Salary_Date, Last_SalaryDate, 'shift-calendar', orgDb);
                let timeOffDates = [];
                // iterate the leaveResponse and form timeoff dates into an array
                for (let timeOff of leaveResponse){
                    // Check shift start and end date difference
                    let startDate = moment(timeOff.Start_Date, 'YYYY-MM-DD');
                    let endDate = moment(timeOff.End_Date, 'YYYY-MM-DD');
                    // Get difference between 2 dates in days
                    let dateDiff = endDate.diff(startDate, 'days'); 
                    // check dateDiff
                    if(dateDiff === 0){
                        // push time timeoff details into an array because the date will be the business working day and not the week off or holiday.
                        timeOffDates.push(timeOff.Start_Date);
                    }else{
                        /** If date difference is greater than 0 then get the between dates by adding the one date with the start date and validate the 
                         * date is either leave date(business working day) or not and push the valid leave date in an array.
                        */
                        for (let day = 0; day <= dateDiff; day++) {
                            let leaveNextDate = moment(timeOff.Start_Date, "YYYY-MM-DD").add(day, 'days').format('YYYY-MM-DD');
                            let empLeaveCalculationDays = await commonLib.employees.getLeaveTypeLeaveCalculationDays(orgDb,timeOff.LeaveType_Id);
                            /** If the leave calculation days exist for a leave type then validate the date is leave date or not. Otherwise 
                             * we should not consider it as the leave date. */ 
                            if(Object.keys(empLeaveCalculationDays).length > 0){
                                let leaveDayCountForRequestDate = await commonLib.employees.getTotalDaysBasedOnLeaveCalculationDays(orgDb,employeeId,empLeaveCalculationDays,leaveNextDate,leaveNextDate);
                                /** If the leave calculation days for a leave type is 'All days of salary month' then the date can be considered as the leave.
                                 *  If the leave calculation days for a leave type is 'Business working day' then the date can be considered as the leave only 
                                 *  when the date is a business working day and it can be considered as the leave date. Or if the date is not a business working 
                                 *  day then date may be either week off or holiday and it should not be considered as the leave date.
                                */
                                if(leaveDayCountForRequestDate > 0){
                                    // push time timeoff details into an array
                                    timeOffDates.push(leaveNextDate);
                                }
                            }else{
                                console.log('Empty response from the getLeaveTypeCalculationDays() function, ',empLeaveCalculationDays,' while getting the leave calculation days for a leave-type id,',timeOff.LeaveType_Id);
                                throw('SS0155');//throw error occurred while fetching the leave calculation days for a leave type
                            }
                        }
                    }
                }
                // iterate the compOffResponse and get the date push it into timeOffDates array
                for(let compOff of compOffResponse){
                    // push time compOff details into a timeOffDates array
                    timeOffDates.push(compOff.Compensatory_Date);
                }
                // get weekoff count and week off details
                for(let shift of shiftDetails){
                    // push shift dates into an array
                    shiftDates.push(shift.Shift_Date);
                    // check timeoff date exist in the shift details
                    if (timeOffDates.includes(shift.Shift_Date) === true){
                        shift.Time_Off = 1;
                    }else{
                        shift.Time_Off = 0;
                    }
                    // check particular shift date assigned as week off or not
                    if (shift.Week_Off === 1){
                        // increment the weekoff count
                        weekOffCount ++;
                    }
                }
                // append weekOffCount with response
                response.weekOffCount = weekOffCount;
                let shiftMonthStartDate = '';
                let shiftMonthEndDate = '';
                // check DOJ is greater than Salary_Date the consider DOJ as start date
                if (new Date(dateOfJoin) > new Date(Salary_Date)) {
                    shiftMonthStartDate = dateOfJoin;
                } else {
                    shiftMonthStartDate = Salary_Date;
                }
                // check employee's resignation exit date is exist. If yes check exit date with the Last_SalaryDate.
                // If exit date is less than Last_SalaryDate then consider exit date as end date
                if (exitDate && (new Date(exitDate) < new Date(Last_SalaryDate))) {
                    shiftMonthEndDate = exitDate;
                } else {
                    shiftMonthEndDate = Last_SalaryDate
                }
                // get non scheduled shift dates based on the Salary_Date and Last_SalaryDate
                // Check shift start and end date difference
                shiftMonthStartDate = moment(shiftMonthStartDate, 'YYYY-MM-DD');
                shiftMonthEndDate = moment(shiftMonthEndDate, 'YYYY-MM-DD');
                // Get difference between 2 dates in days
                let totalDays = shiftMonthEndDate.diff(shiftMonthStartDate, 'days');           
                // if date difference is greater than 0 then get the between dates by adding the one date with the start date 
                for (let day = 0; day <= totalDays; day++) {
                    let tmpDate = moment(shiftMonthStartDate, "YYYY-MM-DD").add(day, 'days').format('YYYY-MM-DD');
                    //check if the date is exist in shiftDates array if yes then shift is scheduled for that date otherwise
                    // consider the dates as non shift dates
                    if(shiftDates.includes(tmpDate) === false){
                        // push non shift dates into an array
                        nonShiftDates.push(tmpDate);
                    }
                }
                // append nonShiftDates with response
                response.nonShiftDates = nonShiftDates;
                // return response to UI
                return { success: true, errorCode: '', message: 'Employee shift details retrieved successfully',shiftCalendarView: JSON.stringify(response)};        
            } else if (checkRights === false){
                // if view rights not exists
                let errResult = commonLib.func.getError('', '_DB0100');
                return {
                    success: false,
                    errorCode: errResult.code,
                    message: errResult.message
                }
            } else{
                console.log("Error while checking rights in retrieveMonthlyCalenderShiftDetails()", checkRights);
                let errResult = commonLib.func.getError('', 'SS0141');
                return {
                    success: false,
                    errorCode: errResult.code,
                    message: errResult.message
                }
            }
        }else{
            console.log("Error while validating the employeeId and shiftMonth in retrieveMonthlyCalenderShiftDetails() function.");
            let errResult = commonLib.func.getError('', 'SS0141');
            return {
                success: false,
                errorCode: errResult.code,
                message: errResult.message
            }
        }
    } catch (retrieveMonthlyCalenderShiftDetailsMainCatch){
        console.log('Error in retrieveMonthlyCalenderShiftDetails() function main catch block.',retrieveMonthlyCalenderShiftDetailsMainCatch);
        let errResult;
        if(retrieveMonthlyCalenderShiftDetailsMainCatch === 'SS0155'){
            errResult = commonLib.func.getError('', 'SS0155');
        }else{
            errResult = commonLib.func.getError('', 'SS0141');
        }
        throw new ApolloError(errResult.message, errResult.code)
    } finally {
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
    }
};