// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const commonInputValidation=require('./commonvalidation');
const moment = require('moment-timezone');

module.exports = { 
    addFormInputValidation : async(args,maxDateOfJoin,minResignationDate) =>{
        let validationError = {};
        // validate allocated share
        if(args.allocatedShare && (args.allocatedShare>0)){
            let validate = commonInputValidation.decimalValidation(args.allocatedShare);
            if (!validate) {
                validationError['IVE0159'] = commonLib.func.getError('', 'IVE0159').message;
            }
        }
        else{
            validationError['IVE0159'] = commonLib.func.getError('', 'IVE0159').message;
        }
        // validate allocated date
        if(!args.allocatedDate || new Date(args.allocatedDate) == "Invalid Date" || !moment(args.allocatedDate, 'YYYY-MM-DD').isValid){
            validationError['IVE0160'] = commonLib.func.getError('', 'IVE0160').message;
        }
        // validate allocated date with maximum date of join
        if(!moment(args.allocatedDate, 'YYYY-MM-DD').isSameOrAfter(maxDateOfJoin)){
            validationError['IVE0416'] = commonLib.func.getError('', 'IVE0416').message;
        }

        // validate allcoated date with minimum resignation date
        if(minResignationDate){
            if(!moment(args.allocatedDate, 'YYYY-MM-DD').isSameOrBefore(minResignationDate)){
                validationError['IVE0417'] = commonLib.func.getError('', 'IVE0417').message;
            }
        }

        // validate visibility
        if(args.visibility){
            if((args.visibility!=='Self') && (args.visibility!=='Super Admins')){
                validationError['IVE0161'] = commonLib.func.getError('', 'IVE0161').message;
            }
        }
        else{
            validationError['IVE0161'] = commonLib.func.getError('', 'IVE0161').message;
        }
        // validate employeeId
        if(!(args.employeeIds.length>0)){
            validationError['IVE0083'] = commonLib.func.getError('', 'IVE0083').message;
        }
        return validationError;
    },
    editFormInputValidation : async(args,context,process=null, maxDateOfJoin) =>{
        let validationError = {};
        // validate allocated shareId
        if(!args.allocatedShareId){
            validationError['IVE0163'] = commonLib.func.getError('', 'IVE0163').message;
        }
        /** Based on input process validate the inputs */
        if(process==='vest'){
            // validate vested date
            if(!args.vestedDate){
                validationError['IVE0165'] = commonLib.func.getError('', 'IVE0165').message;
            }
            // validate vested share
            if(args.vestedShare && (args.vestedShare>0)){
                let validate = commonInputValidation.decimalValidation(args.vestedShare);
                if (!validate) {
                    validationError['IVE0164'] = commonLib.func.getError('', 'IVE0164').message;
                }
            }
            else{
                validationError['IVE0164'] = commonLib.func.getError('', 'IVE0164').message;
            }
        }
        else{
            // validate allocated date
            if(!args.allocatedDate){
                validationError['IVE0160'] = commonLib.func.getError('', 'IVE0160').message;
            }
            // validate allocated share
            if(args.allocatedShare && (args.allocatedShare>0)){
                let validate = commonInputValidation.decimalValidation(args.allocatedShare);
                if (!validate) {
                    validationError['IVE0159'] = commonLib.func.getError('', 'IVE0159').message;
                }
            }
            else{
                validationError['IVE0159'] = commonLib.func.getError('', 'IVE0159').message;
            }
        }
        return validationError;
    }
};
