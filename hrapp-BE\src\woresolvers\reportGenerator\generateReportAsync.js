//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
const {callFunctionBasedOnInput}=require('./reportCommonFunction')

// Function to initiate generateReportAsync step function
module.exports.generateReportAsync  = async(args, context) =>{
    console.log('Inside generateReportAsync function');
    let organizationDbConnection,appmanagerDbConnection;
    try{
        let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
        // check whether data exist or not
        if(Object.keys(databaseConnection).length){
            // form app manager database connection
            appmanagerDbConnection=knex(databaseConnection.AppManagerDb);
            let orgCode=args.orgCode;
            let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appmanagerDbConnection,orgCode);
            appmanagerDbConnection?appmanagerDbConnection.destroy():null;
            if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
                let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
                //Get database connection
                let connection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCode,0,additionalHeaders);
                if(Object.keys(connection).length>0){
                    organizationDbConnection = knex(connection.OrganizationDb);
                    await callFunctionBasedOnInput(organizationDbConnection,args.args,args.reportDetails,args.logInEmpId,args.orgCode,args.isAdmin,args.isManager,args.historyId,args.requestDateTime);
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                }
            }
        }
        else{
            console.log("Error while getting database connection.")
        }
    }
    catch(mainCatchError){
        console.log('Error in generateReportAsync function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        appmanagerDbConnection?appmanagerDbConnection.destroy():null;
    }
};