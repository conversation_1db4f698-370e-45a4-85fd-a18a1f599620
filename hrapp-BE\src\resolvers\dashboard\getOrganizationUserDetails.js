// require knex for database connection
const knex = require('knex');
// require aws package
const AWS = require('aws-sdk')
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require table alias list
const tables = require('../../../common/tablealias');
const { CommonLib } = require('@cksiva09/hrapp-corelib');
// Create object for s3 bucket
const s3 = new AWS.S3({ region: process.env.region });

// variable declaration
let ehrTables = tables.ehrTables;
let organizationDb = '';
let employeeName = '';
let productIconPath = '';
let employeeProfile = '';

// resolver definition
const resolvers = {
    Query: {
        // function to get the user and organization details for presenting info in dashboard header
        getOrganizationUserDetails: async (parent, args, context, info) => {
            console.log('Inside the getOrganizationUserDetails() function.');
            // get employeeId from context object
            let employeeId = context.Employee_Id;
            try {
                // get the organization data base connection
                organizationDb = knex(context.connection.OrganizationDb);

                // Function to get organization name based on orgcode
                let { orgName, orgDateFormat, assessmentYear, paycycle, disableLogout, camuBaseUrl,allowConcatenation, Consider_Cutoff_Days_For_Attendance_And_Timeoff, employeeEdit, fieldForce, restrictEmpAccessForManager, autoUpdateEffectiveDateForJobDetails, payRollIntegrationUrl } = await getOrganizationName(organizationDb, context.Org_Code);

                //Partner integration for the org code
                let partnerIntegration = context.partnerid;

                // function to get whether organization settings have consent/stealth mode of app tracking and privacy mode enabled or not
                // Fetch settings in parallel for better performance
                const [
                    { monitoringMode, privacyMode },
                    orgLocationDetails,
                    serviceProviderDetail,
                    uiFeatureSettings,
                    closureMonthJson,
                    entomoSyncType,
                    employeeSettings
                ] = await Promise.all([
                    employeeMonitorSettings(organizationDb),
                    CommonLib.func.getOrgAddress(organizationDb, employeeId),
                    getServiceProviderDetails(organizationDb, employeeId, fieldForce),
                    getFeatureSettings(organizationDb),
                    getClosureMonthJson(organizationDb),
                    getEntomoSettings(organizationDb),
                    getEmployeeSettings(organizationDb)
                ]);


                return (
                    organizationDb
                        .transaction(function (trx) {
                            // retrieve the user details from emp personal info table based on emp status in emp_job table
                            return (
                                organizationDb(ehrTables.empPersonalInfo + ' as EP')
                                    .select('EP.Photo_Path', 'EP.Emp_First_Name', organizationDb.raw("CONCAT_WS(' ',EP.Emp_First_Name,EP.Emp_Middle_Name, EP.Emp_Last_Name) as Employee_Full_Name"),
                                        'EP.Emp_Last_Name', 'EP.Employee_Id', 'EJ.User_Defined_EmpId',' EP.Language_Preference as languagePreference', 'EJ.Emp_Email as employeeEmail', 'EJ.Roles_Id', 'DES.Designation_Name')
                                    .innerJoin(ehrTables.empJob + ' as EJ', 'EP.Employee_Id', 'EJ.Employee_Id')
                                    .leftJoin(ehrTables.designation + ' as DES', 'EJ.Designation_Id', 'DES.Designation_Id')
                                    .where('EJ.Emp_Status', 'Active')
                                    .andWhere('EP.Employee_Id', employeeId)
                                    .transacting(trx)
                                    .then(async (getUserData) => {
                                        let camuBaseUrlResult = (fieldForce === 1 && partnerIntegration && partnerIntegration.toLowerCase() === 'camu') ? serviceProviderDetail.camuBaseUrl : camuBaseUrl;
                                        let payRollIntegrationUrlResult = payRollIntegrationUrl;
                                        // Check whether data exist or not
                                        if (getUserData.length > 0) {
                                            employeeName = getUserData[0].Emp_First_Name;
                                            let photoPath = getUserData[0].Photo_Path;
                                            // get presigned url for employee profile
                                            employeeProfile = (photoPath) ? await getEmployeeProfile(photoPath, context.Org_Code) : '';
                                            // return response
                                            return {
                                                errorCode: '',
                                                message: 'Organization and user details retrieved successfully.',
                                                organizationName: orgName,
                                                serviceProviderName: serviceProviderDetail.serviceProviderName,
                                                orgDateFormat: orgDateFormat,
                                                entomoSyncType: entomoSyncType,
                                                employeeSettings: employeeSettings,
                                                assessmentYear: assessmentYear,
                                                paycycle: paycycle,
                                                productIconPath: productIconPath,
                                                employeeId: employeeId,
                                                monitoringType: monitoringMode,
                                                privacyMode: privacyMode,
                                                allowConcatenation: allowConcatenation,
                                                disableLogout: disableLogout,
                                                camuBaseUrl: camuBaseUrlResult,
                                                payRollIntegrationUrl: payRollIntegrationUrlResult,
                                                advancePayroll: Consider_Cutoff_Days_For_Attendance_And_Timeoff,
                                                employeeEdit: employeeEdit,
                                                fieldForce: fieldForce,
                                                closureMonthJson: closureMonthJson,
                                                restrictEmpAccessForManager: restrictEmpAccessForManager,
                                                autoUpdateEffectiveDateForJobDetails: autoUpdateEffectiveDateForJobDetails,
                                                uiFeatureSettings: uiFeatureSettings,
                                                userDetails: {
                                                    employeeId: getUserData[0].Employee_Id,
                                                    userDefinedEmployeeId: getUserData[0].User_Defined_EmpId,
                                                    employeeFullName: getUserData[0].Employee_Full_Name,
                                                    employeeFirstName: employeeName,
                                                    employeePhotoPath: employeeProfile,
                                                    employeeEmail: getUserData[0].employeeEmail,
                                                    languagePreference: getUserData[0].languagePreference,
                                                    employeeLastName: getUserData[0].Emp_Last_Name,
                                                    designationName: getUserData[0].Designation_Name,
                                                    rolesId: getUserData[0].Roles_Id
                                                },
                                                organization: {
                                                    street1: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.Street1 : '',
                                                    street2: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.Street2 : '',
                                                    city: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.City_Name : '',
                                                    state: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.State_Name : '',
                                                    country: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.Country_Name : '',
                                                    pincode: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.Pincode : ''
                                                },
                                                serviceProvider: {
                                                    street1: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.Street1 : '',
                                                    street2: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.Street2 : '',
                                                    city: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.City_Name : '',
                                                    state: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.State_Name : '',
                                                    country: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.Country_Name : '',
                                                    pincode: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.Pincode : ''
                                                }
                                            }
                                        }
                                        else {
                                            // in case of error we need to return the remaining information to UI so success response is returned
                                            console.log("User data not found");
                                            return {
                                                errorCode: '',
                                                message: 'Organization and user details retrieved successfully.',
                                                organizationName: orgName,
                                                serviceProviderName: serviceProviderName,
                                                orgDateFormat: orgDateFormat,
                                                entomoSyncType: entomoSyncType,
                                                employeeSettings: employeeSettings,
                                                assessmentYear: assessmentYear,
                                                paycycle: paycycle,
                                                productIconPath: productIconPath,
                                                employeeId: employeeId,
                                                monitoringType: monitoringMode,
                                                privacyMode: privacyMode,
                                                disableLogout: disableLogout,
                                                camuBaseUrl: camuBaseUrlResult,
                                                payRollIntegrationUrl: payRollIntegrationUrlResult,
                                                advancePayroll: advancePayroll,
                                                employeeEdit: employeeEdit,
                                                fieldForce: fieldForce,
                                                autoUpdateEffectiveDateForJobDetails: autoUpdateEffectiveDateForJobDetails,
                                                userDetails: {},
                                                uiFeatureSettings: uiFeatureSettings,
                                                organization: {
                                                    street1: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.Street1 : '',
                                                    street2: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.Street2 : '',
                                                    city: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.City_Name : '',
                                                    state: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.State_Name : '',
                                                    country: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.Country_Name : '',
                                                    pincode: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.Pincode : ''
                                                },
                                                serviceProvider: {
                                                    street1: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.Street1 : '',
                                                    street2: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.Street2 : '',
                                                    city: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.City_Name : '',
                                                    state: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.State_Name : '',
                                                    country: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.Country_Name : '',
                                                    pincode: orgLocationDetails && orgLocationDetails.length > 0 ? orgLocationDetails[0]?.Pincode : ''
                                                }
                                            }
                                        }
                                    })
                                    // catch the errors
                                    .catch(function (err) {
                                        console.log('Error in retrieve user details', err);
                                        throw 'DB0002';
                                    })
                            );
                        })
                        .then(function (result) {
                            return result;
                        })
                        //catch db-connectivity errors
                        .catch(function (catchErrror) {
                            console.log('Error in retrieve user details catch block', catchErrror);
                            // get the code and message from common function based on returned error code
                            let errResult = commonLib.func.getError(catchErrror, 'DB0003');
                            // return response
                            throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, organizationName: '', orgDateFormat: '', assessmentYear: '', productIconPath: '', paycycle: '', employeeId: employeeId, monitoringType: '', privacyMode: '', disableLogout: 'No', userDetails: {} }));
                        })
                        /**close db connection */
                        .finally(() => {
                            organizationDb.destroy();
                        })
                );
            } catch (mainCatchError) {
                console.log('Error in getting organization and user details main catch block', mainCatchError);
                // destroy database connection
                organizationDb ? organizationDb.destroy() : null;
                // get the code and message from common function based on returned error code
                let errResult = commonLib.func.getError(mainCatchError, 'DB0003');
                // return response
                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, organizationName: '', orgDateFormat: '', assessmentYear: '', productIconPath: '', paycycle: '', employeeId: employeeId, monitoringType: '', privacyMode: '', userDetails: {} }));
            }
        }
    }
};

/**
 * Retrieves employee settings from the organization's database connection.
 *
 * @async
 * @function
 * @param {Object} organizationDbConnection - The database connection instance for the organization.
 * @returns {Promise<Object>} A promise that resolves to an object containing the employee settings with the following properties:
 *   - Enable_Workflow_Profile: A string indicating whether to enable employee profile workflow or not.
 *   - Enable_Workflow_Team_Summary: A string indicating whether to enable team summary workflow or not.
 */

async function getEmployeeSettings(organizationDbConnection) {
    try {
        let settings = await organizationDbConnection(ehrTables.employeeSettings)
            .select('Enable_Workflow_Profile', 'Enable_Workflow_Team_Summary')
            .first();
        if (!settings) {
            return { Enable_Workflow_Profile: 'No', Enable_Workflow_Team_Summary: 'No' }
        }
        return settings;
    }
    catch (error) {
        console.log('Error in getEmployeeSettings function main catch block', error);
        return { Enable_Workflow_Profile: 'No', Enable_Workflow_Team_Summary: 'No' }
    }
}

/**
 * Retrieves UI feature toggle settings from the organization's database connection.
 *
 * @async
 * @function
 * @param {Object} organizationDbConnection - The database connection instance for the organization.
 * @returns {Promise<Array<{ Element_Name: string, Is_Enabled: boolean }>>} 
 *   A promise that resolves to an array of feature toggle settings, each containing the element name and its enabled status.
 */
async function getFeatureSettings(organizationDbConnection) {
    try {
        let settings = await organizationDbConnection(ehrTables.uiFeatureToggleSettings)
                        .select('Element_Name', 'Is_Enabled')
        
        return settings;
    }
    catch (error) {
        console.log('Error in getFeatureSettings function main catch block', error);
        return []
    }
}

// Function to get organization name from database
// handle the error as success so that we can return the remaining data to UI
async function getOrganizationName(organizationDb, orgCode) {
    try {
        // fucntion to get organization name and date format based on orgcode from org details table
        return await organizationDb(ehrTables.orgDetails)
            .select('Org_Name as orgName', 'Date_Format as orgDateFormat','allow_concatenation as allowConcatenation','Assessment_Year as assessmentYear', 'Paycycle as paycycle', 'Disable_Logout as disableLogout', 'Camu_Base_Url as camuBaseUrl', 'Consider_Cutoff_Days_For_Attendance_And_Timeoff', 'Employee_Edit as employeeEdit', 'Field_Force as fieldForce', 'Restrict_Emp_Access_For_Manager as restrictEmpAccessForManager',
                'Auto_Update_Effective_Date_For_Job_Details as autoUpdateEffectiveDateForJobDetails', 'Payroll_Integration_Url as payRollIntegrationUrl')
            .where('Org_Code', orgCode)
            .then((getOrgData) => {
                if (getOrgData[0]) {
                    return getOrgData[0];
                }
                else {
                    return { orgName: null, orgDateFormat: null, assessmentYear: null, paycycle: null, disableLogout: 'No', fieldForce: 0, restrictEmpAccessForManager: 0}
                }
            })
    }
    catch (error) {
        console.log('Error in getOrganizationName function catch block', error);
        return { orgName: null, orgDateFormat: null, assessmentYear: null, paycycle: null, disableLogout: 'No' }
    }
}

// function to get employee profile path
// handle the error as success so that we can return the remaining data to UI
async function getEmployeeProfile(photoPath, orgCode) {
    try {
        return new Promise(async function (resolve, reject) {
            // formation of s3 employee profile path
            let userFilePath = photoPath ? await commonLib.func.formS3FilePath(photoPath, orgCode, 'profile', '', process.env.domainName) : '';
            // get the employee profile picture signed url
            let empSignedUrl = userFilePath ? await commonLib.func.getFileURL(process.env.region, process.env.hrappProfileBucket, userFilePath) : '';
            resolve(empSignedUrl);
        });
    }
    catch (profileError) {
        console.log("Error in getEmployee profile catch block", profileError);
        return '';
    }
}

// Function to get product icon path
// handle the error as success so that we can return the remaining data to UI
async function getProductIcon() {
    return new Promise(async function (resolve, reject) {
        try {
            // invoke function to form product icon path
            let formedProductIconPath = await commonLib.func.formS3FilePath("", '', 'productlogo', '', process.env.domainName);
            // check whether path returned or not
            if (formedProductIconPath) {
                let s3LogoPath = 'https://s3.' + process.env.region + '.amazonaws.com';
                // form the s3 product logo path
                let s3ProductPath = s3LogoPath + "/" + process.env.logoBucket + "/" + formedProductIconPath;
                // check whether the logo exist in s3 bucket or not
                let existence = await s3.headObject({ Bucket: process.env.logoBucket, Key: formedProductIconPath }).promise();
                resolve(s3ProductPath);
            }
            else {
                console.log("Error in formation of product icon path")
                resolve('');
            }
        }
        catch (getLogoError) {
            console.log('Error in getProductIcon catch block', getLogoError);
            resolve('');
        }
    });
}

// function to get whether activity tracker is in consent/stealth mode & privacy mode enabled or not
async function employeeMonitorSettings(organizationDb) {
    let privacyMode = '';
    let monitoringMode = '';
    try {
        return (
            organizationDb
                .select('Monitoring_Mode', 'Privacy')
                .from(ehrTables.employeeMonitorSettings)
                .then(getData => {
                    if (getData.length > 0) {
                        monitoringMode = getData[0].Monitoring_Mode;
                        privacyMode = getData[0].Privacy;
                    }
                    return { monitoringMode, privacyMode };
                })
                .catch(function (catchError) {
                    console.log('Error in employeeMonitorSettings function .catch block', catchError);
                    return { monitoringMode, privacyMode };
                })
        );
    }
    catch (error) {
        console.log('Error in employeeMonitorSettings function main catch block', error);
        return { monitoringMode, privacyMode };
    }
};

/*
 // Get organization address
 async function getOrgAddress(organizationDbConnection, locationId) {
    try{
        return(
            organizationDbConnection(ehrTables.location)
            .select('location.Street1', 'location.Street2', 'city.City_Name', 'state.State_Name', 'country.Country_Name', 'location.Pincode')
            .leftJoin(ehrTables.country, 'location.Country_Code','country.Country_Code')
            .leftJoin(ehrTables.state, 'location.State_Id','state.State_Id')
            .leftJoin(ehrTables.city, 'location.City_Id', 'city.City_Id')
            .where(qb => {                        
                if (locationId){
                    qb.where('location.Location_Id', locationId);
                }else{
                    qb.where('location.Location_Type','MainBranch');
                }
            })

            .then(orgDetails =>{                
                // return response
                return orgDetails && orgDetails.length > 0 ?  orgDetails[0] : {} ;  
            })
        )
    }catch(error){
        console.log('Error in getOrgAddress function main catch block',error);
        return {};
    }
} */

async function getServiceProviderDetails(organizationDbConnection, employeeId, fieldForce) {

    try {
        if (fieldForce === 1) {
            return (organizationDbConnection(ehrTables.empJob)
                .select('service_provider.Service_Provider_Name', 'service_provider.Location_Id', 'service_provider.Camu_Base_Url')
                .leftJoin(ehrTables.serviceProvider, 'service_provider.Service_Provider_Id', 'emp_job.Service_Provider_Id')
                .where('emp_job.Employee_Id', employeeId)
                .then(async serviceProvider => {
                    // return response
                    return {
                        serviceProviderName: serviceProvider[0]?.Service_Provider_Name,
                        camuBaseUrl: serviceProvider[0]?.Camu_Base_Url,
                    };
                }));
        } else {
            return {
                serviceProviderName: '',
                camuBaseUrl: ''
            }
        }
    } catch (error) {
        console.log('Error in getServiceProviderDetails function main catch block', error);
        return { serviceProviderName: '', camuBaseUrl: '' }
    }

}

exports.resolvers = resolvers;


async function getClosureMonthJson(organizationDbConnection) {
    try {
        const orgDetails = await organizationDbConnection('org_details')
            .select('Assessment_Year')
            .first();

        if (!orgDetails || !orgDetails.Assessment_Year) {
            console.log('Assessment year not found in org_details');
            return {};
        } else {
            const assessmentYear = orgDetails.Assessment_Year;

            const months = {
                1: "01", 2: "02", 3: "03", 4: "04", 5: "05", 6: "06",
                7: "07", 8: "08", 9: "09", 10: "10", 11: "11", 12: "12"
            };

            const monthYear = [];
            const monthYearPrev = [];

            for (let i = 1; i <= 12; i++) {
                if (i <= 3) {
                    monthYear[i] = `${months[i]},${assessmentYear}`;
                } else {
                    monthYearPrev[i] = `${months[i]},${assessmentYear - 1}`;
                }
            }

            const monthYearCombine = { ...monthYearPrev, ...monthYear };

            return Object.values(monthYearCombine);
        }
    } catch (error) {
        console.log('Error in getClosureMonthJson function', error);
        return {};
    }
}

async function getEntomoSettings(organizationDbConnection) {
    try {
        const entomoSettings = await organizationDbConnection(ehrTables.externalApiCredentials)
            .select('Sync_Type')
            .where('Integration_Type', 'Entomo')
            .first();

        if (!entomoSettings || !entomoSettings.Sync_Type) {
            console.log('Entomo sync type not found in entomo_settings');
            return null;
        } else {
            return entomoSettings.Sync_Type;
        }
    } catch (error) {
        console.log('Error in getEntomoSettings function', error);
        return null;
    }
}

