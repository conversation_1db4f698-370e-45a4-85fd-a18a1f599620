module.exports.getShiftSummary = async (parent, args, context, info)=>{
    console.log('Inside getShiftSummary() function');
    // require apollo errors
    const { ApolloError } = require('apollo-server-lambda');
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // Organization database connection
    const knex = require('knex');
    // require table names
    const { ehrTables } = require('../../../common/tablealias');
    //get db connection
    const orgDb = knex(context.connection.OrganizationDb);
    try{
        // check shiftMonth exist or not
        if(args.shiftMonth){
            let shiftEmployeeCount = '',weekOffEmployeeCount = '';

            let shiftMonth = args.shiftMonth;
            let [month, year] = shiftMonth.split(',');// get month and year

            // form filer start and end date based on the month chosen from the UI (Check payscycle type as well)
            let { Salary_Date, Last_SalaryDate } = await commonLib.func.getSalaryDay(context.Org_Code, orgDb, month, year);
            // get active shift type counts
            let activeShifts =  await orgDb(ehrTables.empShiftType)
                                .count('Shift_Type_Id as shiftCount')
                                .where('Status','Active')
                                .then(shiftTypes => {
                                    // return response
                                    return shiftTypes[0] ? shiftTypes[0].shiftCount : 0;
                                })
                                .catch(getShiftCountError =>{
                                    console.log('Error while getting the active shift counts', getShiftCountError);
                                    // return response
                                    return 0;
                                });

            // get active/inactive roster employee count at the admin level for the filter month by sending 'isAdmin' as 1 and the filter month
            let rosterEmployees = await commonLib.func.getAllEmployeeDetails(orgDb, 'getRosterEmployees', context.Org_Code,
                                    process.env.domainName, process.env.region, process.env.hrappProfileBucket, { filterMonth:shiftMonth,isAdmin: 1 }, null);
            
            // Get array count
            let rosterEmployeeCount = rosterEmployees.length;
            if(rosterEmployeeCount > 0){
                //Some of the employees work schedule might be changed. So get the shift roster employee ids to fetch shift employees count and week off count
                let allRosterEmployeeIds = rosterEmployees.map(employeeDetails=>
                    employeeDetails.employee_id);
                // form subQuery to get shiftschedule employee count only for the shift roster employees
                let subQuery = orgDb(ehrTables.shiftEmpMapping)
                                .distinct('Employee_Id')
                                .whereIn('Employee_Id',allRosterEmployeeIds)
                                .where((qb) => {
                                    if (Salary_Date && Last_SalaryDate) {
                                        qb.whereBetween('Shift_Start_Date', [Salary_Date, Last_SalaryDate])
                                        qb.orWhereBetween('Shift_End_Date', [Salary_Date, Last_SalaryDate])
                                    }
                                });
                // get shift scheduled employee count
                shiftEmployeeCount = await subQuery
                                        .then(getshiftEmpCount =>{
                                            // return respone
                                            return getshiftEmpCount[0] ? getshiftEmpCount.length : 0;
                                        })
                                        .catch(getshiftEmpCountError => {
                                            console.log('Error while getting the active shift count.', getshiftEmpCountError);
                                            // return response
                                            return 0;
                                        });
                // form response
                shiftEmployeeCount = shiftEmployeeCount + '/' + rosterEmployeeCount; // response will be 100/120
                // get week off employee count
                weekOffEmployeeCount =  await subQuery
                                            .where('Week_Off',1)
                                            .then(getWeekOffCount => {
                                                // return respone
                                                return getWeekOffCount[0] ? getWeekOffCount.length : 0;
                                            })
                                            .catch(getWeekOffCountError => {
                                                console.log('Error while getting the week off employee count.', getWeekOffCountError);
                                                // return response
                                                return 0;
                                            });
                // form response
                weekOffEmployeeCount = weekOffEmployeeCount + '/' + rosterEmployeeCount;  // response will be 10/95
            }else{
                console.log('No shift roster employee(s) found.')
                shiftEmployeeCount = 0;
                weekOffEmployeeCount = 0;
            }

            // return response back to UI
            return { success: true, errorCode: "", message: "Employee shift summary retrieved successfully", activeShifts: activeShifts, 
                shiftScheduledEmployees: shiftEmployeeCount, weekOffAssignedEmployees: weekOffEmployeeCount, salaryStartDate: Salary_Date, salaryEndDate:Last_SalaryDate }
        }else{
            // throw error if shiftMonth is not exist
            let errResult = commonLib.func.getError('', 'SS0150');
            return {
                success: false,
                errorCode: errResult.code,
                message: errResult.message
            }
        }        
    }catch(getShiftSummaryMainCatch){
        console.log('Error in getShiftSummary() function main catch block.',getShiftSummaryMainCatch);
        let errResult = commonLib.func.getError(getShiftSummaryMainCatch, 'SS0150');
        throw new ApolloError(errResult.message, errResult.code)
    }
};