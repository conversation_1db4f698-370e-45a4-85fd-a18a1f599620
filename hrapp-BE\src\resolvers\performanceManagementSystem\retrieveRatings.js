// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../../common/appconstants');
// require table alias
const { ehrTables } = require('../../../common/tablealias');
// require pms common functions
const {getRatings} = require('../../../common/performanceManagementCommonFunction');

// variable declarations
let errResult = {};
let organizationDbConnection = '';

// resolver definition
const resolvers = {
    Query: {
        // function to retrieve the performance ratings
        retrieveRatings: async (parent, args, context, info) => {
            try{
                console.log('Inside retrieveRatings function');
                // get the organization data base connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                let logInEmpId = context.Employee_Id;
                // check whether the loggedIn employee have view access to goals and achievement form
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.goalsAndAchievement, '', 'UI');
                if ((Object.keys(checkRights).length >0 && checkRights.Role_View===1)) {
                    return(
                        organizationDbConnection(ehrTables.performanceManagementSettings)
                        .select('Maximum_Rating as maximumRating')
                        .then(async(getDetails) => {
                            if(getDetails.length>0){
                                let ratingDescription=await getRatings(organizationDbConnection);
                                getDetails[0]['ratingDetails']=ratingDescription;
                            }
                            // destroy database connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return {errorCode:'',message:'Ratings retrieved successfully.',getRatingDetails:(getDetails.length>0)?getDetails[0]:[]};
                        })
                        .catch(catchError => {
                            console.log('Error in retrieveRatings function .catch block.', catchError);
                            // destroy database connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            errResult = commonLib.func.getError(catchError, 'EPM0109');
                            // throw error response to UI
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                    )
                }
                else{
                    // throw if employee does not have access
                    throw ('_DB0100');
                }
            }catch(mainCatchError){
                console.log('Error in retrieveRatings function main catch block',mainCatchError);
                errResult = commonLib.func.getError(mainCatchError, 'EPM0009');
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                // return response
                throw new ApolloError(errResult.message,errResult.code);
            }
        }
    }
};
exports.resolvers = resolvers;
