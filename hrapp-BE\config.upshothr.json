{"securityGroupIds": ["sg-04bc41f567ae532b6"], "subnetIds": ["subnet-0cbbb2391d86f1d0e", "subnet-0120a712c5936a292"], "dbSecretName": "PROD/UPSHOT/PGACCESS", "region": "eu-west-2", "hrappProfileBucket": "s3.images.upshothr.uk", "lambdaRole": "arn:aws:iam::327313496531:role/lambdaFullAccess", "dbPrefix": "upshothr_", "domainName": "upshothr", "authorizerARN": "arn:aws:lambda:eu-west-2:327313496531:function:ATS-upshothr-firebaseauthorizer", "customDomainName": "api.upshothr.uk", "logoBucket": "s3.logos.upshothr.uk", "screenshotsBucket": "s3.hrapp.prod.employeemonitoring", "emailFrom": "<EMAIL>", "sesRegion": "eu-west-2", "firebaseApiKey": "AIzaSyCw5pu5_1SEdhJm__K-kHWMdzQbZi4tEs4", "webAddress": ".uk", "desktopClientURL": "", "asynchronousreportBucket": "asynchronousreport.upshothr.uk", "generateReportAsyncStepFunction": "arn:aws:states:eu-west-2:327313496531:stateMachine:upshothr-generateReportAsyncStepFunction", "updateRosterLeaveBatch": "arn:aws:lambda:eu-west-2:327313496531:function:BATCHPROCESSING-upshothr-updateRosterLeaveBatch", "resourceArnPrefix": "arn:aws:lambda:eu-west-2:327313496531:function:HRAPPBACKEND-upshothr"}