// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex to make DB connection
const knex = require('knex');
// require apollo server to return error message
const { UserInputError,ApolloError } = require('apollo-server-lambda');
// require table alias
const { appManagerTables } = require('../../../common/tablealias');

// list the help URL/Text applicable for the login employee based on the module id
module.exports.listHelpUrlAndText = async (parent, args, context, info) => {
    console.log("Inside listHelpUrlAndText() function.");
    let appManagerDbConnection;
    let errResult;
    let validationError={};
    try{
        let booleanValuesList = ['Yes','No'];
        if(!args.moduleId || args.moduleId<=0){
            validationError['IVE0003'] = commonLib.func.getError('', 'IVE0003').message2;
        }

        if(!(booleanValuesList.includes(args.applicableForEmployee))){
            validationError['IVE0177'] = commonLib.func.getError('', 'IVE0177').message;
        }

        if(!(booleanValuesList.includes(args.applicableForManager))){
            validationError['IVE0178'] = commonLib.func.getError('', 'IVE0178').message;
        }

        if(!(booleanValuesList.includes(args.applicableForAdmin))){
            validationError['IVE0179'] = commonLib.func.getError('', 'IVE0179').message;
        }

        if(args.applicableForEmployee === 'No' && args.applicableForManager === 'No' &&
        args.applicableForAdmin === 'No'){
            validationError['IVE0180'] = commonLib.func.getError('', 'IVE0180').message;
        }
        // check whether there is no validation error
        if(Object.keys(validationError).length ===0){
            appManagerDbConnection = knex(context.connection.AppManagerDb);
            let helpDetailsQuery = appManagerDbConnection(appManagerTables.helpDetails+' as HD')
            .select('HD.Title','HD.Type','HD.Url','HD.Text','HD.Embed_Url')
            .where('HD.Module_Id',args.moduleId);
            /** Based on the access form the respective where condition. This condition is formed in order
             * to avoid retrieving the help details which are not applicable for the login employee. */
            if(args.applicableForAdmin==='Yes'){
                if(args.applicableForManager==='Yes'){
                    helpDetailsQuery = helpDetailsQuery.where(qb=>{
                        qb.where('HD.Applicable_For_Admin',args.applicableForAdmin)
                        qb.orWhere('HD.Applicable_For_Manager',args.applicableForManager)
                    })
                }else{
                    helpDetailsQuery = helpDetailsQuery.where('HD.Applicable_For_Admin',args.applicableForAdmin);
                }
            }else if(args.applicableForManager==='Yes'){
                helpDetailsQuery = helpDetailsQuery.where('HD.Applicable_For_Manager',args.applicableForManager);
            }else{
                helpDetailsQuery = helpDetailsQuery.where('HD.Applicable_For_Employee',args.applicableForEmployee);
            }
            // retrieve the help URL/text applicable for the login employee based on the module id and the access
            return(
                helpDetailsQuery
                .then((helpDetails)=>{
                    // destroy DB connection
                    appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                    return { errorCode:'',message:'Help url and text retrieved successfully.',helpDetails:(helpDetails.length>0)?helpDetails:[]};
                })
                .catch(catchError => {
                    console.log('Error in listHelpUrlAndText function .catch block',catchError);
                    errResult = commonLib.func.getError(catchError, '_DB0110');
                    // destroy DB connection
                    appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                    // return error response
                    throw new ApolloError(errResult.message,errResult.code);              
                })
            );
        }
        else{
            throw 'IVE0000';
        }
    }catch(listHelpUrlAndTextMainCatchErr) {
        console.log('Error in the listHelpUrlAndText() function main catch block. ',listHelpUrlAndTextMainCatchErr);
        // destroy DB connection
        appManagerDbConnection ? appManagerDbConnection.destroy() : null;
        if (listHelpUrlAndTextMainCatchErr === 'IVE0000') {
            console.log('Validation error in the listHelpUrlAndText() function. ',validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            // return error response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else{
            errResult = commonLib.func.getError(listHelpUrlAndTextMainCatchErr, '_EC0004');
            // return error response
            throw new ApolloError(errResult.message,errResult.code);
        }
    }
};