# defining custom data type
scalar Date

type Query {
  listProbationEmployees(isTeamDashboard: Int!): listProbationEmployeesResponse!
}

type listProbationEmployeesResponse {
  errorCode: String
  message: String
  probationEmployeesDetails: [probationEmployeesDetails]
}

type probationEmployeesDetails {
  employeeId: Int
  userDefinedEmployeeId: String
  employeeName: String
  dateOfJoin: String
  probationDate: String
  probationDays: Int
}

type Mutation {
  approveMathchingSwapRequest(
    Swap_ID: Int!
    Matching_Swap_ID: Int!
):commonResponse!
  updateProbationAndConfirmation(
    employeeId: Int!
    dateOfJoin: Date!
    oldProbationDate: Date!
    newProbationDate: Date!
    confirmationDate: Date
  ): commonResponse!
  addUpdateEmployeeShiftSwap(
  employeeId: Int!
  approverId: Int!
  swapShiftTypeId: Int!
  swapDate: String!
  reason: String!
  status: String!
  swapId: Int
  ):commonResponse!
  updateGoalStatus(goalId: Int!, goalStatus: String!): commonResponse
  deleteEmployeeGoalsAndAchievement(
    performanceAssessmentId: Int!
  ): commonResponse
  revertGoalsOrRatings(
    action: String
    employeeId: [Int]!
    month: Int!
    year: Int!
  ): commonResponse
  generateReportBasedOnInputs(
    reportId: Int!
    reportFormat: String!
    reportHeaders: String
    orderBy: String
    filters: filters!
  ): generateReportBasedOnInputsResponse
  addUpdateShiftRotation(
    Rotation_Id: Int
    Scheduler_Name: String!
    Repeat_Schedule: YesNoOption!
    Repeat_Count: Int!
    Enable_Roster_Leave: YesNoOption!
    Leave_Entitlement_Per_Roster_Day: Float
    Leave_Replenishment_Period: Leave_Replenishment_Period
    LeaveType_Id: Int
    Shift_Rotation_Schedule: [Shift_Rotation_Schedule]
  ): commonResponse!
  deleteShiftRotation(Rotation_Id: Int!): commonResponse!
  evaluateAndProcessShiftSwapStatus(swapId: Int!): processSwapStatusResponse!
  rejectShiftSwap(swapId: Int!): commonResponse!
}

input Shift_Rotation_Schedule{
  ShiftType_Id: Int,
  Rotation_Level: Int
  Applicable_Period: Int,
  Period_Unit: Period_Unit
}

enum Period_Unit{
  day
  week
  month
}

enum Leave_Replenishment_Period{
  Daily
  Weekly
  Monthly
}

enum YesNoOption{
  Yes
  No
}

type commonResponse {
  errorCode: String
  message: String
}

type processSwapStatusResponse {
  errorCode: String
  message: String
  matchingSwapIds: [Int]
}

input filters {
  employeeIdsArray: [Int]!
  startDate: String
  endDate: String
}

type generateReportBasedOnInputsResponse {
  errorCode: String
  message: String
  reportData: String
}

schema {
  query: Query
  mutation: Mutation
}
