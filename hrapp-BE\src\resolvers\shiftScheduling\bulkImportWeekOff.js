// list the shift scheduling details
module.exports.bulkImportWeekOff = async (parent, args, context, info) => {

    console.log('Inside bulkImportWeekOff() function');    
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // require apollo error
    const { ApolloError } = require('apollo-server-lambda');
    // require common constant files
    const { formName, roles, systemLogs} = require('../../../common/appconstants');
    // require weekoff common function
    const { updateWeekOffDetails } = require('./weekOffCommonFunction');
    // Organization database connection
    const knex = require('knex');

    // get db connection
    const orgDb = knex(context.connection.OrganizationDb);

    // variable declarations
    let errResult;
    let errorMessage = {};

    try {
        // Check form view access rights
        let loginId = context.Employee_Id;

        // check shift schduling update rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(orgDb, loginId, formName.shiftScheduling, roles.roleUpdate);

        // check update rights exist or not 
        if (checkRights === true) {

            // Call function getEmployeeTimeZone() to get current date and time based on employee time zone 
            // Send employeeId ,dbConnection and another flag as 1(This flag is to get the time zone date with time)
            let empTimeZone = await commonLib.func.getEmployeeTimeZone(loginId, orgDb, 1);

            // get input details
            let weekOffArray = args.weekOffArray;

            // Iterate the and update the weekoff details
            for (let i = 0; i < weekOffArray.length; i++){

                // fuction to update the week off details
                let response = await updateWeekOffDetails(weekOffArray[i], empTimeZone, loginId,orgDb);
                
                // check message exist in the response. If yes append the error message with response
                if(response.message){
                    errorMessage[i] = response.message;
                }
            }

            // form inputs to update system log activities commonly
            let systemLogParams = {
                action: '',
                userIp: context.User_Ip,
                employeeId: loginId,
                formName: '',
                trackingColumn: '',
                organizationDbConnection: orgDb,
                uniqueId: '',
                message: systemLogs.roleUpdate + ' ' + formName.shiftScheduling + ' ' + ' - Bulk import Week Off'
            }
            // call function createSystemLogActivities() to update system log activities
            await commonLib.func.createSystemLogActivities(systemLogParams);

            // return response back to UI
            return { success: true, errorCode: '', message: Object.keys(errorMessage).length ? JSON.stringify(errorMessage) : "" }
        
        // If rights not exists
        } else if (checkRights === false) {
            console.log('Access rights does not exists.');
            errResult = commonLib.func.getError('', '_DB0102');            
            return {
                success: false,
                errorCode: errResult.code,
                message: errResult.message
            }
        } else {
            console.log("Error while checking rights in bulkImportWeekOff() function", checkRights);
            errResult = commonLib.func.getError(checkRights, 'SS0109');
            return {
                success: false,
                errorCode: errResult.code,
                message: errResult.message
            }
        }
    } catch (bulkImportWeekOffMainCatchError){
        console.log('Error in bulkImportWeekOff() function main catch block.', bulkImportWeekOffMainCatchError);
        let errResult = commonLib.func.getError(bulkImportWeekOffMainCatchError, 'SS0142');
        throw new ApolloError(errResult.message, errResult.code)
    } finally {
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
    }
};