// resolver function to calculate the overtime wages for the employees
// This will be working as an API as well as function
module.exports.overtimeWageCalculation = async (parent, args, context, info) => {
    console.log("Inside overtimeWageCalculation() function.")
    // require apollo server errors
    const { ApolloError, UserInputError } = require('apollo-server-lambda');
    // Organization database connection
    const knex = require('knex');
    // require table names
    const { ehrTables } = require('../../../common/tablealias');
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // require moment 
    let moment = require('moment-timezone');
    // require validation file
    const { validateInputs, formTotalHours} = require('./overtimeCommonFunctions');
    // get db connection
    const orgDb = knex(context.connection.OrganizationDb);
    let validationError = {};
    try{
        /**
         * Declare the inputs
         * Input parameters, inputWorkScheduleDetails and shiftAllowanceAppliedWorkScheduleId will be send during overtime claim add and update
        */
        let { employeeId,otStartTime,otEndTime,overtimeClaimId=0, action, inputWorkScheduleDetails={},shiftAllowanceAppliedWorkScheduleId=0} = args;
        // validate inputs
        validationError = validateInputs(args);
        // check validation error is exist or not
        if (Object.keys(validationError).length === 0) {
            // check overtime is enabled for this employee or not
            let isOvertimeEnabled = await orgDb(ehrTables.empSalaryConfiguration)
                                        .select('Eligible_For_Overtime','Overtime_Slab')
                                        .where('Employee_Id',employeeId);
            // check isOvertimeEnabled is 0/1
            if (isOvertimeEnabled[0] && isOvertimeEnabled[0].Eligible_For_Overtime){
                // get overtime slab id  
                let overtimeSlabId = isOvertimeEnabled[0].Overtime_Slab;
                // if the current workschuedule details are not sent
                if(Object.keys(inputWorkScheduleDetails).length === 0){
                    // get workSchedule details
                    currentWorkScheduleDetails = await commonLib.employees.getCurrentWorkScheduleDetails(employeeId, otStartTime, orgDb);
                }else{
                    // if the current workschuedule details sent from the add and update overtime claim details
                    currentWorkScheduleDetails = inputWorkScheduleDetails;
                }
                // get workSchedule details
                let { regularFrom, regularTo, considerationFrom, considerationTo, overtimeCoolingPeriod, workScheduleId, errorCode } = currentWorkScheduleDetails;
                // if the overtime claim duration falls on the shift and if that shift/ work schedule details exist
                if (regularTo && regularFrom && considerationFrom && considerationTo && workScheduleId){
                    // "2020-09-09 23:50:00"
                    considerationFrom = considerationFrom.toISOString().replace(/T/, ' ').replace(/\..+/, '');
                    considerationTo = considerationTo.toISOString().replace(/T/, ' ').replace(/\..+/, '');
                    // Check any overtime record exist in between overtime claim shift - consideration start date-time and consideration end date-time
                    let overtimeRecordsQuery = orgDb(ehrTables.overtime)
                                        .select('Overtime_Claim_Id')
                                        .where('Employee_Id', employeeId)
                                        .where(qb => {
                                            qb.where('Start_Date_Time','>=', considerationFrom)
                                            qb.where('End_Date_Time','<=', considerationTo)
                                        });
                    /** If the overtime claim id already exist */
                    if(overtimeClaimId && overtimeClaimId > 0){
                        /** validate any overtime record exist for the consideration date-time except this overtime claim id */
                        overtimeRecordsQuery = overtimeRecordsQuery.whereNot('Overtime_Claim_Id', overtimeClaimId);
                    }

                    let overtimeRecords = await overtimeRecordsQuery.then(otRecords =>{
                                        // return response
                                        return otRecords[0] ? otRecords[0].Overtime_Claim_Id : 0;
                                    })
                                    .catch(getOtRecordsError =>{
                                        console.log('Error while getting the overtime records in overtimeWageCalculation() function .catch block', getOtRecordsError)
                                        // return response
                                        return 0;
                                    });
                    // Check any overtime records exists
                    if (overtimeRecords){
                        // throw error if OT record already exists
                        throw ('OT0109');
                    }else{
                        let isWorkingDay = 0;
                        
                        // form date format
                        let startDate = moment(otStartTime).format('YYYY-MM-DD');
                        
                        let date = new Date(startDate);
                        // get the ID for the day
                        let dayId = date.getDay();
                        if (dayId == 0) {
                            dayId = 7;
                        }
                        // Add overtimeCoolingPeriod minutes with regularTo
                        regularTo.setMinutes(regularTo.getMinutes() + overtimeCoolingPeriod);
                        
                        // Form regularTo
                        regularTo = regularTo.toISOString().replace(/T/, ' ').replace(/\..+/, ''); // "2020-09-09 23:50:00"
                        
                        // get overtime settings
                        overtimeSettings = await orgDb(ehrTables.overtimeSettings)
                        .select('Overtime_Flat_OR_Slab_Rate','Allow_Regular_Hours_Overtime_For_Weekoff_holiday');

                        // flag to allow the user to claim overtime from "shift - consideration start date time" when the shift falls on weekoff or holiday
                        let allowRegularHoursOvertimeForWeekoffHoliday = overtimeSettings[0] && overtimeSettings[0].Allow_Regular_Hours_Overtime_For_Weekoff_holiday;

                        // If we want to check corresponding overtime start date is working day or not based on workschedule,
                        // then we have to send the totalWorkingDays as null and formName as leaves
                        isWorkingDay = await commonLib.payroll.getBusinessWorkingDays(orgDb, employeeId, startDate, startDate, 1, null, 'leaves');

                        /** Employee are eligible to apply the overtime before "regular to" date time when the allowRegularHoursOvertimeForWeekoffHoliday is 1 and the overtime claim shift should
                         * fall on either weekoff or holiday. So validate whether are eligible to apply overtime before regular to date-time */
                        if (otStartTime < regularTo && (allowRegularHoursOvertimeForWeekoffHoliday === 0 || 
                        (allowRegularHoursOvertimeForWeekoffHoliday === 1 && isWorkingDay ))){
                            throw ('OT0110');// throw Overtime should not fall in the regular work hours
                        }
                        /** validate either employee applies the overtime claim on or after the consideration start date time when 
                         * overtime claimed on weekoff or holiday and allowRegularHoursOvertimeForWeekoffHoliday is 1. In this statement,"otStartTime < considerationFrom"
                         * will not fail as this is already checked in the function "getCurrentWorkScheduleDetails" */
                        else if (otStartTime < considerationFrom && allowRegularHoursOvertimeForWeekoffHoliday === 1 && !(isWorkingDay)){
                            // Overtime can be claimed after the consideration start time
                            throw ('OT0119');
                        }
                        /** While adding or updating the overtime claim and if the shiftAllowanceAppliedWorkScheduleId exist then validate the overtime claim end date is less than or
                         * equal to the regular to date-time when overtime is claimed on weekoff or holiday and allowRegularHoursOvertimeForWeekoffHoliday is 1. */
                        else if (args.source && args.source === 'Back-End' && shiftAllowanceAppliedWorkScheduleId && otStartTime >= considerationFrom && otEndTime <= regularTo && allowRegularHoursOvertimeForWeekoffHoliday === 1 && !(isWorkingDay)){
                            throw ('OT0125');//Shift allowance is not applicable as the overtime is claimed for the weekoff or holiday on or before the regular to date-time
                        }else{
                            // check otEndTime is less than the next day consideration from time
                            if (otEndTime <= considerationTo){
                            let slabType = isWorkingDay ? 'Working Days' : 'Nonworking Days';
                            // get overtime slab details
                            let slabDetails = await orgDb(ehrTables.overtimeSlab)
                                                    .select('*')
                                                    .where('Overtime_Id', overtimeSlabId)
                                                    .where('Day_Type', slabType);
                            
                            // get the total hours from otStartTime and otEndTime
                            otStartTime = moment(otStartTime, 'YYYY-MM-DD hh:mm:ss');
                            otEndTime = moment(otEndTime, 'YYYY-MM-DD hh:mm:ss');
                            // Get difference between 2 dates in seconds
                            let timeDiff = otEndTime.diff(otStartTime, 'seconds');
                            let totalHoursInFormat = await formTotalHours(timeDiff);
                            // convert timeDiff seconds to hours
                            timeDiff = (timeDiff / 3600).toFixed(2);
                            let totalHours = timeDiff;
                            let overtimeWages = 0;                            
                            // check Overtime_Flat_OR_Slab_Rate flag. It can be Flat/Slab Range/Slab
                            if (overtimeSettings[0] && overtimeSettings[0].Overtime_Flat_OR_Slab_Rate === 'Flat'){
                                let hours = [];
                                let amount = [];
                                let isSlabMatched = 0;
                                // Iterate the overtime slabs
                                for(let slab of slabDetails){
                                    hours.push(slab.OT_Hours_To);
                                    amount.push(slab.Overtime_Wage);
                                    // Check OT start and end time is fall in the configured Overtime hours
                                    // If yes the get the overtime wages and add it with overtimeWages
                                    // We have to cross check this while developing the settings.
                                    // As of now this will satisfy the indianoiling requirement
                                    if (timeDiff >= slab.OT_Hours_From && timeDiff <= slab.OT_Hours_To){
                                        isSlabMatched = 1;
                                        overtimeWages = slab.Overtime_Wage;
                                    }
                                }
                                // Check OT hours matched any slabs.
                                // if not check total overtime hours is more than configured hours
                                //  then assign maxWage to overtimeWages
                                let maxHours = hours.reduce(function (a, b) {
                                    return Math.max(a, b);
                                });
                                let maxWage = amount.reduce(function (a, b) {
                                    return Math.max(a, b);
                                });
                                if (isSlabMatched === 0 && timeDiff > maxHours) {
                                    overtimeWages = maxWage;
                                }
                            } else if (overtimeSettings[0] && overtimeSettings[0].Overtime_Flat_OR_Slab_Rate === 'Slab Range'){
                                // iterate the slab details and get the overtime wages
                                for(let slab of slabDetails){
                                    if (timeDiff >= slab.OT_Hours_From || timeDiff >= slab.OT_Hours_To) {
                                        overtimeWages = overtimeWages + slab.Overtime_Wage;
                                    }
                                }
                            }else{
                                // Iterate the slab details and get the overtime wages
                                for (let slab of slabDetails) {
                                    // if total hour is greater than OT_Hours_To then get the Overtime_Wage as is.
                                    if (totalHours >= slab.OT_Hours_To){
                                        overtimeWages = overtimeWages + slab.Overtime_Wage;
                                    }else{
                                        // If total hour is less than OT_Hours_To then calculate the amount based on the hours
                                        // If Total hour is 2.5hrs 
                                        // Slab1: OT From and To is 0-2 hrs and wages is 600
                                        // Slab2: OT From and To is 2-5 hrs and wages is 900
                                        // we have get 600 from the Slab1
                                        // calculate wages for .5 hrs from Slab2
                                        // So formula will be (900/((5-2)+1))
                                        // Why this formula used (5-2)+1) ? --> The hours between the Slab2(5-2hrs) are 2,3,4,5 so have to get the numbe of hours between the 2-5 hrs slab
                                        // get the amount for 1hrs
                                        if (totalHours >= slab.OT_Hours_From){
                                            let amountBasedOnHours = (slab.Overtime_Wage / ((slab.OT_Hours_To - slab.OT_Hours_From) + 1));
                                            // Calculate the remaining hours
                                            let totalHoursWithInSlab = totalHours - slab.OT_Hours_From;
                                            // Calculate the amount for remaining hours
                                            overtimeWages = overtimeWages + (totalHoursWithInSlab*amountBasedOnHours);
                                        }
                                    }
                                }
                            }
                            // destroy database connection
                            orgDb ? orgDb.destroy() : null;
                            // return response back to function
                            return { totalHours: totalHours, overtimeWages: overtimeWages,totalHoursInFormat: totalHoursInFormat };
                            // check status configuration
                            }else{
                                // OT End time should not fall in the next day consideration from time
                                throw ('OT0113');
                            }
                        }
                    }
                }
                else{
                    console.log('Current work schedule details not exist. Work schedule response. ',currentWorkScheduleDetails);
                    //if an error code returned while fetching the current work schedule details
                    if(errorCode){
                        throw (errorCode);    
                    }else{
                        throw ('OT0011');
                    }
                }
            }else{
                // overtime is not enabled for them
                throw ('OT0108');
            }

        }else{
            // throw validation error
            throw ('IVE0000');
        }
    }catch(overtimeWageCalculationMainCatchError){
        console.log('Error in overtimeWageCalculation() function main catch block.',overtimeWageCalculationMainCatchError);
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
        // check input validation error or not
        if (overtimeWageCalculationMainCatchError === 'IVE0000') {
            errResult = commonLib.func.getError('', overtimeWageCalculationMainCatchError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            // get error and return it to UI
            let errResult = commonLib.func.getError(overtimeWageCalculationMainCatchError, 'OT0106');
            if (args.source && args.source === 'Back-End'){
                throw (errResult.code);
            }else{
                throw new ApolloError(errResult.message, errResult.code);
            }
        }
    }
};