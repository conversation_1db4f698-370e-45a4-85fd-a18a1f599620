// Get the employee allocated and vested shares details
module.exports.listEmployeeAllocatedShares = async (parent, args, context, info)=>{
    console.log('Inside the listEmployeeAllocatedShares() function.');
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // require knex for database connection
    const knex = require('knex');
    // require apollo server to return error message
    const { UserInputError,ApolloError } = require('apollo-server-lambda');
    // require common constant files
    const { formName,defaultValues } = require('../../../common/appconstants');
    // require table names
    const { ehrTables } = require('../../../common/tablealias');
    // variable declarations
    let errResult ={};
    let organizationDbConnection = '';
    let validationError = {};

    try{
        // get the organization database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let logInEmpId = context.Employee_Id;
        // Check ESOP form view access rights exist for employee or not
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.esop, '', 'UI');   
        if(Object.keys(checkRights).length>0 && checkRights.Role_View === 1) {
            /** If employee id not exist */
            if(!args.employeeId){
                validationError['IVE0083'] = commonLib.func.getError('', 'IVE0083').message;
            }

            /** If source param does not exist */
            if(args.source!== 'esop' && args.source !== 'myesop'){
                validationError['IVE0120'] = commonLib.func.getError('', 'IVE0120').message;
            }
            
            if(Object.keys(validationError).length===0){
                return (
                organizationDbConnection
                .transaction(function (trx) {
                    //Get the esop benefits configuration
                    return(
                    organizationDbConnection(ehrTables.benefitsConfiguration+ ' as BC')
                    .select('BC.Value_Of_Share')
                    .transacting(trx)
                    .then(async (getBenefitsConfig) =>{
                        if(getBenefitsConfig && getBenefitsConfig.length > 0){
                            let valueOfShare = getBenefitsConfig[0].Value_Of_Share;

                            let employeeAllocatedShareQuery = organizationDbConnection(ehrTables.employeeShareDetails+ ' as ESD')
                            .select(
                                'ESD.Employee_Allocated_Share_Id',
                                'ESD.Allocated_Shares',
                                'ESD.Allocated_Date',
                                'ESD.Vested_Status',
                                organizationDbConnection.raw("ESD.Allocated_Shares * ? as Total_Share_Value", [valueOfShare]),
                                organizationDbConnection.raw(`
                                  (SELECT SUM(ESVH.Vested_Share)
                                   FROM employee_share_vest_history AS ESVH
                                   WHERE ESVH.Employee_Allocated_Share_Id = ESD.Employee_Allocated_Share_Id) as Vested_Share
                                `));
                            /** If the source is esop, then benefits admin can view the shares. So get the input employee id - share and details for benefits
                             * admin based on the visibility status and login employee id.*/
                            if(args.source === 'esop'){
                                // Check super admin form - optional choice access rights exist for employee or not
                                let checkSuperAdminRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.superAdmin, '', 'UI');
                                let isLoginEmployeeSuperAdmin = 0;
                                if(Object.keys(checkSuperAdminRights).length>0 && checkSuperAdminRights.Role_Optional_Choice === 1) {
                                    isLoginEmployeeSuperAdmin = 1;
                                }

                                //where condition to get the shares details which are added by the login employee id
                                employeeAllocatedShareQuery = employeeAllocatedShareQuery
                                .where(qb => {
                                    qb.where('ESD.Employee_Id', args.employeeId)
                                    qb.where('ESD.Added_By', logInEmpId)
                                });

                                /** If the login employee is having super admin access then they can view all the super admin-visibility status records
                                 * along with the records added by them.
                                 */
                                if(isLoginEmployeeSuperAdmin === 1){
                                    //where condition to get all the super admin visibility status - shares details
                                    employeeAllocatedShareQuery = employeeAllocatedShareQuery
                                    .orWhere(qb => {
                                        qb.where('ESD.Employee_Id', args.employeeId)
                                        qb.where('ESD.Visibility', defaultValues.benefitsSuperAdminsVisibilityStatus)
                                    });
                                }
                            }else{
                                /** If the source is myesop, then login employee can view all his shares. So get the input employee id - share and details.*/
                                employeeAllocatedShareQuery = employeeAllocatedShareQuery
                                .where('ESD.Employee_Id', args.employeeId);
                            }

                            //Get the employee allocated share and vested share details
                            return(
                                employeeAllocatedShareQuery
                                .transacting(trx)
                                .then((employeeAllocatedSharesDetails) =>{
                                    return { errorCode: '', message: 'Employee allocated shares retrieved successfully.', employeeAllocatedSharesList: employeeAllocatedSharesDetails };
                                })
                            )
                        }else{
                            throw('BES0006');
                        }
                    })
                    )
                })
                .catch(function (listEmployeeAllocatedSharesInsideCatchError) {
                    console.log('Error in listEmployeeAllocatedShares() function .catch block', listEmployeeAllocatedSharesInsideCatchError);
                    errResult = commonLib.func.getError(listEmployeeAllocatedSharesInsideCatchError, 'BES0008');
                    throw new ApolloError(errResult.message,errResult.code);
                })
                // close db connection
                .finally(() => {
                    organizationDbConnection.destroy();
                })
                )
            }else{
                throw 'IVE0000';
            }
        }else{
            throw ('_DB0100');
        }
    }catch(listEmployeeAllocatedSharesCatchError){
        console.log('Error in the listEmployeeAllocatedShares() function main catch block.',listEmployeeAllocatedSharesCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if(listEmployeeAllocatedSharesCatchError==='IVE0000'){
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });
        }
        else{
            errResult = commonLib.func.getError(listEmployeeAllocatedSharesCatchError, 'BES0107');
            throw new ApolloError(errResult.message,errResult.code);
        }
    }
};