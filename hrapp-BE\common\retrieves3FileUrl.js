
async function getFileURL(fileName){
    try{
        // require aws-sdk to use aws services
        const AWS = require('aws-sdk')
        // Create object for s3 bucket
        const s3 = new AWS.S3({ region: process.env.region });
        // Get bucket name from environment variable
        var bucket = process.env.hrappProfileBucket;

        // Set URL expired time
        const signedUrlExpireSeconds = 60 * 60;
        
        // Call function headObject to check files exists or not in s3 bucket. Pass bucket name and file name as input
        await s3.headObject({ Bucket: bucket, Key: fileName }).promise();

        // Call function getSignedUrl and pass getObject/putObject function to getpresigned url
        var url = s3.getSignedUrl('getObject', { Bucket: bucket, Key: fileName, Expires: signedUrlExpireSeconds });
        return url;
    }
    catch(error){
        console.log('Error while retrieving the presigned url',error);
        return '';
    }
    
}

module.exports.getFileURL = getFileURL;
