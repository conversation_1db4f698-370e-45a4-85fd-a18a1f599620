{"securityGroupIds": ["sg-0d3854ad69d6e7e09", "sg-0a9a621d864c23783"], "subnetIds": ["subnet-075680669427eff9d", "subnet-0e2510550b4a177a5"], "dbSecretName": "prod/hrapp/pgaccess", "region": "ap-south-1", "hrappProfileBucket": "s3.images.hrapp.co", "lambdaRole": "arn:aws:iam::484056187456:role/LambdaMicroservice", "dbPrefix": "hrapp_", "domainName": "hrapp", "authorizerARN": "arn:aws:lambda:ap-south-1:484056187456:function:ATS-prod-firebaseauthorizer", "customDomainName": "api.hrapp.co", "logoBucket": "s3.logos.hrapp.co", "screenshotsBucket": "s3.hrapp.prod.employeemonitoring", "emailFrom": "<EMAIL>", "sesRegion": "us-west-2", "firebaseApiKey": "AIzaSyAupi9_2ATYi05M7hfgO3pZFqF1dNGK7tk", "webAddress": ".co", "desktopClientURL": "https://www.microsoft.com/en-in/p/hrapp-activity-tracker/9nd9c9nmb4nr", "pmsEmailNotificationArn": "arn:aws:states:ap-south-1:484056187456:stateMachine:prod-pmsReminderNotification", "batchEmailNotificationArn": "arn:aws:states:ap-south-1:484056187456:stateMachine:prod-batchEmailNotification", "asynchronousreportBucket": "asynchronousreport.hrapp.co", "generateReportAsyncStepFunction": "arn:aws:states:ap-south-1:484056187456:stateMachine:prod-generateReportAsyncStepFunction", "updateRosterLeaveBatch": "arn:aws:lambda:ap-south-1:484056187456:function:BATCHPROCESSING-prod-updateRosterLeaveBatch", "resourceArnPrefix": "arn:aws:lambda:ap-south-1:484056187456:function:HRAPPBACKEND-prod"}