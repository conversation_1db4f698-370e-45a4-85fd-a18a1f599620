// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
// Require Apollo Server to return error messages
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formIds } = require('../../../common/appconstants');
// require status approval function
const { evaluateAndApproveAlternateSwap } = require('../shiftSwap/statusApprovalCommonFunction');

// Declare database connections
let organizationDbConnection, appManagerDbConnection;

module.exports.approveMathchingSwapRequest = async (parent, args, context, info) => {
  let overlapErrorMessage = '';
  try {
    console.log("Inside approveMathchingSwapRequest function.");

    // Get login employee ID from context
    let loginEmployeeId = context.Employee_Id;

    // Initialize the database connection
    organizationDbConnection = knex(context.connection.OrganizationDb);

    // Check employee rights
    let checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      null,
      null,
      'ui',
      null,
      formIds.shiftSwap,
      { appManagerDbConnection: appManagerDbConnection, orgCode: context.Org_Code }
    );

    // If no rights or update role is not available, throw an error
    if (!checkRights || !checkRights.Role_Update) {
      throw '_DB0102';
    }

    // Use transaction to handle database updates
      let updateResult = await evaluateAndApproveAlternateSwap(
        organizationDbConnection,
        args,
        context,
      );

      if(updateResult.errorCode){
        overlapErrorMessage = updateResult.message;
        throw updateResult.errorCode;
      }else{
        return {
          errorCode: "",
          message: `${checkRights.Custom_Form_Name} status updated successfully.`,
        };
      }

    return result; // Return success response from the transaction
  } catch (e) {
    console.error('Error in approveMathchingSwapRequest function main catch block.', e);
    // Handle and throw formatted error
    if(e === 'SS0160'){
      throw new ApolloError(overlapErrorMessage, 'SS0160');
    }else{
      let errResult = commonLib.func.getError(e, 'ESS0055');
      throw new ApolloError(errResult.message, errResult.code);
    }
  } finally {
    // Destroy the database connection if it exists
    if (organizationDbConnection) {
      organizationDbConnection.destroy();
    }
  }
};
