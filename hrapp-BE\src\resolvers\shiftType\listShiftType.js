module.exports.listShiftType = async (parent, args, context, info) => {
    const { ApolloError,UserInputError } = require('apollo-server-lambda');
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    const { formName, roles } = require('../../../common/appconstants');
    const knex = require('knex');
    const { ehrTables } = require('../../../common/tablealias');
    const orgDb = knex(context.connection.OrganizationDb);
    const moment = require('moment');
  
    try {
      // Check access rights if not dropdown mode
      let validationError = {}
      const checkRights = !args.isDropDown
        ? await commonLib.func.checkEmployeeAccessRights(
            orgDb,
            context.Employee_Id,
            formName.shiftScheduling,
            roles.roleView
          )
        : true;
  
      if (!checkRights) {
        const errResult = commonLib.func.getError('', '_DB0100');
        return { success: false, errorCode: errResult.code, message: errResult.message };
      }

      if (args.employeeId && args.swapDate && !moment.utc(args.swapDate).isValid()) {
        validationError['IVE0510'] = commonLib.func.getError(
          'IVE0510',
          ''
        ).message
        const tomorrowUtc = moment.utc().add(1, 'day').startOf('day')

        if (moment.utc(args.swapDate).isBefore(tomorrowUtc)) {
          validationError['IVE0511'] = commonLib.func.getError(
            'IVE0511',
            ''
          ).message
        }
      }
      if (Object.keys(validationError).length > 0) {
        throw new Error('IVE0000')
      }
      // Determine sorting fields
      const sortFieldMap = {
        1: 'WS.Title',
        2: 'ST.Minimum_Employee_Count',
        3: 'ST.Maximum_Employee_Count',
      };
      const sortField = sortFieldMap?.[args.sortField] || 'WS.Title';
      const sortOrder = args.sortOrder === 'asc' ? 'asc' : 'desc';
  
      // Fetch shift employee count
      const shiftEmployeeCountData = await orgDb
        .count('EJ.Employee_Id as count')
        .from(`${ehrTables.empJob} as EJ`)
        .leftJoin(`${ehrTables.empType} as ET`, 'ET.EmpType_Id', 'EJ.EmpType_Id')
        .leftJoin(`${ehrTables.empPersonal} as EP`, 'EP.Employee_Id', 'EJ.Employee_Id')
        .where({
          'EP.Form_Status': 1,
          'ET.Work_Schedule': 'Shift Roster',
          'EJ.Emp_Status': 'Active',
        })
        .first();
      const shiftEmployeeCount = shiftEmployeeCountData.count;
  
      // Fetch roster settings
      const rosterSettings = await orgDb(`${ehrTables.rosterSettings}`).select('*').first();
  
      // Fetch shift types
      let shiftType = await orgDb
        .select(
          'ST.Shift_Type_Id as Shift_Id',
          'ST.WorkSchedule_Id',
          'WS.Title as Shift_Name',
          'ST.Status',
          'ST.Minimum_Employee_Count',
          'ST.Maximum_Employee_Count',
          'ST.Holiday_Override',
          'ST.Comments',
          'ST.Colour_Code',
          orgDb.raw(
            "CONCAT_WS(' ', P.Emp_First_Name, P.Emp_Middle_Name, P.Emp_Last_Name) as Added_By"
          ),
          'ST.Added_On',
          orgDb.raw(
            "CONCAT_WS(' ', P1.Emp_First_Name, P1.Emp_Middle_Name, P1.Emp_Last_Name) as Updated_By"
          ),
          'ST.Updated_On'
        )
        .from(`${ehrTables.empShiftType} as ST`)
        .leftJoin(`${ehrTables.workSchedule} as WS`, 'WS.WorkSchedule_Id', 'ST.WorkSchedule_Id')
        .leftJoin(`${ehrTables.empPersonal} as P`, 'P.Employee_Id', 'ST.Added_By')
        .leftJoin(`${ehrTables.empPersonal} as P1`, 'P1.Employee_Id', 'ST.Updated_By')
        .orderBy(sortField, sortOrder)
        .modify((qb) => {
          if (args.searchString) {
            qb.orWhere('WS.Title', 'like', `%${args.searchString}%`);
            qb.orWhere('ST.Minimum_Employee_Count', args.searchString);
            qb.orWhere('ST.Maximum_Employee_Count', args.searchString);
          }
          if (args.shiftName) qb.where('WS.Title', 'like', `%${args.shiftName}%`);
          if (args.minCount) qb.where('ST.Minimum_Employee_Count', args.minCount);
          if (args.maxCount) qb.where('ST.Maximum_Employee_Count', args.maxCount);
          if (args.isDropDown) qb.where('ST.Status', 'Active');
        });
  
      // Add loginEmployeeIdShift field if employeeId and swapDate are provided
      if (args.employeeId && args.swapDate) {
  
        const employeeShiftMapping = await orgDb
          .select('*')
          .from(`${ehrTables.shiftEmpMapping} as SE`)
          .where('SE.Employee_Id', args.employeeId)
          .andWhere('SE.Shift_Start_Date', args.swapDate);
          const mappedShiftIds = (employeeShiftMapping?.length > 0) 
          ? employeeShiftMapping.map((mapping) => mapping.Shift_Type_Id) 
          : [];
  
        shiftType = shiftType.map((shift) => ({
          ...shift,
          loginEmployeeIdShift: mappedShiftIds.includes(shift.Shift_Id),
        }));
      }
  
      return {
        success: true,
        errorCode: null,
        message: 'Successfully retrieved shift types',
        shiftType,
        shiftEmployeeCount,
        rosterSettings,
      };
    } catch (error) {
      if (error.message === 'IVE0000') {
        const errResult = commonLib.func.getError('', 'IVE0000')
        throw new UserInputError(errResult.message, { validationError })
      }else{
      console.error('Error in listShiftType:', error);
      const errResult = commonLib.func.getError(error, 'ST0101');
      throw new ApolloError(errResult.message, errResult.code);
      }
    } finally {
      if (orgDb) orgDb.destroy();
    }
  };
  