// resolver function to delete the overtime records of the employee
module.exports.deleteOvertimeDetails = async (parent, args, context, info) => {
    console.log('Inside deleteOvertimeDetails() function');
    const { ApolloError } = require('apollo-server-lambda');
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // require common constant files
    const { formName, systemLogs } = require('../../../common/appconstants');
    // Organization database connection
    const knex = require('knex');
    // require table names
    const { ehrTables } = require('../../../common/tablealias');
    // get db connection
    const orgDb = knex(context.connection.OrganizationDb);
    try{
        // get login employee Id
        let loginEmpId = context.Employee_Id;
        // Check form delete access rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(orgDb, loginEmpId, formName.overtime, '', 'UI');
        // check checkRights is not empty json or not
        if (Object.keys(checkRights).length === 0) {
            // throw access denied error
            throw ('_DB0103');
        } else {
            // check loggedIn has delete access or not
            if (checkRights.Role_Delete === 1) {
                // check logged-in employee is employee admin or not
                let checkEmpAdminRights = await commonLib.func.checkEmployeeAccessRights(orgDb, loginEmpId, formName.employeeAdmin, '', 'UI');
                // we can allow the user to delete the records if they are admin/superadmin/employee admin/payroll admin
                if ((Object.keys(checkEmpAdminRights).length > 0 && checkEmpAdminRights.Role_Update === 1) || (checkRights.Employee_Role.toLowerCase() === 'admin')) {
                    let overtimeClaimIds = args.overtimeClaimId;
                    let deletedRecords = [];
                    // check overtimeClaimId array is not empty
                    if (overtimeClaimIds.length > 0){
                        // iterate the array and delete the records
                        for(id of overtimeClaimIds){
                            // Get the overtime record details
                            let overtimeRecord = await orgDb(ehrTables.overtime)
                                                .select('Approval_Status','Lock_Flag')
                                                .where('Overtime_Claim_Id',id)
                                                .then(overtimeRecord =>{
                                                    // return response
                                                    return overtimeRecord;
                                                })
                                                .catch(getRecordError =>{
                                                    console.log('Error while getting the overtime records in deleteOvertimeDetails() function.', getRecordError);
                                                    // return response
                                                    return [];
                                                });
                            // check overtimeRecord is empty or not
                            if (overtimeRecord.length > 0){
                                overtimeRecord = overtimeRecord[0];
                                // check lock flag
                                if (overtimeRecord.Lock_Flag){
                                    // lock flag is there so just ignore this record
                                    console.log('Some one is editing this record so cannot able to delete',id);
                                }
                                // check approval status. If it is in Paid status then we should not allow to delete the records
                                else if(overtimeRecord.Approval_Status === 'Paid'){
                                    // User can allow to delete Applied/Approved/Returned/Rejected records
                                    console.log('This overtime records is in paid status so we cannot able to delete', id);
                                }
                                /** During the second level approval either the employee-compensatory off balance or
                                 * shift allowance record might be updated. So we should not allow to delete the overtime claim in this case
                                 */
                                else if(overtimeRecord.Approval_Status === 'Completed'){
                                    // User can allow to delete Applied/Approved/Returned/Rejected records
                                    console.log('The overtime record is in completed status. So unable to delete the overtime claim id, ', id);
                                } else{
                                    // delete the overtime record
                                    let deleteResponse = await orgDb(ehrTables.overtime)
                                                        .del()
                                                        .where('Overtime_Claim_Id',id)
                                                        .then(response => {
                                                            // return response
                                                            return response;
                                                        })
                                                        .catch(delError => {
                                                            console.log('Error while deleting the records in deleteOvertimeDetails() function',delError);
                                                            // return response
                                                            return 0;
                                                        });
                                    // Check deleteResponse, if it is 1 then record has been deleted, so push it in deletedRecords array.
                                    deleteResponse ? deletedRecords.push(id) : null;
                                }
                            }else{
                                // there is no record exist
                                console.log('There is no record exist',id);
                            }
                        }
                        // form inputs to update system log activities commonly
                        let systemLogParams = {
                            action: systemLogs.roleDelete,
                            userIp: context.User_Ip,
                            employeeId: loginEmpId,
                            formName: formName.overtime,
                            trackingColumn: '',
                            organizationDbConnection: orgDb,
                            uniqueId: deletedRecords
                        };
                        // check all records deleted or not
                        if (overtimeClaimIds.length === deletedRecords.length) {
                            // call function createSystemLogActivities() to update system log activities
                            await commonLib.func.createSystemLogActivities(systemLogParams);
                            // destroy database connection
                            orgDb ? orgDb.destroy() : null;
                            // return response back to UI
                            return {
                                errorCode: "",
                                message: "Overtime record(s) deleted successfully"
                            }
                        // Check any one record is deleted or not
                        } else if (deletedRecords.length > 0) {
                            // call function createSystemLogActivities() to update system log activities
                            await commonLib.func.createSystemLogActivities(systemLogParams);
                            // throw error if records deleted partially
                            throw ('OT0104');
                        } else {
                            // throw error
                            throw ('OT0002');
                        }
                    }else{
                        // throw error if overtimeClaimIds array is empty
                        throw ('OT0103');
                    }   
                }else{
                    // throw access denied error
                    throw ('_DB0103');
                }
            }else{
                // throw access denied error
                throw ('_DB0103');
            }            
        }
    } catch (deleteOvertimeDetailsMainCatchError){
        console.log('Error in deleteOvertimeDetails() function main catch block.',deleteOvertimeDetailsMainCatchError);
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
        let errResult = commonLib.func.getError(deleteOvertimeDetailsMainCatchError, 'OT0103');
        throw new ApolloError(errResult.message, errResult.code);
    }
};