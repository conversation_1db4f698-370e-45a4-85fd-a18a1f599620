module.exports.empLeaveHistory  = async(parent, args, context, info) => {
    console.log("Inside empLeaveHistory() function.");
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // Organization database connection
    const knex = require('knex');
    // get the organization data base connection
    let orgDb = knex(context.connection.OrganizationDb);
    // require common constant files
    const { formName, roles,formIds} = require('../../../common/appconstants');

    try
    {
        let logInEmpId = context.Employee_Id;
        let employeeId = args.employeeId ? args.employeeId : context.Employee_Id;

        let source = args.source ? args.source : 'dashboard';
        let orgCode=context.Org_Code;
        let rights;
        if(source == 'dashboard'){
            let rights1 = false, rights2 = false;
            let error1 = null, error2 = null;
            
            try {
              const checkRightsLeaves = await commonLib.func.checkEmployeeAccessRights(
                orgDb,
                logInEmpId,
                null,
                '',
                'UI',
                false,
                formIds.leaves
              );
              rights1 = checkRightsLeaves.Role_View === 1;
            } catch (err) {
              error1 = err;
            }
            
            try {
              const checkRightsEmployeeLeave = await commonLib.func.checkEmployeeAccessRights(
                orgDb,
                logInEmpId,
                null,
                '',
                'UI',
                false,
                formIds.employeeLeave
              );
              rights2 = checkRightsEmployeeLeave.Role_View === 1;

            } catch (err) {
              error2 = err;
            }
            
            if (error1 && error2) {
              throw '_DB0100';
            }

            rights = rights1 || rights2;
        }
        else{
          rights=true;
        }
    
        if (rights === true) {
            let response = await commonLib.employees.empLeaveHistory(orgDb, employeeId,orgCode, source, logInEmpId);

            if(response.errorCode)
            {
                console.log('Error returned from the empLeaveHistory function.', response);
                throw('DB0015');
            }
            else
            {
                return {
                    errorCode:"",
                    message: response.message,
                    leaveHistory: response.leaveHistory
                }
            }
        } 
        else if (rights === false) 
        {
            // throw error if view rights is not exists
            throw ('_DB0100');
        } 
        else 
        {
            // throw error
            throw (rights);
        }
    }
    catch(empLeaveHistoryErr)
    {
        console.log('Error in empLeaveHistory() function main catch block.',empLeaveHistoryErr);
        // access denied error
        if (empLeaveHistoryErr === '_DB0100') 
        {
            errResult = commonLib.func.getError('', '_DB0100');
        } 
        else
        {
            errResult = commonLib.func.getError('', 'DB0015');
        }
        // return response
        throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, leaveHistory:""}));
    }
}