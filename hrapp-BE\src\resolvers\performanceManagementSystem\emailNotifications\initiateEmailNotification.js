'use strict';
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// function to trigger email notification for goals and rating
module.exports.triggerEmailNotificationStepFunction  = async(event, context) =>{
    try{
        console.log('Inside triggerEmailNotificationStepFunction function',event);
        // trigger step function for providing goals
        let triggerGoalReminder= await triggerStepFunction(process.env.stateMachineArn,'goal');
        console.log('Response after triggering goal reminder notification step function',triggerGoalReminder);
        // trigger step function for providing ratings
        let triggerRatingReminder= await triggerStepFunction(process.env.stateMachineArn,'rating');
        console.log('Response after triggering rating reminder notification step function',triggerRatingReminder);
        // return response
        return {errorCode:'',message: 'Reminder notification initiated successfully.'};
    }
    catch(mainCatchError){
        console.log('Error in triggerEmailNotificationStepFunction function main catch block.', mainCatchError);
        let errResult = commonLib.func.getError(mainCatchError, 'EPM0021');
        return {errorCode:errResult.code,message: errResult.message};
    }
};

// function to trigger step functions
async function triggerStepFunction(arn,process){
    try{
        // require aws sdk
        const AWS = require('aws-sdk');
        const stepfunctions = new AWS.StepFunctions();
        // declare inputs
        let input={
            process:process
        }
        // formation of inputs for step functions
        const params = {
            stateMachineArn: arn,
            input: JSON.stringify(input)
        }
        // trigger step function with the input params
        return new Promise(function(resolve, reject) {
            stepfunctions.startExecution(params, function (error, data) {
                if (error) 
                {
                    console.log("Error in initiating step function",error)
                    resolve('');             
                }
                else
                {
                    console.log('Step function triggered sucessfully.');
                    resolve(data);
                }
            })
        })
    }
    catch(error){
        console.log('Error in triggerStepFunction function catch block',error);
        return '';
    }
}
