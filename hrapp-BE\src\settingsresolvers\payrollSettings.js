const knex = require('knex');
const { ehrTables } = require('../../common/tablealias');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

module.exports.Query = {
    // Function to retrieve payroll settings
    viewPayrollSettings: async (parent, args, context, info) => {
        const orgDb = knex(context.connection.OrganizationDb);
        try {

            let settings = await orgDb.select('Form_Id', 'Round_Off_Settings_Id')
                                        .from(ehrTables.payrollRoundOffSettings)

            let payrollSettings = {};
            for(setting of settings)
            {
                payrollSettings[setting.Form_Id] = setting.Round_Off_Settings_Id;
            }

            return {
                errorCode: '',
                message: '',
                payrollSettings: JSON.stringify(payrollSettings)
            }
        } catch(err) {
            console.log('Error in viewPayrollSettings() function ', err);
            let errResult = commonLib.func.getError('', 'SE0010');
            return {
                errorCode: errResult.code,
                message: errResult.message,
                payrollSettings: ''
            }
        } finally {
            orgDb.destroy();
        }
    }
}

module.exports.Mutation = {
    // Function to update payroll settings
    updatePayrollSettings: async (parent, args, context, info) => {
        const orgDb = knex(context.connection.OrganizationDb);
        try {
            let settings = args.settings;

            if(settings.length)
            {
                let queries = [];
                for(setting of settings)
                {
                    let query = orgDb(ehrTables.payrollRoundOffSettings)
                                .update({
                                    Round_Off_Settings_Id: setting.choice
                                })
                                .where('Form_Id',setting.formId)
                    queries.push(query);
                }
    
                await Promise.all(queries)
    
                return {
                    errorCode : '',
                    message : 'Payroll settings updated successfully',
                    validationError : ''
                }
            }
            else
            {
                let errResult = commonLib.func.getError('', 'IVE0000');
                return {
                    errorCode : errResult.code,
                    message : errResult.message,
                    validationError : commonLib.func.getError('', 'IVE0051').message
                }
            }
        } catch(err) {
            console.log("Error while updating payroll settings", err);
            let errResult = commonLib.func.getError('', 'SE0011');

            return {
                errorCode: errResult.code,
                message: errResult.message,
                validationError : ''
            }
        } finally {
            orgDb.destroy();
        }
    }
}
