
module.exports.deleteShiftType = async (parent, args, context, info) =>{
    const { ApolloError } = require('apollo-server-lambda');
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // require table names
    const { ehrTables } = require('../../../common/tablealias');
    // require common constant files
    const { formName, roles, systemLogs } = require('../../../common/appconstants');
    // Organization database connection
    const knex = require('knex');

    const orgDb = knex(context.connection.OrganizationDb);

    try {

        // Check form view access rights
        let loginId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(orgDb, loginId, formName.shiftScheduling, roles.roleDelete);

        if (checkRights === true) {
            let { shiftTypeIds } = args;
            let invalidRecords = [];
            let deletedRecords = [];
            for(shiftTypeId of shiftTypeIds)
            {
                let isShiftMapExist = await orgDb(ehrTables.shiftEmpMapping).count('Shift_Type_Id as count')
                                            .where("Shift_Type_Id",shiftTypeId)
                                            .then((res)=>{
                                                return res[0];
                                            })

                if(isShiftMapExist.count > 0)
                {
                    invalidRecords.push(shiftTypeId);
                }
                else
                {
                    let shiftExist = await orgDb.select('Lock_Flag')
                                                .from(ehrTables.empShiftType)
                                                .where('Shift_Type_Id',shiftTypeId)

                    if(shiftExist[0])
                    {
                        if(shiftExist[0].Lock_Flag)
                        {
                            invalidRecords.push(shiftTypeId);
                        }
                        else
                        {
                            let isDeleted = await orgDb(ehrTables.empShiftType)
                                                    .where('Shift_Type_Id',shiftTypeId)
                                                    .del()
                            
                            isDeleted ? deletedRecords.push(shiftTypeId) : invalidRecords.push(shiftTypeId);
                        }
                    }
                    else
                    {
                        invalidRecords.push(shiftTypeId);
                    }
                }
            }
            // form inputs to update system log activities commonly
            let systemLogParams = {
                action: systemLogs.roleDelete,
                userIp: context.User_Ip,
                employeeId: loginId,
                formName: 'Shift Type',
                trackingColumn: '',
                organizationDbConnection: orgDb,
                uniqueId: deletedRecords
            };
            
            if(shiftTypeIds.length === deletedRecords.length) {

                // call function createSystemLogActivities() to update system log activities
                await commonLib.func.createSystemLogActivities(systemLogParams);

                return {
                    success : true,
                    errorCode : null,
                    message : "Shift Type(s) deleted successfully"
                }
            } else if(deletedRecords.length > 0) {

                // call function createSystemLogActivities() to update system log activities
                await commonLib.func.createSystemLogActivities(systemLogParams);

                let errResult = commonLib.func.getError('', 'SS0137');
                return {
                    success : false,
                    errorCode : errResult.code,
                    message : errResult.message
                }
            } else {
                let errResult = commonLib.func.getError('', 'SS0138');
                return {
                    success : false,
                    errorCode : errResult.code,
                    message : errResult.message
                }
            }
        } else if(checkRights === false) {
            let errResult = commonLib.func.getError('', '_DB0103');
            return {
                success : false,
                errorCode : errResult.code,
                message : errResult.message
            }
        } else {
            console.log("Error while checking rights in deleteShiftType", checkRights);
            let errResult = commonLib.func.getError('', 'ST0113');
            return {
                success : false,
                errorCode : errResult.code,
                message : errResult.message
            }
        }
    }
    catch(err) {
        console.log('Error in deleteShiftType function main catch block',err);
        let errResult  = commonLib.func.getError('', 'ST0109');
        throw new ApolloError(errResult.message,errResult.code )
    } finally {
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
    }
}