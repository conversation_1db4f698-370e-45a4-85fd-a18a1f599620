// list the shift scheduling details
module.exports.listWorkSchedule = async (parent, args, context, info) => {
    console.log('Inside listWorkSchedule function');
    const { ApolloError } = require('apollo-server-lambda');
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // Organization database connection
    const knex = require('knex');
    // require table names
    const { ehrTables } = require('../../../common/tablealias');
    //Require moment package
    const moment = require('moment-timezone');
    let orgDb;
    try{
        orgDb = knex(context.connection.OrganizationDb);
        let subQuery=orgDb.select("WorkSchedule_Id", "Title as WorkSchedule_Name")
        .from(ehrTables.workSchedule  + " as WS")
        // If input contains productivity monitoring form then get the work schedule details along with timezone
        if(args.formName && args.formName.toLowerCase()==='productivitymonitoring'){
            subQuery=subQuery
            .select('WS.Regular_Hours_Per_Day','WS.Target_Effort_In_Hours_Per_Week','T.Zone_Id','T.TimeZone_Id',orgDb.raw("CONCAT('(',T.Offset_Time,')-',T.TimeZone_Id) as Time_Zone"))
            .leftJoin(ehrTables.timezone + ' as T','WS.Zone_Id','T.Zone_Id')
        }
        return await subQuery
            .then((workSchedule)=>{
                let response;
                /**Get current date based on timezone for each workschedule */
                if(args.formName && args.formName.toLowerCase()==='productivitymonitoring' && workSchedule.length > 0){
                    response = [];
                    let currentDate;
                    for(let workScheduleData of workSchedule){
                        if(workScheduleData.TimeZone_Id){
                            let targetDate = moment.tz(workScheduleData['TimeZone_Id']);
                            const isDST = targetDate.isDST();

                            if (isDST) {
                                //Calculate the DST offset and add it to the target time
                                const dstOffset = targetDate.utcOffset();
                                const convertedOffset = minutesToHoursMinutes(dstOffset, workScheduleData.Time_Zone, workScheduleData.TimeZone_Id);
                                workScheduleData.Time_Zone = convertedOffset; 
                            }
                            workScheduleData.currentDateBasedOnTimezone = targetDate.format('YYYY-MM-DD');
                            
                        }
                        response.push(workScheduleData);
                    }
                }else{
                    response = workSchedule;
                }
                return {
                    success : true,
                    errorCode : null,
                    message : "Successfully retrieved work schedule",
                    workSchedule : response
                }
            })
            .catch(function (err){
                console.log('Error in listWorkSchedule function .catch block',err);
                let errResult = commonLib.func.getError(err, 'ST0101');
                throw new ApolloError(errResult.message,errResult.code )
            })
    } catch (listError) {
        console.log('Error in listWorkSchedule function main catch block',listError);
        let errResult  = commonLib.func.getError('', 'ST0104');
        throw new ApolloError(errResult.message,errResult.code )
    } finally {
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
    }
}

function minutesToHoursMinutes(totalMinutes, Time_Zone, TimeZone_Id) {
    try{
        const sign = totalMinutes < 0 ? '-' : '+'; // Determine the sign
        const absTotalMinutes = Math.abs(totalMinutes);
        const hours = Math.floor(absTotalMinutes / 60);
        const minutes = absTotalMinutes % 60;
        const formattedHours = hours.toString().padStart(2, '0');
        const formattedMinutes = minutes.toString().padStart(2, '0');
        return `(UTC${sign}${formattedHours}:${formattedMinutes})-${TimeZone_Id}`;
    } catch(err) {
        console.log('Error in minutesToHoursMinutes function catch block', err);
        return Time_Zone;
    }
  }