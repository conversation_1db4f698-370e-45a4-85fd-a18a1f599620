// resolver function to list the shift allowance - shift type in the additional wage claim form
module.exports.listShiftAllowanceShiftType = async (parent, args, context, info) => {
    console.log('Inside listShiftAllowanceShiftType() function');
    // require apollo server errors
    const { UserInputError,ApolloError } = require('apollo-server-lambda');
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // Organization database connection
    const knex = require('knex');
    // require validation file
    const {validateInputs,getOvertimeSettings,checkShiftAllowanceCustomGroupShiftTypeExist,validateOvertimeWorkSchedule} = require('./overtimeCommonFunctions');
    // require table names
    const { ehrTables } = require('../../../common/tablealias');
    // require common constant files
    const { formIds } = require('../../../common/appconstants');
    // require moment timezone
    let moment = require('moment-timezone');
    //variable declaration
    let orgDb;
    let errResult;
    let validationError = {};
    try{
        // get db connection
        orgDb = knex(context.connection.OrganizationDb);        
        // get variable values
        let { employeeId,otStartTime,otEndTime} = args;
        // validate inputs
        validationError = validateInputs(args);
        // check validation error is exist or not
        if (Object.keys(validationError).length === 0) {
            // get the overtime settings
            let overtimeSettings = await getOvertimeSettings(orgDb);
            /** If overtime settings JSON length is greater than zero */
            if(Object.keys(overtimeSettings).length > 0){
                /** If the shift allowance is applicable for overtime */
                if(overtimeSettings.Shift_Allowance_Applicable_For_Overtime){
                    // flag to allow the user to claim overtime from "shift - consideration start date time" when the shift falls on weekoff or holiday
                    let allowRegularHoursOvertimeForWeekoffHoliday = overtimeSettings.Allow_Regular_Hours_Overtime_For_Weekoff_holiday;

                    let validateWorkScheduleInputs = {
                        employeeId: employeeId,
                        otStartTime: otStartTime,
                        allowRegularHoursOvertimeForWeekoffHoliday:allowRegularHoursOvertimeForWeekoffHoliday
                    }

                    let validateOTWorkscheduleResponse = await validateOvertimeWorkSchedule(orgDb,validateWorkScheduleInputs);

                    /** If the overtime start date-time is valid */
                    if(!validateOTWorkscheduleResponse.error){
                        let workScheduleId = validateOTWorkscheduleResponse.currentWorkScheduleDetails.workScheduleId;
                            /** Validate overtime end date-time is less than the regular to date-time when the regular hours-overtime claim is applicable for week off and holiday 
                             * and when the overtime is claimed on weekoff or holiday. Because for this duration the shift allowance is not applicable as it will be automatically
                             * handled during payslip generation.*/
                            if (allowRegularHoursOvertimeForWeekoffHoliday === 1 && !(validateOTWorkscheduleResponse.isWorkingDay) && 
                            otStartTime >= validateOTWorkscheduleResponse.currentWorkScheduleDetails.considerationFrom &&
                            otEndTime <= validateOTWorkscheduleResponse.currentWorkScheduleDetails.regularTo){
                                throw ('OT0125');//Shift allowance is not applicable as the overtime is claimed for the weekoff or holiday on or before the regular to date-time
                            }else{
                                let customGroupCoverageShiftTypeDetails = [];
                                /**
                                 * manual - employee has to manually apply for shift allowance
                                 * both - refers to manual and auto. auto refers to applying shift allowance for the employee actual regular shift
                                */
                                let shiftMode = ['manual','both'];
                                let isShiftTypeCustomGroupExist = await checkShiftAllowanceCustomGroupShiftTypeExist(orgDb,0,shiftMode,'listemployeeshiftallowanceworkschedule');
                                //if the shift allowance- shift-type custom group exist for the shift modes
                                if(isShiftTypeCustomGroupExist === 1){
                                    // get the shift type custom group configuration id associated with the employee and the shift-type form id from the custom employee group form
                                    let customGroupShiftTypeConfigIds = await commonLib.func.getCustomGroupParentId(orgDb,employeeId,formIds.shiftType);
                                    // if the overtime claim employee id exists in the shift-type custom group then fetch those shift-type details using the shift-type custom group configuration ids
                                    if(customGroupShiftTypeConfigIds && customGroupShiftTypeConfigIds.length > 0){
                                        // Get the shift allowance - shift-type custom group coverage configuration - active record except for the employee actual shift(work schedule id)
                                        customGroupCoverageShiftTypeDetails =
                                        await orgDb(ehrTables.shiftType + ' as ST')
                                        .select('WS.WorkSchedule_Id','WS.Title')
                                        .innerJoin(ehrTables.shiftTypeConfiguration + ' as STC', 'ST.Shift_Name','STC.WorkSchedule_Id')
                                        .innerJoin(ehrTables.workSchedule + ' as WS', 'STC.WorkSchedule_Id','WS.WorkSchedule_Id')
                                        .whereIn('STC.Shift_Mode', shiftMode)
                                        .where('ST.Coverage', 'CUSTOMGROUP')
                                        .where('ST.ShiftType_Status', 'Active')
                                        .whereIn('STC.Configuration_Id',customGroupShiftTypeConfigIds)
                                        .whereNot('ST.Shift_Name', workScheduleId)
                                        .groupBy('STC.WorkSchedule_Id')//group by custom group shift type configuration - workschedule id
                                        .orderBy('ST.Shift_Name','asc')//order by workschedule id
                                        .then(customGroupShiftTypeDetail => {
                                            // Check shift type configuration exists or not
                                            if (customGroupShiftTypeDetail && customGroupShiftTypeDetail.length > 0) {
                                                return customGroupShiftTypeDetail;// return response
                                            } else {
                                                return [];// return response
                                            }
                                        })
                                        .catch(customGroupShiftTypeDetailCatchError =>{
                                            console.log('Error while getting the custom group coverage shift type details in the .catch block.', customGroupShiftTypeDetailCatchError);
                                            throw ('OT0008');
                                        });
                                        
                                    }else{
                                        customGroupCoverageShiftTypeDetails = [];
                                    }
                                }

                                // Get the shift allowance - shift-type - organization coverage configuration - active record except for the employee actual shift(work schedule id)
                                let organizationCoverageShiftTypeDetails = await orgDb(ehrTables.shiftType + ' as ST')
                                .select('WS.WorkSchedule_Id','WS.Title')
                                .innerJoin(ehrTables.workSchedule + ' as WS', 'ST.Shift_Name','WS.WorkSchedule_Id')
                                .where('ST.Coverage', 'ORGANIZATION')
                                .where('ST.ShiftType_Status', 'Active')
                                .whereNot('ST.Shift_Name', workScheduleId)
                                .orderBy('ST.Shift_Name','asc')//order by workschedule id
                                .then(orgShiftTypeDetail => {
                                    // Check shift type organization coverage configuration exists or not
                                    if (orgShiftTypeDetail && orgShiftTypeDetail.length > 0) {
                                        return orgShiftTypeDetail;// return response
                                    } else {
                                        return [];// return response
                                    }
                                })
                                .catch(orgShiftTypeDetailCatchError =>{
                                    console.log('Error while getting the organization coverage shift type details in the .catch block.', orgShiftTypeDetailCatchError);
                                    throw ('OT0009');//throw the error code to handle in the main catch block
                                })
        
                                // merge two arrays
                                let shiftAllowanceShiftTypeList = organizationCoverageShiftTypeDetails.concat(customGroupCoverageShiftTypeDetails);

                                // destroy database connection
                                orgDb ? orgDb.destroy() : null;
                                return { errorCode: '', message: 'Shift type list is retrieved for the shift allowance successfully.',shiftAllowanceShiftTypeList: shiftAllowanceShiftTypeList};
                            }
                        }else{
                            console.log('Response from the validateOvertimeWorkSchedule() function. ',validateOTWorkscheduleResponse);
                            /** If an error occured during overtime work schedule validation or overtime start date-time is not valid throw an error returned from the function. */
                        throw(validateOTWorkscheduleResponse.error);
                    }
                }else{
                    console.log("Shift allowance cannot be claimed for the overtime as the shift allowance applicable flag is disabled in the overtime settings.");
                    throw('OT0123');// throw error
                }
            }else{
                console.log('Empty or error response is returned from the getOvertimeSettings() function.', overtimeSettings);
                throw('OT0120');// throw error
            }
        }else{
            // throw validation error
            throw ('IVE0000');
        }
    }catch(listShiftAllowanceShiftTypeConfigCatchError){
        console.log('Error in the listShiftAllowanceShiftType() function main catch block',listShiftAllowanceShiftTypeConfigCatchError);
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
        if(listShiftAllowanceShiftTypeConfigCatchError === 'IVE0000'){
            console.log('Validation error in the listShiftAllowanceShiftType() function.',validationError);
            errResult = commonLib.func.getError('', listShiftAllowanceShiftTypeConfigCatchError);
            throw new UserInputError(errResult.message, { validationError: validationError });// return response
        }else{
            // get the error response for the error code
            let errResult = commonLib.func.getError(listShiftAllowanceShiftTypeConfigCatchError, 'OT0124');
            throw new ApolloError(errResult.message, errResult.code);// return response
        }
    }
};