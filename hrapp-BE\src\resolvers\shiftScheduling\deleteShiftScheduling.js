// delete the shift scheduling details
module.exports.deleteShiftScheduling = async (parent, args, context, info) => {
    console.log('Inside deleteShiftScheduling() function');
    const { ApolloError, UserInputError } = require('apollo-server-lambda');
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // require common constant files
    const { formName, roles, systemLogs } = require('../../../common/appconstants');
    // Organization database connection
    const knex = require('knex');
    // require table names
    const { ehrTables } = require('../../../common/tablealias');
    //get db connection
    const orgDb = knex(context.connection.OrganizationDb);

    try{            
        // Check form view access rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(orgDb, context.Employee_Id, formName.shiftScheduling, roles.roleDelete);
        
        if (checkRights === true) {
            
            let shiftSchedulingId = args.shiftSchedulingId;
            let lockedRecords = [];
            let invalidRecords = [];
            let deletedRecords = [];

            if(shiftSchedulingId.length > 0) {
                // Iterate the array and delete shift scheduling records from shift_emp_mapping table
                for(shiftId of shiftSchedulingId) {
                    let currentRecord = await orgDb.select("Lock_Flag", "Shift_Start_Date","Shift_Type_Id","Employee_Id")
                                                    .where("Shift_Schedule_Id",shiftId)
                                                    .from(ehrTables.shiftEmpMapping)
                                                    .then((response)=>{
                                                        return response;
                                                    });
                    // check exists or not
                    if(currentRecord.length === 0) {
                        invalidRecords.push(shiftId);
                    } else if (currentRecord[0] && currentRecord[0].Lock_Flag) { // check record is locked or not
                        lockedRecords.push(shiftId);
                    } else {
                        // check attendance exists for this shift
                        let getAttendanceDetails = await commonLib.employees.getAttendance(currentRecord[0].Shift_Start_Date, currentRecord[0].Employee_Id, orgDb);
                        
                        // If an error does not occured while getting the attendance details for the shift date
                        if(!getAttendanceDetails.errorCode){
                            let isAttendanceExists = getAttendanceDetails.attendanceDetails;
                            // Check leave already exist for this date
                            let isLeaveExists = await commonLib.employees.getLeaves(currentRecord[0].Employee_Id, currentRecord[0].Shift_Start_Date, currentRecord[0].Shift_Start_Date,'delete-shift',orgDb);

                            // check compensatory_off exist for this
                            let isCompensatoryOffExists = await commonLib.employees.getCompensatoryOff(currentRecord[0].Employee_Id, currentRecord[0].Shift_Start_Date, currentRecord[0].Shift_Start_Date, 'delete-shift', orgDb);

                            // check isAttendanceExists and isLeaveExists based on that perform action
                            if (isAttendanceExists || isLeaveExists || isCompensatoryOffExists) {
                                // Either attendance or leave record is exist then we should not allow to delete the records
                                invalidRecords.push(shiftId);
                            }else{
                                let isDeleted = await orgDb(ehrTables.shiftEmpMapping)
                                    .where("Shift_Schedule_Id", shiftId)
                                    .del()
                                    .then((res) => {
                                        return res;
                                    });
                                isDeleted ? deletedRecords.push(shiftId) : invalidRecords.push(shiftId);
                            }
                        }else{
                            console.log('Error response from the getAttendanceDetails() function for the shiftId,',shiftId,' is ,',getAttendanceDetails);
                        }
                    }
                }
                // form inputs to update system log activities commonly
                let systemLogParams = {
                    action: systemLogs.roleDelete,
                    userIp: context.User_Ip,
                    employeeId: context.Employee_Id,
                    formName: formName.shiftScheduling,
                    trackingColumn: '',
                    organizationDbConnection: orgDb,
                    uniqueId: deletedRecords
                };

                if(shiftSchedulingId.length == deletedRecords.length) {

                    // call function createSystemLogActivities() to update system log activities
                    await commonLib.func.createSystemLogActivities(systemLogParams);

                    // return response back to UI
                    return {
                        success : true,
                        errorCode : null,
                        message: "Shift schedule deleted successfully"
                    }
                } else if( deletedRecords.length > 0 ) {

                    // call function createSystemLogActivities() to update system log activities
                    await commonLib.func.createSystemLogActivities(systemLogParams);

                    // return response back to UI
                    return {
                        success : true,
                        errorCode : null,
                        message: "Shift scheduling records deleted partially due to the presence of either leave, compensatory off, or attendance or an error occurred while validating attendance for the shift date."
                    }
                } else {
                    let errResult = commonLib.func.getError('', 'SS0125');
                    return {
                        success : false,
                        errorCode : errResult.code,
                        message : errResult.message
                    }
                }
            } else {
                let errResult = commonLib.func.getError('', 'SS0124');
                throw new UserInputError(errResult.message, errResult.code)
            }
        } else if (checkRights === false) {
            let errResult = commonLib.func.getError('', '_DB0103');
            return {
                success : false,
                errorCode : errResult.code,
                message : errResult.message
            }
        } else {
            console.log("Error while checking rights in deleteShiftScheduling", checkRights);
            let errResult = commonLib.func.getError('', 'SS0135');
            return {
                success : false,
                errorCode : errResult.code,
                message : errResult.message
            }
        }
    } catch (listError) {
        console.log('Error in deleteShiftScheduling function main catch block',listError);
        let errResult  = commonLib.func.getError('', 'SS0126');
        throw new ApolloError(errResult.message,errResult.code )
    } finally {
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
    }
}