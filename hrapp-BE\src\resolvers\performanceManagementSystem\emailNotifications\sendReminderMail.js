'use strict';
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require aws package
const AWS = require("aws-sdk");
// require table alias
const { ehrTables } = require('../../../../common/tablealias');
// require constant file
const { awsSesTemplates,defaultValues } = require('../../../../common/appconstants');

// variable declarations
let organizationDbConnection='';
let response;
let redirectionURL;

// function to send email reminder notification
module.exports.sendReminderMail  = async(event, context) =>{
    try{
        console.log('Inside sendReminderMail function',event);
        // form inputs
        let inputData=event.input;
        let orgCodeList=inputData.orgCodeList;
        let mailListData;
        // check whether any organization list exist for sending email notification
        if(orgCodeList.length>0)
        {
            // iterate for all the instances
            for(let i=0;i<orgCodeList.length;i++){
                let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgCodeList[i]);
                console.log('additionalHeaders',additionalHeaders)
                // make database connection
                organizationDbConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCodeList[i].Org_Code,0,additionalHeaders);
                organizationDbConnection=knex(organizationDbConnection.OrganizationDb);

                // get all manager email address
                let getEmailArrayList=await commonLib.func.getAllManagersEmailAddress(organizationDbConnection);
                console.log('Response from getAllManagersEmailAddress function for '+orgCodeList[i]+' instance',getEmailArrayList);

                // if email address list not exist then iterate for other instances
                if(getEmailArrayList.length>0)
                {
                    // function to get email subject and content
                    let getEmailData=await getMailData(organizationDbConnection,inputData.process);
                    // check whether email content exist or not.
                    // if data not exist then process the next record
                    if(Object.keys(getEmailData).length>0){
                        // get organization details for sending mail
                        let orgDetails = await commonLib.func.getOrgDetails(orgCodeList[i].Org_Code, organizationDbConnection);
                        // form redirection URL
                        if (process.env.stageName.toLowerCase() === 'local') {
                            redirectionURL = 'http://localhost/in'+defaultValues.pmsRedirectionUrl;
                        } else {
                            redirectionURL = 'https://' + orgCodeList[i].Org_Code + '.' + process.env.domainName + process.env.webAddress +defaultValues.pmsRedirectionUrl;
                        }
                        // form input params
                        let inputParam={
                            getEmailArrayList: getEmailArrayList,
                            sourceEmail: process.env.emailFrom,
                            process: inputData.process,
                            orgDetails: orgDetails,
                            getEmailData:getEmailData,
                            redirectionURL:redirectionURL,
                            region:process.env.sesTemplatesRegion,
                            orgCode: orgCodeList[i].Org_Code
                        }
                        do
                        {
                            // form params to send mail
                            mailListData=await triggerBulkMail(inputParam);
                            console.log('Response after triggerBulkMail function',mailListData);
                        }
                        // iterate the list till all the records are processed
                        while(Object.keys(mailListData).length>0)
                        {
                            console.log('Email triggered for all the email address in '+orgCodeList[i]+' instance');
                            // destroy connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                        }
                    }
                    else{
                        console.log('Error while getting the email content for '+orgCodeList[i]+ ' instance so process the next record.');
                        // destroy connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                    }
                }
                else{
                    console.log('No managers email address found in '+orgCodeList[i]+' instance. So process the next instance.');
                    // destroy connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                }
            }
            response={
                nextStep:'End',
                message:'Email notification send successfully.'
            }
        }
        else{
            console.log('Input orgcode list is empty');
            response={
                nextStep:'End',
                message:'No records found so quit the process.'
            }
        }
        return response;
    }
    catch(mainCatchError){
        console.log('Error in sendReminderMail function main catch block.', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(mainCatchError, 'EPM0126');
        console.log('Error from step2:',errResult.code+" - "+errResult.message)
        response={
            nextStep:'End',
            message:'Error from step2 so quit the process.'
        }
        return response;
    }
};

// function to get the email content based on input orgcode
async function getMailData(organizationDbConnection,process){
    try{
        console.log('Inside getMailData function');
        // since same function is used for 2 different process goal/rating so define the table field
        let subjectFieldName=(process==='goal')?'Goal_Email_Subject':'Rating_Email_Subject';
        let contentFieldName=(process==='goal')?'Goal_Email_Content':'Rating_Email_Content';
        return(
            // get the email subject and content from settings table
            organizationDbConnection(ehrTables.performanceManagementSettings)
            .select(subjectFieldName+' as emailSubject',contentFieldName+' as emailContent')
            .then(getData=>{
                return (getData.length>0)?getData[0]:{};
            })
            .catch(catchError => {
                console.log('Error in getMailData function .catch block',catchError);
                return {};
            })
        );
    }
    catch(error){
        console.log('Error in getMailData function main catch block',error);
        return {};
    }
};

// function to form notification params and trigger mail
async function triggerBulkMail(event){
    try{
        console.log('Inside triggerBulkMail function',event);
        // create object for aws SES
        let region=event.region;
        let numberOfMailToBeTriggered;
        const ses = new AWS.SES({ region });
        let mailList=event.getEmailArrayList;
        // check whether the email address list exist or not
        if(Object.keys(mailList).length>0)
        {
            let destinationArray=[];
            // form params
            let notificationParams={
                "Source":event.sourceEmail,
                // differentiate the template based on process
                'Template': (event.process==='goal')?awsSesTemplates.performanceGoalsReminder:awsSesTemplates.performanceRatingsReminder,
                "DefaultTemplateData":JSON.stringify({
                        orgLogo: event.orgDetails.logoPath ? event.orgDetails.logoPath : '', 
                        emailContent: event.getEmailData.emailContent,   
                        redirectionUrl: event.redirectionURL,
                        emailSubject:event.getEmailData.emailSubject,
                        hrappSupportEmail: event.orgDetails.hrAdminEmailAddress ? event.orgDetails.hrAdminEmailAddress : ''
                })
            };
            // define the number of mail to be triggered at a particular time
            // Since 50 mail can be triggered for each call. So we will limit the count
            numberOfMailToBeTriggered=((Object.keys(mailList).length)>(defaultValues.maximumNumberOfMailTriggered))?defaultValues.maximumNumberOfMailTriggered:(Object.keys(mailList).length);
            console.log('numberOfMailToBeTriggered',numberOfMailToBeTriggered)
            // form destination array
            for(let i=0;i<numberOfMailToBeTriggered;i++)
            {
                destinationArray.push({"Destination":{"ToAddresses":[mailList[i]]}});
            }
            notificationParams.Destinations=destinationArray;
            console.log('notificationParams',destinationArray);
            if(Object.keys(mailList).length>0){
                // call the sendBulkTemplatedEmail function to send the email
                let response = await ses.sendBulkTemplatedEmail(notificationParams).promise();
                console.log('sendEmailNotifications response:', response); //Console Required
            }
            else{
                console.log('Mail list does not exists');
            }
        }
        // remove the email address for which email process is triggered
        mailList.splice(0,numberOfMailToBeTriggered);
        return (Object.keys(mailList).length>0)?mailList:{};
    }
    catch(error){
        console.log('Error in triggerBulkMail function main catch block',error);
        return {};
    }
};
