// require apollo errors
const { ApolloError } = require('apollo-server-lambda');
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
// require moment timezone
let moment = require('moment-timezone');
// require app constants
let { defaultDateFormat } = require('../../../common/appconstants');
// resolver function to get the overtime pre-requisites
const resolvers = {
    Query: {
        // function to get employee shift, leave and comp off and attendance details
        getOvertimePrerequisites: async (parent, args, context, info) => {
            console.log('Inside getOvertimePrerequisites() function');
            // get db connection
            let orgDb = knex(context.connection.OrganizationDb);
            try {
                // get input values
                let { employeeId } = args;
                let response = {};
                let shiftDetails = {};
                let getAttendanceDetails = [];
                let leaveDetails = [];
                let compOffDetails = [];
                let shiftDate = '';
                if(args.shiftDate){
                    shiftDate = args.shiftDate;
                }else if(args.otStartTime){
                    shiftDate = args.otStartTime;
                }
                else{
                    shiftDate = '';
                }
                // Check employeeId and shiftDate is exist or not. If not throw an error
                if(employeeId && shiftDate){
                    let workScheduleDetails = {};
                    //Get workSchedule details
                    if(args.otStartTime){
                        workScheduleDetails = await commonLib.employees.getCurrentWorkScheduleDetails(employeeId, shiftDate, orgDb);
                    }else{
                        workScheduleDetails = await commonLib.employees.getGraceTimeDetails(employeeId, null, shiftDate, orgDb);
                    }

                    if(Object.keys(workScheduleDetails).length > 0){
                        let { regularFrom, regularTo, considerationFrom, considerationTo, title, overtimeCoolingPeriod } = workScheduleDetails;

                        if(regularFrom && regularTo && considerationFrom && considerationTo){
                            // convert the datetime, "2021-01-14T21:00:00.000Z" to "2020-09-09 23:50:00"
                            let considerationFromDateTime =considerationFrom.toISOString().replace(/T/, ' ').replace(/\..+/, '');
                            let considerationToDateTime =considerationTo.toISOString().replace(/T/, ' ').replace(/\..+/, '');
                            let regularFromDateTime =regularFrom.toISOString().replace(/T/, ' ').replace(/\..+/, '');
                            let regularToDateTime =regularTo.toISOString().replace(/T/, ' ').replace(/\..+/, '');
                            // convert from dateTime to date format
                            let regularFromDate = moment(regularFromDateTime).format('YYYY-MM-DD');
                            // get mysql date format based on the organization date format
                            let dateFormat = await commonLib.func.getOrgDetails(context.Org_Code,orgDb,0);
                            // check Date_Format is exist or not.if not form default date format
                            dateFormat = dateFormat.Date_Format ? dateFormat.Date_Format : defaultDateFormat.dateFormat;                    
                            // form regularFrom,regularTo based on the organization date format
                            shiftDetails.regularFrom = moment(regularFromDateTime).format(dateFormat + ' HH:mm');
                            shiftDetails.regularTo = moment(regularToDateTime).format(dateFormat + ' HH:mm');
                            shiftDetails.considerationFrom = moment(considerationFromDateTime).format(dateFormat + ' HH:mm');
                            shiftDetails.considerationTo = moment(considerationToDateTime).format(dateFormat + ' HH:mm');
                            shiftDetails.overtimeCoolingPeriod = overtimeCoolingPeriod ? overtimeCoolingPeriod :'';
                            shiftDetails.title = title ? title :'';
                            // append shiftDetails with response
                            response.shiftDetails = shiftDetails;
                            /** Get attendance details. Regular from date and current work schedule details has to be sent. 
                             * The attendance details will be fetched based on the consideration start and end time. */
                            getAttendanceDetails = await commonLib.employees.getAttendance(regularFromDate, employeeId, orgDb,'get-attendance-details',context.Org_Code,workScheduleDetails);
                            
                            if(!getAttendanceDetails.errorCode){
                                // append attendance details with response
                                response.attendanceDetails = getAttendanceDetails.attendanceDetails;

                                // get leave details for the regular from date
                                leaveDetails = await commonLib.employees.getLeaves(employeeId, regularFromDate,regularFromDate,'get-overtime-prerequisites',orgDb);
                                // append leave details with response
                                response.leaveDetails = leaveDetails;
                                for(let leave of leaveDetails){
                                    leave.Start_Date = moment(leave.Start_Date).format(dateFormat);
                                    leave.End_Date = moment(leave.End_Date).format(dateFormat);
                                }
                                // get compensatory off details for the regular from date
                                compOffDetails = await commonLib.employees.getCompensatoryOff(employeeId, regularFromDate, regularFromDate, 'get-overtime-prerequisites', orgDb);
                                for(let compOff of compOffDetails){
                                    compOff.Compensatory_Date = moment(compOff.Compensatory_Date).format(dateFormat);
                                    compOff.Duration = compOff.Duration === 1 ? 'Full Day' : 'Half Day' 
                                }
                                // append compensatory off details with response
                                response.compOffDetails = compOffDetails;
                                //destroy database connection
                                orgDb ? orgDb.destroy() : null;
                                // return response back to UI
                                return { errorCode: '', message: 'Overtime pre-requisites retrieved successfully', otPreRequisites: JSON.stringify(response)};
                            }else{
                                console.log('Response from the getAttendance() function',getAttendanceDetails);
                                throw (getAttendanceDetails.errorCode);
                            }
                        }else{
                            let workScheduleError = (Object.keys(workScheduleDetails).length > 0 && workScheduleDetails.errorCode) ? workScheduleDetails.errorCode : 'OT0107';

                            // throw shift is not scheduled for this date
                            throw (workScheduleError);//OT0128, OT0107
                        }
                    }else{
                        // throw shift is not scheduled for this date
                        throw ('OT0107');
                    }
                }else{
                    // throw error if input not exists
                    throw ('OT0115');
                }                
            }catch(getOvertimePrerequisitesMainCatchError) {
                console.log('Error in getOvertimePrerequisites() function main catch block.', getOvertimePrerequisitesMainCatchError);
                // destroy database connection
                orgDb ? orgDb.destroy() : null;
                let errResult = commonLib.func.getError(getOvertimePrerequisitesMainCatchError, 'OT0114');
                throw new ApolloError(errResult.message, errResult.code)
            }
        }
    }
};
exports.resolvers = resolvers;