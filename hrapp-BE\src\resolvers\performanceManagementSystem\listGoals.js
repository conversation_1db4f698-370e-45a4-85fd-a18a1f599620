// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../../common/appconstants');
// require table alias
const { ehrTables } = require('../../../common/tablealias');

// variable declarations
let errResult = {};
let organizationDbConnection = '';

// resolver definition
const resolvers = {
    Query: {
        // function to list the performance goals
        listGoals: async (parent, args, context, info) => {
            try{
                console.log('Inside listGoals function');
                // get the organization data base connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                let logInEmpId = context.Employee_Id;
                // check whether loggedIn employee have super admin access
                let checkSuperAdminAccess = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.superAdmin, '', 'UI');
                
                // check whether the employee has view access to goalsAndAchievement form or not & loggedIn employee should be either employee admin or manager
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.goalsAndAchievement, '', 'UI');
                
                // form sub query to get goals detail
                let subQuery=organizationDbConnection(ehrTables.performanceGoalsLibrary + ' as PGL')
                .select('PGL.Goal_Id as goalId','PGL.Goal_Description as goalDescription','PGL.Status as status')

                // If loggedIn employee have super admin access then list all the goals in the organization level
                if ((Object.keys(checkSuperAdminAccess).length >0 && checkSuperAdminAccess.Employee_Role==='admin')) 
                {
                    subQuery=subQuery
                }
                // if loggedIn employee is either manager or admin(not super admin) then retrieve only the goals which are added to the employee alone
                else if ((Object.keys(checkRights).length >0 && checkRights.Role_View===1) && (checkRights.Is_Manager===1 || checkRights.Employee_Role==='admin')) {
                    subQuery=subQuery.where('Added_By',logInEmpId)
                }
                else{
                    throw '_DB0100';
                }
                return(
                    subQuery
                    .then(async(getGoalDetails)=>{
                        let goalsTotalRecord = getGoalDetails.length;
                        if(goalsTotalRecord>0){
                            for(let i=0; i<goalsTotalRecord; i++){
                                //Get the performance goal count which is associated with the performance record
                                let goalAssociatedRecordResult =
                                await organizationDbConnection(ehrTables.assessmentCycleGoalRatings)
                                .count('Goal_Id as goalIdCount')
                                .where('Goal_Id',getGoalDetails[i].goalId)
                                .then();
                                let isGoalAssociated = (goalAssociatedRecordResult.length > 0) ? ((goalAssociatedRecordResult[0].goalIdCount > 0) ? 1 : 0) : 0;
                                getGoalDetails[i].isGoalAssociated=isGoalAssociated;
                            }
                        }
                        // destroy database connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return {errorCode:'',message:'Performance goals retrieved successfully.',goalsList:getGoalDetails};
                    })
                    .catch(function (catchError) {
                        console.log('Error in listGoals function .catch block',catchError);
                        errResult = commonLib.func.getError(catchError, 'EPM0104');
                        // destroy database connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        // return response
                        throw new ApolloError(errResult.message,errResult.code);
                    })            
                );
            }catch(mainCatchError){
                console.log('Error in listGoals function main catch block',mainCatchError);
                errResult = commonLib.func.getError(mainCatchError, 'EPM0006');
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                // return response
                throw new ApolloError(errResult.message,errResult.code);
            }
        }
    }
};

exports.resolvers = resolvers;
