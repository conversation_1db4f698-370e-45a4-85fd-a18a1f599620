// resolver function to list the overtime records of the employee
module.exports.listOvertimeDetails = async (parent, args, context, info) => {
    console.log('Inside listOvertimeDetails() function');
    const { ApolloError } = require('apollo-server-lambda');
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // require common constant files
    const { formName, formIds } = require('../../../common/appconstants');
    // Organization database connection
    const knex = require('knex');
    // require table names
    const { ehrTables } = require('../../../common/tablealias');
    // require common function
    const { formTotalHours, getWorkScheduleTitle} = require('./overtimeCommonFunctions');
    // get db connection
    const orgDb = knex(context.connection.OrganizationDb);
    try{
        // get login employee Id
        let loginEmpId = context.Employee_Id;
        let isAdmin = 0;
        // Check form view access rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(orgDb, loginEmpId, formName.overtime, '', 'UI');
        // check checkRights is not empty json or not
        if (Object.keys(checkRights).length === 0) {
            // throw access denied error
            throw ('_DB0100');
        } else {
            // check loggedIn employee is  admin or not
            if (checkRights.Role_View === 1 && (checkRights.Employee_Role.toLowerCase() === 'admin')) {
                isAdmin = 1;
            } 
            // check logged-in employee is employee admin or not
            let checkEmpAdminRights = await commonLib.func.checkEmployeeAccessRights(orgDb, loginEmpId, formName.employeeAdmin, '', 'UI');
            // check employee admin rights
            if (Object.keys(checkEmpAdminRights).length > 0 && checkEmpAdminRights.Role_Update === 1) {
                isAdmin = 1;
            }
            // if not admin check view rights alone exist or not
            if (checkRights.Role_View === 1 || isAdmin) {
                // check filterMonth and year is exist or not
                if (args.filterMonth && args.filterYear){
                    let filterMonth = args.filterMonth;
                    filterMonth = filterMonth > 9 ? filterMonth : '0'+filterMonth;
                    // form start and end date based on the month and year chosen from the UI (Check payscycle type as well)
                    let { Salary_Date, Last_SalaryDate } = await commonLib.func.getSalaryDay(context.Org_Code, orgDb, filterMonth, args.filterYear);  
                    let salaryStartDate = Salary_Date;
                    let salaryEndDate = Last_SalaryDate;    
                    // Get mysql date format based on the organization date format       
                    let dateFormat = await commonLib.func.formMySqlDateFormat(context.Org_Code, orgDb);     
                    // getover time records
                    let subQuery = orgDb.select
                                    (
                                        'OT.Overtime_Claim_Id', 'OT.Employee_Id','OT.Total_Hours','OT.Overtime_Wage','EP.Photo_Path','EJ.Manager_Id',
                                        'OT.Reason','OT.Shift_Allowance_Applied_Work_Schedule_Id',
                                        'OT.First_Level_Approver as First_Level_Approver_Id', 'EJ1.User_Defined_EmpId as First_Level_Approver_User_Defined_EmpId',
                                        'EJ2.User_Defined_EmpId as Second_Level_Approver_User_Defined_EmpId','OT.Second_Level_Approver as Second_Level_Approver_Id', 'OT.Approval_Status',
                                        'EJ.User_Defined_EmpId','OT.Approved_By','OT.Updated_By','OT.Added_By',
                                        orgDb.raw("CONCAT_WS(' ',EP.Emp_First_Name,EP.Emp_Middle_Name, EP.Emp_Last_Name) as Employee_Name"),
                                        orgDb.raw("CONCAT_WS(' ',EP1.Emp_First_Name,EP1.Emp_Middle_Name, EP1.Emp_Last_Name) as Added_Emp_Name"),
                                        orgDb.raw("DATE_FORMAT(OT.Added_On, '%m-%d-%Y - %H:%i') as Added_On"),
                                        orgDb.raw("CONCAT_WS(' ',EP2.Emp_First_Name,EP2.Emp_Middle_Name, EP2.Emp_Last_Name) as Updated_Emp_Name"), 
                                        orgDb.raw("DATE_FORMAT(OT.Updated_On, '%m-%d-%Y - %H:%i') as Updated_On"),
                                        orgDb.raw("CONCAT_WS(' ',EP3.Emp_First_Name,EP3.Emp_Middle_Name, EP3.Emp_Last_Name) as Approved_Emp_Name"),
                                        orgDb.raw("DATE_FORMAT(OT.Approved_On, '%m-%d-%Y - %H:%i') as Approved_On"),
                                        orgDb.raw("CONCAT_WS(' ',EP4.Emp_First_Name,EP4.Emp_Middle_Name, EP4.Emp_Last_Name) as First_Level_Approver"),
                                        orgDb.raw("CONCAT_WS(' ',EP5.Emp_First_Name,EP5.Emp_Middle_Name, EP5.Emp_Last_Name) as Second_Level_Approver"),
                                        orgDb.raw(`DATE_FORMAT(OT.Start_Date_Time, ?) as Start_Date_Time`, [dateFormat]),
                                        orgDb.raw(`DATE_FORMAT(OT.End_Date_Time, ?) as End_Date_Time`, [dateFormat])
                                    )
                                    .from(ehrTables.overtime + ' as OT')
                                    .leftJoin(ehrTables.empJob + ' as EJ', 'OT.Employee_Id', 'EJ.Employee_Id')
                                    .leftJoin(ehrTables.empJob + ' as EJ1', 'OT.First_Level_Approver', 'EJ1.Employee_Id')
                                    .leftJoin(ehrTables.empJob + ' as EJ2', 'OT.Second_Level_Approver', 'EJ2.Employee_Id')
                                    .leftJoin(ehrTables.empPersonal + ' as EP','OT.Employee_Id', 'EP.Employee_Id')
                                    .leftJoin(ehrTables.empPersonal + ' as EP1', 'OT.Added_By', 'EP1.Employee_Id')
                                    .leftJoin(ehrTables.empPersonal + ' as EP2', 'OT.Updated_By', 'EP2.Employee_Id')
                                    .leftJoin(ehrTables.empPersonal + ' as EP3', 'OT.Approved_By', 'EP3.Employee_Id')
                                    .leftJoin(ehrTables.empPersonal + ' as EP4', 'OT.First_Level_Approver', 'EP4.Employee_Id')
                                    .leftJoin(ehrTables.empPersonal + ' as EP5', 'OT.Second_Level_Approver', 'EP5.Employee_Id')
                                    .orderBy('OT.Start_Date_Time', 'desc')
                                    .where((qb) => {
                                        // form start and end date to filter the records based on the date chosen in the UI
                                        let startDate = args.startDate;
                                        let endDate = args.endDate;
                                        // check startDate and endDate is from UI input
                                        if (startDate && endDate){
                                            // if startDate is greater than Salary_Date then startDate is the mininum date
                                            if (startDate > Salary_Date){
                                                Salary_Date = startDate;
                                            }
                                            // if endDate is lessthan than Last_SalaryDate then endDate is the maximum date
                                            if (endDate < Last_SalaryDate){
                                                Last_SalaryDate = endDate;
                                            }
                                        }
                                        // Apply filter if Salary_Date and Last_SalaryDate is exist
                                        if (Salary_Date && Last_SalaryDate) {
                                            // form date format to check in the table
                                            Salary_Date = Salary_Date + ':00:00:00';
                                            Last_SalaryDate = Last_SalaryDate + ':23:59:59';
                                            qb.whereBetween('OT.Start_Date_Time', [Salary_Date, Last_SalaryDate])
                                            qb.orWhereBetween('OT.End_Date_Time', [Salary_Date, Last_SalaryDate])
                                        }
                                    });
                    // check if the loggedIn employee is admin then get all employee records
                    if(!isAdmin){
                        // If the loggedIn employee is not an admin, then check logedIn employee has any records to approve. if yes return those records.
                        // If no records to approve then check any records exist of theirs. if yes return those records Otherwise return empty []
                        subQuery = subQuery
                                    .where((qb) => {
                                        qb.where('OT.Employee_Id', loginEmpId)
                                        qb.orWhere('OT.First_Level_Approver', loginEmpId)
                                        qb.orWhere('OT.Second_Level_Approver', loginEmpId)
                                    });           
                    }
                    // Apply status filter if status array from input is not empty
                    if (args.status.length) {
                        subQuery.whereIn('OT.Approval_Status', args.status)
                    }
                    return (
                        subQuery
                        .then(async (overtimeRecords) => {
                            let shiftTypeWorkScheduleTitle = '';
                            // iterate the records and get the comments of exists
                            for (let otRecords of overtimeRecords){
                                shiftTypeWorkScheduleTitle = '';
                                otRecords.Total_Hours_From_Table = otRecords.Total_Hours;
                                let totalHours = otRecords.Total_Hours;
                                let totalHoursInSeconds = totalHours*60*60;
                                // form the total hours in hh:mm format
                                otRecords.Total_Hours = await formTotalHours(totalHoursInSeconds);                                
                                // get the comment from comment_generation table(send uniqueId , formId and dbConnection as params)
                                let comment = await commonLib.func.getComments(otRecords.Overtime_Claim_Id, formIds.overtime, null, orgDb);
                                otRecords.comments = comment.Emp_Comment ? comment.Emp_Comment : '';
                                // check Photo_Path exist or not
                                if(otRecords.Photo_Path){
                                    let fileName = await commonLib.func.formS3FilePath(otRecords.Photo_Path, context.Org_Code, 'profile', '', process.env.domainName);
                                    // get the employee profile picture signed url
                                    otRecords.Photo_Path = fileName ? await commonLib.func.getFileURL(process.env.region, process.env.hrappProfileBucket, fileName) : '';
                                }
                                //get workschedule title associated with the workschedule id
                                shiftTypeWorkScheduleTitle = (otRecords.Shift_Allowance_Applied_Work_Schedule_Id) ? await getWorkScheduleTitle(orgDb,otRecords.Shift_Allowance_Applied_Work_Schedule_Id) : '';
                                otRecords.Shift_Type_Work_Schedule_Title = shiftTypeWorkScheduleTitle;
                            }
                            // return response back to UI
                            return { errorCode: '', message: 'Overtime records retrieved successfully', overtimeDetails:overtimeRecords.length ? JSON.stringify(overtimeRecords): "", salaryStartDate:salaryStartDate,salaryEndDate:salaryEndDate };
                        })
                        .catch(function (listOvertimeDetailsInsideCatchError) {
                            console.log('Error in listOvertimeDetails function .catch block', listOvertimeDetailsInsideCatchError);
                            let errResult = commonLib.func.getError(listOvertimeDetailsInsideCatchError, 'OT0001');
                            throw new ApolloError(errResult.message, errResult.code)
                        })
                        .finally(()=>{
                            // destroy database connection
                            orgDb ? orgDb.destroy() : null;
                        })
                    )
                } else {
                    // throw error if month and year is empty/null/undefined/0
                    throw ('OT0101');
                }
            } else {
                // throw access denied error
                throw ('_DB0100');
            }
        }
    } catch (listOvertimeDetailsMainCatchError){
        console.log('Error in listOvertimeDetails() function main catch block.',listOvertimeDetailsMainCatchError);
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
        let errResult = commonLib.func.getError(listOvertimeDetailsMainCatchError, 'OT0101');
        throw new ApolloError(errResult.message, errResult.code)
    }    
};