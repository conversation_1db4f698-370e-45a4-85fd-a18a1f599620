// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require common constant files
const { formIds, systemLogs } = require('../../../common/appconstants');
//require moment
const moment = require('moment')
// require common constant files
const { checkAttendanceLeaveCompOff,validateShiftOverlap } = require('../shiftSwap/statusApprovalCommonFunction');
// require common functions
const { validateCommonRuleInput }= require('../../../common/commonFunction');
module.exports.addUpdateEmployeeShiftSwap = async (parent, args, context) => {
  console.log('Inside addUpdateEmployeeShiftSwap function:');
  let organizationDbConnection;
  let validationError = {};
  let shiftOverlapErrorMessage = '';

  try {
    const { Employee_Id: loginEmployeeId, User_Ip: userIp } = context
    organizationDbConnection = knex(context.connection.OrganizationDb)

    // Check access rights
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      '',
      '',
      'UI',
      false,
      formIds.shiftSwap
    )

    if (
      !Object.keys(checkRights).length ||
      (args.swapId && checkRights.Role_Update === 0) ||
      (!args.swapId && checkRights.Role_Add === 0)
    ) {
      throw new Error(!args.swapId ? '_DB0101' : '_DB0102')
    }

    const tomorrowUtc = moment.utc().add(1, 'day').startOf('day')

    // Validation checks for the swap date
    if (!moment.utc(args.swapDate).isValid()) {
      validationError['IVE0510'] = commonLib.func.getError(
        'IVE0510',
        ''
      ).message
    }

    let rosterSettings = await organizationDbConnection(ehrTables.rosterSettings).select('*').first();

    if(rosterSettings && rosterSettings.Allow_Past_Shift_Swaps.toLowerCase() === 'no' 
    && moment.utc(args.swapDate).isBefore(tomorrowUtc)) {
      validationError['IVE0511'] = commonLib.func.getError(
        'IVE0511',
        ''
      ).message
    }

    let validationArgs = {
      shiftSwapReason: args.reason
    };

    // Validation rules
    let fieldValidations = {
      shiftSwapReason: "IVE0523"
    };
    if(args.reason){
      let commonValidationError = await validateCommonRuleInput(validationArgs, fieldValidations);
      if(commonValidationError && Object.keys(commonValidationError).length > 0){
        validationError = {...validationError, ...commonValidationError};
      }
    }else{
      validationError['IVE0523'] = commonLib.func.getError('', 'IVE0523').message2;
    }

    // Get employee shift mapping data
    const employeeShiftMapping = await getEmployeeShiftMapping(
      organizationDbConnection,
      args.employeeId,
      args.swapDate
    )

    if (
      employeeShiftMapping.length > 0 &&
      employeeShiftMapping[0]?.Shift_Type_Id === args.swapShiftTypeId
    ) {
      validationError['IVE0512'] = commonLib.func.getError(
        'IVE0512',
        ''
      ).message
    }
    if (Object.keys(validationError).length > 0) {
      throw 'IVE0000'
    }

    let validationInputs = {
      employeeId :args.employeeId,
      shiftDate: args.swapDate
    };
    await checkAttendanceLeaveCompOff(organizationDbConnection,validationInputs);

    // Check for duplicate swaps
    const duplicateRecord = await checkDuplicateSwap(
      organizationDbConnection,
      args.employeeId,
      args.swapDate,
      args.swapId
    )

    if (duplicateRecord.length > 0) {
      throw 'ESS0053'
    }

    let overlapValidationInputs = { 
      organizationDbConnection, 
      employeeId:  args.employeeId, 
      swapDate: args.swapDate,
      shiftTypeId: args.swapShiftTypeId, 
      currentShiftScheduleId: employeeShiftMapping[0]?.Shift_Schedule_Id 
    };

    //Validate shift overlap
    let overlapResult = await validateShiftOverlap(overlapValidationInputs);
    if(overlapResult.overlapErrorCode){
      console.log('Shift overlap error in addUpdateEmployeeShiftSwap function:', overlapResult)
      shiftOverlapErrorMessage = overlapResult.overlapErrorMessage;
      throw 'SS0160';
    }

    // Check within the restriction period
    await checkWithinRestrictionPeriod(organizationDbConnection, args)

    const swapData = {
      Employee_Id: args.employeeId,
      Approver_Id: args.approverId,
      Swap_Shift_Type_Id: args.swapShiftTypeId,
      Swap_Date: args.swapDate,
      Reason: args.reason,
      Approval_Status: 'Applied'
    }

    // Insert or update swap data based on swapId
    if (args.swapId === 0 || args.swapId === null) {
      swapData.Added_By = loginEmployeeId
      swapData.Added_On = moment().utc().format('YYYY-MM-DD HH:mm:ss')
      const insertResult = await organizationDbConnection(
        ehrTables.employeeShiftSwap
      ).insert(swapData)
      if (!insertResult) throw 'ESS0051'
    } else {
      swapData.Updated_On = moment().utc().format('YYYY-MM-DD HH:mm:ss')
      swapData.Updated_By = loginEmployeeId

      const updateResult = await organizationDbConnection(ehrTables.employeeShiftSwap)
        .where('Swap_ID', args.swapId)
        .update(swapData)

      if (!updateResult) throw 'ESS0052'
    }

    const actionMessage = args.swapId ? 'updated' : 'added'

    // Log activity
    await commonLib.func.createSystemLogActivities({
      userIp,
      employeeId: loginEmployeeId,
      organizationDbConnection,
      message: `Employee shift swap ${actionMessage} successfully`
    })

    return {
      errorCode: '',
      message: `Employee shift swap ${actionMessage} successfully`
    }
  } catch (error) {
    console.log('Error in addUpdateEmployeeShiftSwap function:', error)

    if (error === 'IVE0000') {
      const errResult = commonLib.func.getError('', 'IVE0000')
      throw new UserInputError(errResult.message, { validationError })
    } else if (error === 'SS0160'){
      throw new ApolloError(shiftOverlapErrorMessage, 'SS0160');
    }else {
      const errResult = commonLib.func.getError(error, 'ESS0050')
      throw new ApolloError(errResult.message, errResult.code)
    }
  } finally {
    if (organizationDbConnection) organizationDbConnection.destroy()
  }
}

// Function to check for existing shift mapping for employee and swap date
async function getEmployeeShiftMapping(
  organizationDbConnection,
  employeeId,
  swapDate
) {
  return await organizationDbConnection
    .select('*')
    .from(ehrTables.shiftEmpMapping + ' as SE')
    .where('SE.Employee_Id', employeeId)
    .andWhere('SE.Shift_Start_Date', swapDate)
}

// Function to check for duplicate swap records for the same employee and swap date
async function checkDuplicateSwap(
  organizationDbConnection,
  employeeId,
  swapDate,
  swapId
) {
  let duplicateCheckQuery = organizationDbConnection
    .select('Swap_ID')
    .from(ehrTables.employeeShiftSwap)
    .where({
      Employee_Id: employeeId,
      Swap_Date: swapDate
    })
    .whereIn('Approval_Status', ['Approved', 'Applied'])

  if (swapId) {
    duplicateCheckQuery.andWhere('Swap_ID', '!=', swapId)
  }

  return await duplicateCheckQuery
}

async function checkWithinRestrictionPeriod(organizationDbConnection, args) {
  try {
    const rosterManagementSettings = await organizationDbConnection
      .select('Enable_Shift_Swap_Restriction', 'Max_Swap_Requests_Per_Month')
      .from(ehrTables.rosterSettings)
      .first()

    if (rosterManagementSettings) {
      const { Enable_Shift_Swap_Restriction, Max_Swap_Requests_Per_Month } =
        rosterManagementSettings
      if (Enable_Shift_Swap_Restriction === 'Yes') {
        const currentMonthStart = moment(args.swapDate)
          .startOf('month')
          .format('YYYY-MM-DD')
        const currentMonthEnd = moment(args.swapDate)
          .endOf('month')
          .format('YYYY-MM-DD')

        const swapCountQuery = organizationDbConnection
          .count('Swap_ID as count')
          .from(ehrTables.employeeShiftSwap)
          .where('Swap_Date', '>=', currentMonthStart)
          .andWhere('Swap_Date', '<=', currentMonthEnd)
          .whereIn('Approval_Status', ['Approved', 'Applied'])
        if (args.swapId) {
          swapCountQuery.andWhere('Swap_ID', '!=', args.swapId)
        }

        const swapCountInMonth = await swapCountQuery.first()
        if (swapCountInMonth.count >= Max_Swap_Requests_Per_Month) {
          throw 'ESS0057'
        }
      }
    } else {
      throw 'ESS0056'
    }
  } catch (error) {
    console.log('Error in checkWithinRestrictionPeriod function:', error)
    throw error
  }
}
