// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const commonValidation = require('../../../common/commonvalidation');

module.exports = {
    serviceProviderValidation : async(args,context) =>{
        let validationError = {};
        // variable declaration
        let {isEditForm,serviceProviderId,serviceProviderName,contactPersonName,contactNumber,emailId,website,serviceProviderLogo,serviceProviderMou,locationId} = args;
        if(isEditForm){
            // validate service provider id
            if(serviceProviderId){
                if (!commonValidation.numberValidation(serviceProviderId)) {
                    validationError['IVE0182'] = commonLib.func.getError('', 'IVE0182').message;
                }
            }
            else{
                validationError['IVE0182'] = commonLib.func.getError('', 'IVE0182').message;
            }
        }
        // validate service provider name
        if(serviceProviderName){
            let lengthValidate = commonValidation.checkLength(serviceProviderName,1,50);
            let nameValidate=commonValidation.commentValidation(serviceProviderName);
            if (!lengthValidate || !nameValidate) {
                validationError['IVE0183'] = commonLib.func.getError('', 'IVE0183').message;
            }
        }
        else{
            validationError['IVE0183'] = commonLib.func.getError('', 'IVE0183').message;
        }
        // validate contact person name
        if(contactPersonName){
            if(!commonValidation.nameValidation(contactPersonName)){
                validationError['IVE0053'] = commonLib.func.getError('', 'IVE0053').message4;
            }
            else if(!commonValidation.checkLength(contactPersonName,1,50)) {
                validationError['IVE0053'] = commonLib.func.getError('', 'IVE0053').message3;
            }
        }
        else{
            validationError['IVE0053'] = commonLib.func.getError('', 'IVE0053').message4;
        }
        // validate contact number
        if(contactNumber){
            if(!commonValidation.mobileNumberValidation(contactNumber)){
                validationError['IVE0126'] = commonLib.func.getError('', 'IVE0126').message2;
            }
        }
        else{
            validationError['IVE0126'] = commonLib.func.getError('', 'IVE0126').message1;
        }
        // validate email address
        if(emailId){
            if (!commonValidation.emailValidation(emailId)) {
                validationError['IVE0052'] = commonLib.func.getError('', 'IVE0052').message;
            }
            else if(!commonValidation.checkLength(emailId,1,50)) {
                validationError['IVE0052'] = commonLib.func.getError('', 'IVE0052').message3;
            }
        }
        else{
            validationError['IVE0052'] = commonLib.func.getError('', 'IVE0052').message2;
        }
        // validate locationId when it is add form
        if(!isEditForm){
            if(locationId){
                if (!commonValidation.numberValidation(locationId)) {
                    validationError['IVE0181'] = commonLib.func.getError('', 'IVE0181').message;
                }
            }
            else{
                validationError['IVE0181'] = commonLib.func.getError('', 'IVE0181').message;
            }    
        }

        if(website){
            if(!commonValidation.checkLength(website,1,300)){
                validationError['IVE0184'] = commonLib.func.getError('', 'IVE0184').message;
            }
        }

        if(serviceProviderLogo){
            if(!commonValidation.checkLength(serviceProviderLogo,1,50)){
                validationError['IVE0185'] = commonLib.func.getError('', 'IVE0185').message;
            }
        }

        if(serviceProviderMou){
            if(!commonValidation.checkLength(serviceProviderMou,1,50)){
                validationError['IVE0186'] = commonLib.func.getError('', 'IVE0186').message;
            }
        }
        return validationError;
    }
}