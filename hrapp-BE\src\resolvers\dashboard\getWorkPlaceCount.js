const { ehrTables } = require('../../../common/tablealias');
const moment = require('moment-timezone');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { formName, roles } = require('../../../common/appconstants');
const knex = require('knex');
module.exports.getWorkPlaceCount = async(parent, args, context, info) => {
    try{
        // get the organization data base connection
        let orgDb = knex(context.connection.OrganizationDb);
        let logInEmpId = context.Employee_Id;

        let isAdmin = await commonLib.func.checkEmployeeAccessRights(orgDb, logInEmpId, formName.admin, roles.roleUpdate);
        let isEmployeeAdmin = await commonLib.func.checkEmployeeAccessRights(orgDb, logInEmpId, formName.employeeAdmin, roles.roleUpdate);

        let empDetails = await commonLib.func.getAllEmployeeDetails(orgDb,'getuseridandname',null,null,null,null,{employeeId:[logInEmpId]},null)
        let isManager = (empDetails[0] && empDetails[0].is_manager) ? true : false;

        if(isAdmin || isEmployeeAdmin || isManager)
        {
            let timezone = await commonLib.func.getEmployeeTimeZone(logInEmpId, orgDb, 0);

            let todayDate = moment().tz(timezone).format('YYYY-MM-DD');

            let totalAttCountQry = orgDb.count('Attendance_Id as count')
                                    .from(ehrTables.attendance)
                                    .where('PunchIn_Date', todayDate)
                                    .first()
            
            let workPlaceCountQry = orgDb.count('Attendance_Id as count')
                                        .from(ehrTables.attendance)
                                        .where('PunchIn_Date', todayDate)
                                        .first()

            // If the employee is only the manager not an admin
            if(!isAdmin && !isEmployeeAdmin && isManager)
            {
                let employeeIds = await commonLib.func.getEmpIdBasedOnManagerId(orgDb, context.Org_Code, logInEmpId);

                totalAttCountQry = totalAttCountQry.whereIn('Employee_Id',employeeIds);

                workPlaceCountQry = workPlaceCountQry.whereIn('Employee_Id',employeeIds);
            }

            let totalAttCount = (await totalAttCountQry.select()).count;
            let wfhCount = (await workPlaceCountQry.clone().where('Checkin_Work_Place_Id', 1)).count;
            let officeCount = (await workPlaceCountQry.clone().where('Checkin_Work_Place_Id', 2)).count;
            let fieldCount = (await workPlaceCountQry.clone().where('Checkin_Work_Place_Id', 3)).count;
            let others = totalAttCount - ( wfhCount + officeCount + fieldCount )

            return {
                errorCode: '',
                message: 'Work place count retrieved successfully',
                workPlaceCount: {
                    office: officeCount,
                    field: fieldCount,
                    workFromHome: wfhCount,
                    others
                }
            }
        }
        else
        {
            let errResult = commonLib.func.getError('', '_DB0100');
            return {
                errorCode: errResult.code,
                message: errResult.message,
                workPlaceCount: null
            }
        }
    } catch(err) {
        console.log('Error in getWorkPlaceCount', err);
        let errResult = commonLib.func.getError('', 'DB0027');
        return {
            errorCode: errResult.code,
            message: errResult.message,
            workPlaceCount: null
        }
    }
}

module.exports.getAttendanceCount = async (parent, args, context, info) => {
    try{
        // get the organization data base connection
        let orgDb = knex(context.connection.OrganizationDb);

        let logInEmpId = context.Employee_Id;
        let timezone = await commonLib.func.getEmployeeTimeZone(logInEmpId, orgDb, 0);
        let todayDate = moment().tz(timezone).format('YYYY-MM-DD');

        let isAdmin = await commonLib.func.checkEmployeeAccessRights(orgDb, logInEmpId, formName.admin, roles.roleUpdate);
        let isEmployeeAdmin = await commonLib.func.checkEmployeeAccessRights(orgDb, logInEmpId, formName.employeeAdmin, roles.roleUpdate);

        let empDetails = await commonLib.func.getAllEmployeeDetails(orgDb,'getuseridandname',null,null,null,null,{employeeId:[logInEmpId]},null)
        let isManager = (empDetails[0] && empDetails[0].is_manager) ? true : false;

        if(isAdmin || isEmployeeAdmin || isManager)
        {
            let totalAttCountQry = orgDb.count('Attendance_Id as count')
                                        .from(ehrTables.attendance)
                                        .where('PunchIn_Date', todayDate)
                                        .first()

            let lateAttCountQry = orgDb.count('Attendance_Id as count')
                                            .from(ehrTables.attendance)
                                            .where('PunchIn_Date', todayDate)
                                            .whereIn('Late_Attendance', [1,2,3,4])
                                            .first()

            // If the employee is only the manager not an admin
            if(!isAdmin && !isEmployeeAdmin && isManager)
            {
                let employeeIds = await commonLib.func.getEmpIdBasedOnManagerId(orgDb, context.Org_Code, logInEmpId);

                totalAttCountQry = totalAttCountQry.whereIn('Employee_Id',employeeIds);

                lateAttCountQry = lateAttCountQry.whereIn('Employee_Id',employeeIds);
            }

            let totalAttCount = (await totalAttCountQry.select()).count;
            let lateAttCount = (await lateAttCountQry.select()).count;

            return {
                errorCode: '',
                message: 'Attendance count retrieved successfully',
                onTimeArrivals: totalAttCount - lateAttCount,
                lateArrivals: lateAttCount
            }
        }
        else
        {
            let errResult = commonLib.func.getError('', '_DB0100');
            return {
                errorCode: errResult.code,
                message: errResult.message,
                onTimeArrivals: null,
                lateArrivals: null
            }
        }
    } catch(err) {
        console.log("Error in getAttendanceCount", err);
        let errResult = commonLib.func.getError('', 'DB0028');
        return {
            errorCode: errResult.code,
            message: errResult.message,
            onTimeArrivals: null,
            lateArrivals: null
        }
    }
}