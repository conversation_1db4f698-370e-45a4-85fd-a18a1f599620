// require table alias
const {ehrTables} = require('./tablealias');
// require common constant files
const { defaultValues } = require('./appconstants');
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

/**
 * Return the employe id when the employee - performance goal achievement record exists in the 'performance_goal_achievement' table 
 * except the Assessment_Status "Rated" for the previous assessment month and the previous assessment year. Or when the employee 
 * performance goal achievement record exist for the given(input) assessment month and year in the 'performance_goal_achievement' table.
 * @param organizationDbConnection
 * @param assessmentMonth
 * @param assessmentYear
 * @returns {Promise<unknown>}
 */
async function getPmsAlreadyExistEmpIds(organizationDbConnection,assessmentMonth,assessmentYear){
    return new Promise((resolve,reject) => {
        /** Fetch the employee(s) id where employee performance should exist for the given or future assessment period or for the 
         * previous assessment period with the rating publish status empty or null or except the rating publish status 'Published'. 
        */
        organizationDbConnection(ehrTables.performanceGoalAchievement)
        .distinct()
        .pluck('Employee_Id')
        .where(qb => {
            qb.where('Performance_Assessment_Month','>=', assessmentMonth)
            qb.where('Performance_Assessment_Year', assessmentYear)
        })// where condition to get the current and future assessment period employees for the same year
        .orWhere(qb => {
            qb.where('Performance_Assessment_Month','>=', defaultValues.performanceDefaultStartMonth)
            qb.where('Performance_Assessment_Year','>',assessmentYear)
        })// where condition to get the future assessment period employees for the next year
        .orWhere(qb => {
            qb.where('Performance_Assessment_Month','<', assessmentMonth)
            qb.where('Performance_Assessment_Year', assessmentYear)
            qb.where( sqb => {
                sqb.whereNot('Rating_Publish_Status', defaultValues.goalsAndAchievementGoalPublishStatus)
                sqb.orWhere('Rating_Publish_Status','')
                sqb.orWhereNull('Rating_Publish_Status')
            })
        })// where condition to get the past assessment period-ratings unpublished employees for the same year
        .orWhere(qb => {
            qb.where('Performance_Assessment_Year','<', assessmentYear)
            qb.where( sqb => {
                sqb.whereNot('Rating_Publish_Status', defaultValues.goalsAndAchievementGoalPublishStatus)
                sqb.orWhere('Rating_Publish_Status','')
                sqb.orWhereNull('Rating_Publish_Status')
            })
        })// where condition to get the past assessment period-ratings unpublished employees for the past year(s)
        .then(pmsInvalidEmployeeIds =>{
            resolve(pmsInvalidEmployeeIds[0] ? pmsInvalidEmployeeIds : []);
            
        }).catch(error =>{
            console.log('Error in getPmsAlreadyExistEmpIds() function .catch block',error);
            reject(error);
        })
    })
}

// function to get rating description
async function getRatings(organizationDbConnection){
    try{
        return(
            organizationDbConnection(ehrTables.performanceRatingDescription)
            .select('Rating_Id','Rating_Description')
            .then(getRatings => {
                return (getRatings.length>0)?getRatings:[];
            })
            .catch(catchError => {
                console.log('Error in getRatings function .catch block',catchError);
                return [];
            })
        )
    }
    catch(error){
        console.log('Error in getRatings function main catch block',error);
        return [];
    }
};

// function to get employeeid based on the loggedIn employeeId
async function getEmployeeIdBasedOnReviewer(organizationDbConnection,logInEmpId){
    try{
        // get employeeid under the loggedIn employeeid
        return(
            organizationDbConnection(ehrTables.empJob)
            .pluck('Employee_Id')
            .where('Manager_Id',logInEmpId)
            .then(getId=>{
                return getId;
            })
            .catch(catchError => {
                console.log('Error in getEmployeeIdBasedOnReviewer function .catch block',catchError);
                return [];        
            })
        );
    }
    catch(error){
        console.log('Error in getEmployeeIdBasedOnReviewer function main catch block',error);
        return [];
    }
};

// get the employee performance ids for the given assessment period or for the performance assessment id
async function goalsPublishableOrDeletePerformanceIds(organizationDbConnection,loginEmpId,isLoginEmployeeAdmin,args,action){
    try {
        let goalsPublishableEmpIdQry = organizationDbConnection(ehrTables.performanceGoalAchievement)
        .distinct()
        .pluck('Performance_Assessment_Id');
        if(action === 'publishgoals'){
            goalsPublishableEmpIdQry = goalsPublishableEmpIdQry.where('Goal_Publish_Status', defaultValues.goalsAndAchievementGoalUnPublishStatus)
            .whereIn('Performance_Assessment_Id', args.performanceAssessmentId)
            .where('Performance_Assessment_Month', args.month)
            .where('Performance_Assessment_Year', args.year);
        }else if(action === 'delete'){
            goalsPublishableEmpIdQry = goalsPublishableEmpIdQry.where('Performance_Assessment_Id', args.performanceAssessmentId);
        }
        // if the login employee is not admin
        if(isLoginEmployeeAdmin!=='admin'){
            goalsPublishableEmpIdQry = goalsPublishableEmpIdQry
                .where('Reviewer_Employee_Id', loginEmpId);
        }

        // get the performance assessment ids
        let publishablePerformanceIdList = await goalsPublishableEmpIdQry
        .then(publishablePerformanceIdsList =>{
            return((publishablePerformanceIdsList && publishablePerformanceIdsList.length > 0) ? publishablePerformanceIdsList : []);
        }).catch(performanceIdCatchError =>{
            console.log('Error in goalsPublishableOrDeletePerformanceIds() function .catch block',performanceIdCatchError);
            throw performanceIdCatchError;
        });

        return publishablePerformanceIdList;
    }catch(performanceIdMainCatchError){
        console.log('Error in goalsPublishableOrDeletePerformanceIds() function main catch block',performanceIdMainCatchError);
        throw performanceIdMainCatchError;
    }
}

// get the employee goals and achievement details for the given assessment period or for the performance assessment id
async function ratingsPublishableEmpPerformanceDetails(organizationDbConnection,loginEmpId,isLoginEmployeeAdmin,args){
    try {
        let ratingsPublishableEmpIdQry = organizationDbConnection(ehrTables.performanceGoalAchievement)
        .select('Performance_Assessment_Id','Employee_Id','Performance_Assessment_Month','Performance_Assessment_Year')
        .whereIn('Performance_Assessment_Id', args.performanceAssessmentId)
        .where('Goal_Publish_Status', defaultValues.goalsAndAchievementGoalPublishStatus)
        .where('Rating_Publish_Status', defaultValues.goalsAndAchievementGoalUnPublishStatus);

        // if the login employee is not admin
        if(isLoginEmployeeAdmin!=='admin'){
            ratingsPublishableEmpIdQry = ratingsPublishableEmpIdQry.where('Reviewer_Employee_Id', loginEmpId);
        }

        // get the ratings publishable employee performance assessment details
        let publishablePerformanceList = await ratingsPublishableEmpIdQry
        .then(publishablePerformanceIdsList =>{
            return((publishablePerformanceIdsList && publishablePerformanceIdsList.length > 0) ? publishablePerformanceIdsList : []);
        }).catch(performanceListCatchError =>{
            console.log('Error in ratingsPublishableEmpPerformanceDetails() function .catch block',performanceListCatchError);
            throw performanceListCatchError;
        });

        return publishablePerformanceList;
    }catch(performanceIdMainCatchError){
        console.log('Error in ratingsPublishableEmpPerformanceDetails() function main catch block',performanceIdMainCatchError);
        throw performanceIdMainCatchError;
    }
}

// get the average yearly rating based on the employee performance id for the performance year based on the assessment period
async function getEmpPerformanceAvgRating(orgCode,organizationDbConnection,performanceEmpId,monthsYear){
    try {
        // average overall rating query
        let overallRatingQry = organizationDbConnection(ehrTables.performanceGoalAchievement)
        .avg('Overall_Rating as averageYearlyRating');

            // iterate all the performance month and year to form the where condition to get the average rating
            for(let inputMonthYear of monthsYear){
                overallRatingQry = overallRatingQry.orWhere(qb => {
                    qb.where('Performance_Assessment_Month', inputMonthYear.month)
                    qb.where('Performance_Assessment_Year', inputMonthYear.year)
                    qb.where('Employee_Id', performanceEmpId)
                });
            }

            // get the average yearly rating for the employee
            let averageYearlyRating  = await overallRatingQry.then(averageRatingDetail =>{
                if(averageRatingDetail && averageRatingDetail[0].averageYearlyRating){
                    return averageRatingDetail[0].averageYearlyRating.toFixed(1);
                }else{
                    console.log('Empty response is returned while calculating the average rating for the employee(',performanceEmpId,')',averageRatingDetail);
                    throw 'EPM0014';
                }
            }).catch(overallRatingCatchError =>{
                console.log('Error in getEmpPerformanceAvgRating() function .catch block',overallRatingCatchError);
                throw 'EPM0116';
        });

        return averageYearlyRating;
    }catch(fetchAvgRatingMainCatchError){
        console.log('Error in getEmpPerformanceAvgRating() function main catch block',fetchAvgRatingMainCatchError);
        if(fetchAvgRatingMainCatchError === 'EPM0116'){
            throw 'EPM0116';
        }else{
            throw 'EPM0014';
        }
    }
}

// get the performance months and year based on the action
async function getPerformanceMonthYear(orgCode,organizationDbConnection,action='getseparatemonthsyear',args={}){
    let response = [];
    try{
        // get the organization settings details
        let orgDetails = await commonLib.func.getOrgDetails(orgCode,organizationDbConnection,0);

        if(orgDetails && Object.keys(orgDetails).length > 0){
            performanceMonth = orgDetails.Performance_StartMonth;//performance start month

            if(performanceMonth){
                if(action.toLowerCase() === 'getseparatemonthsyear'){
                    // get the performance month and year in this format for the performance year. Example: [{month:4,year:2020},....] based on the assessment period
                    let response = await commonLib.payroll.getMonthYear(organizationDbConnection,performanceMonth,'calculateYear',action,args);
                    return response;
                }else if(action.toLowerCase() === 'getstartendmonthandyear'){
                    // get the performance start month, end month, current performance start year and the current performance end year
                    let response = await commonLib.payroll.getMonthYear(organizationDbConnection,performanceMonth,'calculateYear',action,args);
                    return response;
                }else{
                    console.log('Invalid action is sent in the getPerformanceMonthYear() function.');
                    return response;
                }
            }else{
                console.log('Performance month is empty in the getPerformanceMonthYear() function.');
                return response;
            }
        }else{
            console.log('Empty organization settings details response is returned in the getPerformanceMonthYear() function.');
            return response;
        }
    }catch(mainCatchError){
        console.log('Error in the getPerformanceMonthYear() function main catch block. ',mainCatchError);
        return response;
    }
}

// function to update update goals and achievement details
async function updateGoalsAndAchievement(organizationDbConnection,assessmentId,args,empTimeZone,logInEmpId,action){
    try{
        let updateParams;
        // if action is to update only the reviewer then update it
        if(action==='updateReviewer'){
            updateParams={
                Reviewer_Employee_Id:args.reviewerId,
                Updated_On:empTimeZone,
                Updated_By:logInEmpId
            }
        }
        else if(action==='updateUpdatedOnDetails'){
            updateParams={
                Updated_On:empTimeZone,
                Updated_By:logInEmpId
            };
        }
        else{
            // form params to update
            updateParams={
                Assessment_Status:defaultValues.ratedAssessmentStatus,
                Rating_Publish_Status:defaultValues.goalsAndAchievementGoalUnPublishStatus,
                Overall_Rating:args.overallRating,
                Comments:(args.comments)?args.comments:null,
                Updated_On:empTimeZone,
                Updated_By:logInEmpId
            }
        }
        // update assessment and  publish status
        return(
            organizationDbConnection(ehrTables.performanceGoalAchievement)
            .update(updateParams)
            .where('Performance_Assessment_Id',assessmentId)
            .then(updateStatus => {
                return 'success';
            })
            .catch(catchError => {
                console.log('Error in updateGoalsAndAchievement function .catch block',catchError);
                return '';
            })
        );
    }
    catch(error){
        console.log('Error in updateGoalsAndAchievement function main catch block',error);
        return '';
    }
}

// function to form performance year for presentation
async function formCurrentPerformanceYear(startMonth,startYear,endMonth,endYear)
{
    // require moment package
    const moment = require('moment-timezone');
    try{
        // form performance year[Example: Apr 2020 - Mar 2021] response
        //Example response: Apr 2020
        let currentYearPerformanceStartMonthYear = moment.monthsShort(startMonth - 1)+" "+startYear;
        //Example response: Mar 2021
        let currentYearPerformanceEndMonthYear = moment.monthsShort(endMonth - 1)+" "+endYear;
        //Example response: Apr 2020 - Mar 2021
        let currentPerformanceYear = currentYearPerformanceStartMonthYear+' - '+currentYearPerformanceEndMonthYear;
        return currentPerformanceYear;
    }
    catch(error){
        console.log('Error in formCurrentPerformanceYear function catch block',error);
        return '';
    }
};

/**
 * Function to send goals/ratings published notification to employee(s)
 * @param {Object} organizationDbConnection - Organization database connection object
 * @param {JSON} sendEmailArgs - Input params
 * @returns {String|JSON} - Returns string or JSON when an error occurred in this function and return string when the email is sent.
 */
 async function sendEmailToEmployees(organizationDbConnection,sendEmailArgs){
    try{
        //Require aws package
        const AWS = require("aws-sdk");

        let { orgCode, performanceAssessmentIds, templateName, emailSubject, emailContent }= sendEmailArgs;
        let toEmployeeDetails = await organizationDbConnection
        .pluck('EJ.Emp_Email')
        .from(ehrTables.performanceGoalAchievement+' as PGA')
        .innerJoin(ehrTables.empPersonalInfo + ' as EPI', 'EPI.Employee_Id', 'PGA.Employee_Id')
        .innerJoin(ehrTables.empJob + ' as EJ', 'EPI.Employee_Id', 'EJ.Employee_Id')
        .whereIn('PGA.Performance_Assessment_Id',performanceAssessmentIds)
        .whereNot('EJ.Emp_Email','')
        .whereNotNull('EJ.Emp_Email')
        .then(empDetails => {
            return empDetails;
        });

        // check toEmployeeDetails exists or not
        if (toEmployeeDetails.length > 0) {
            let mailListData;
        
            /** Get organization details for sending mail. Send third param as 1
             * to get the email notification related details. */
            let orgDetails = await commonLib.func.getOrgDetails(orgCode, organizationDbConnection,1);

            let redirectionURL = '';
            //Form redirection URL
            if (process.env.stageName.toLowerCase() === 'local') {
                redirectionURL = 'http://localhost/in'+defaultValues.pmsRedirectionUrl;
            } else {
                redirectionURL = 'https://' + orgCode + '.' + process.env.domainName + process.env.webAddress +defaultValues.pmsRedirectionUrl;
            }

            let inputParams={
                orgDetails:orgDetails,
                emailBody:emailContent,
                emailSubject:emailSubject,
                redirectionURL:redirectionURL,
                emailFrom:process.env.emailFrom,
                emailIdList:toEmployeeDetails
            }
            do
            {
                // function to send the bulk emails
                mailListData=await commonLib.func.sendBulkMail(process.env.sesTemplatesRegion,inputParams,templateName);
                console.log('Response after calling sendBulkMail function',mailListData);
            }
            // iterate the list till all the records are processed
            while(Object.keys(mailListData).length>0)
            {
                //Return response
                return 'success';
            }
        } else {
            throw 'PR0016'; //Throw email id does not exist for the employee(s)
        } 
    }catch(sendEmailCatchError){
        console.log('Error in the sendEmailToEmployees function main catch block.',sendEmailCatchError);
        throw sendEmailCatchError;
    }
}

//Get the employee ids for the given assessment period or for the performance assessment id
async function goalsRatingUnPublishableEmpIds(organizationDbConnection,loginEmpId,isLoginEmployeeAdmin,action,args){
    try {
        let unPublishableEmpIdQry = organizationDbConnection(ehrTables.performanceGoalAchievement)
        .distinct()
        .pluck('Employee_Id')
        .whereIn('Employee_Id', args.employeeId)
        .where('Performance_Assessment_Month', args.month)
        .where('Performance_Assessment_Year', args.year);
        if(action === 'revertgoals'){
            unPublishableEmpIdQry = unPublishableEmpIdQry.where('Goal_Publish_Status', defaultValues.goalsAndAchievementGoalPublishStatus)
                                    .where('Assessment_Status', defaultValues.goalsAndAchievementAssessmentStatus);
        }else if(action === 'revertratings'){
            unPublishableEmpIdQry = unPublishableEmpIdQry.where('Rating_Publish_Status', defaultValues.goalsAndAchievementGoalPublishStatus)
                                    .where('Assessment_Status', defaultValues.ratedAssessmentStatus);
        }
        // if the login employee is not admin
        if(isLoginEmployeeAdmin!=='admin'){
            unPublishableEmpIdQry = unPublishableEmpIdQry.where('Reviewer_Employee_Id', loginEmpId);
        }

        //Get the employee ids
        let unpublishableEmployeeIds = await unPublishableEmpIdQry
        .then(unpublishableList =>{
            return((unpublishableList && unpublishableList.length > 0) ? unpublishableList : []);
        }).catch(catchError =>{
            console.log('Error in goalsRatingUnPublishableEmpIds() function .catch block',catchError);
            throw catchError;
        });

        return unpublishableEmployeeIds;
    }catch(mainCatchError){
        console.log('Error in goalsRatingUnPublishableEmpIds() function main catch block',mainCatchError);
        throw mainCatchError;
    }
}

module.exports.getPmsAlreadyExistEmpIds = getPmsAlreadyExistEmpIds;
module.exports.getRatings=getRatings;
module.exports.getEmployeeIdBasedOnReviewer = getEmployeeIdBasedOnReviewer;
module.exports.goalsPublishableOrDeletePerformanceIds = goalsPublishableOrDeletePerformanceIds;
module.exports.updateGoalsAndAchievement=updateGoalsAndAchievement;
module.exports.getEmpPerformanceAvgRating = getEmpPerformanceAvgRating;
module.exports.getPerformanceMonthYear = getPerformanceMonthYear;
module.exports.ratingsPublishableEmpPerformanceDetails = ratingsPublishableEmpPerformanceDetails;
module.exports.formCurrentPerformanceYear=formCurrentPerformanceYear;
module.exports.sendEmailToEmployees=sendEmailToEmployees;
module.exports.goalsRatingUnPublishableEmpIds=goalsRatingUnPublishableEmpIds;
