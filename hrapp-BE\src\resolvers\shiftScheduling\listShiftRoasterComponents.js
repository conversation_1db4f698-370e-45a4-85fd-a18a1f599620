const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require common constant files
const { formName, roles, formIds } = require('../../../common/appconstants');
// Organization database connection
const knex = require('knex');
// require table names
const { ehrTables } = require('../../../common/tablealias');
// require moment-timezone
const moment = require('moment-timezone');
module.exports = {
    // list the shift roaster employees
    listShiftRoasterEmployees: async (parent, args, context, info) => {
        console.log('Inside listShiftRoasterEmployees() function.');
        const orgDb = knex(context.connection.OrganizationDb);
        const orgCode = context.Org_Code;
        try {
            // variable declaration
            args.isAdmin = 0;
            args.employeeId = context.Employee_Id;
            args.source = 'getCoreDetails';
            // Check shift scheduling form access rights.
            let checkRights = await commonLib.func.checkEmployeeAccessRights(orgDb, context.Employee_Id, formName.shiftScheduling, '', 'UI');
            if (Object.keys(checkRights).length > 0) {
                //Check the employee is admin or not
                if (checkRights.Employee_Role && checkRights.Employee_Role.toLowerCase() === 'admin') {
                    args.isAdmin = 1;
                }
                // Check filter month value is exist or not
                if (args.filterMonth) {
                    // get active/inactive roster employee count for the filter month based on the login employee admin access
                    let employees = await commonLib.func.getAllEmployeeDetails(orgDb, 'getRosterEmployees', context.Org_Code,
                        process.env.domainName, process.env.region, process.env.hrappProfileBucket, args, null);
                    let count = employees.length;
                    if (count > 0) {
                        // get month and date
                        let [month, year] = args.filterMonth.split(',');
                        // form filer start and end date based on the month chosen from the UI
                        let { Salary_Date, Last_SalaryDate } = await commonLib.func.getSalaryDay(context.Org_Code, orgDb, month, year);

                        let employeeDetails = [];
                        // Iterate the employees and for response
                        for (let i = 0; i < count; i++) {
                            // variable declarations
                            let startDate;
                            let endDate;
                            // get date of joning
                            let doj = employees[i].doj;
                            // If the employee not part of current payroll month then we should not list them
                            // check employee DOJ should be lessthan Last_SalaryDate
                            if (new Date(doj) <= new Date(Last_SalaryDate) && new Date(doj) >= new Date(Salary_Date) || new Date(doj) <= new Date(Salary_Date)) {
                                let exitDate = employees[i].resignation_date;
                                // check DOJ is greater than startDate the consider DOJ as start date 
                                if (new Date(doj) > new Date(Salary_Date)) {
                                    startDate = doj;
                                } else {
                                    startDate = Salary_Date;
                                }
                                // check employee's resignation exit date is exist. If yes check exit date with the end date. 
                                // If exit date is less than end date then consider exit date as end date
                                if (exitDate && (new Date(exitDate) < new Date(Last_SalaryDate))) {
                                    endDate = exitDate;
                                } else {
                                    endDate = Last_SalaryDate
                                }
                                // Get difference between 2 dates in days
                                let tmpStartDate = moment(startDate, 'YYYY-MM-DD');
                                let tmpEndDate = moment(endDate, 'YYYY-MM-DD');
                                let dateDiff = tmpEndDate.diff(tmpStartDate, 'days') + 1;
                                // get count of shift scheduled dates
                                let response = await orgDb.count('Shift_Schedule_Id as shiftCount')
                                    .from(ehrTables.shiftEmpMapping)
                                    .where('Employee_Id', employees[i].employee_id)
                                    .where('Shift_Start_Date', '>=', startDate)
                                    .where('Shift_End_Date', '<=', endDate);
                                // append shift_count in the response
                                employees[i]['shift_count'] = response[0] ? response[0].shiftCount + '/' + dateDiff : 0 + '/' + dateDiff; // "5/31"
                                // check photo path exist or not
                                if (employees[i]["photo_path"]) {
                                    let fileName = await commonLib.func.formS3FilePath(employees[i]["photo_path"], orgCode, 'profile', '', process.env.domainName);
                                    employees[i]["photo_path"] = fileName ? await commonLib.func.getFileURL(process.env.region, process.env.hrappProfileBucket, fileName) : null;
                                }
                                else {
                                    employees[i]["photo_path"] = null;
                                }
                                //Push the employee details into an array
                                employeeDetails.push(employees[i]);
                            }
                        }
                        return {
                            success: true,
                            errorCode: null,
                            message: "Shift roaster employees retrieved successfully",
                            employees: employeeDetails[0] ? employeeDetails : null
                        }
                    } else {
                        console.log('No shift roster employees found.');
                        let errResult = commonLib.func.getError('', 'SS0156');
                        return {
                            success: false,
                            errorCode: errResult.code,
                            message: errResult.message
                        }
                    }
                } else {
                    console.log('Filter month is not send as an input.');
                    // if filter month is not selected then return error
                    let errResult = commonLib.func.getError('', 'SS0123');
                    return {
                        success: false,
                        errorCode: errResult.code,
                        message: errResult.message
                    }
                }
            } else {
                let errResult = commonLib.func.getError('', '_DB0100');
                return {
                    success: false,
                    errorCode: errResult.code,
                    message: errResult.message
                }
            }
        }
        catch (err) {
            console.log('Error in listShiftRoasterEmployees function main catch block', err);
            let errResult;
            if (err instanceof Object) { // is to check error object is json or not
                errResult = err;
            } else {
                errResult = commonLib.func.getError(err, 'SS0123');
            }
            throw new ApolloError(errResult.message, errResult.code)
        } finally {
            // destroy database connection
            orgDb ? orgDb.destroy() : null;
        }
    },
    // list the custom groups based on input formName
    listCustomEmployeeGroups: async (parent, args, context, info) => {
        const orgDb = knex(context.connection.OrganizationDb);
        let validationError = {};
        let errResult;
        let preApprovalType;
        try {
            if (!args.formName) {
                validationError['IVE0145'] = commonLib.func.getError('', 'IVE0145').message;
            }

            //check validation error is exist or not
            if (Object.keys(validationError).length === 0) {
                let inputFormId, inputFormName;
                if (args.formName.toLowerCase() === 'shiftscheduling') {
                    inputFormName = formName.shiftScheduling;
                    inputFormId = formIds.shiftScheduling;
                } else if (args.formName.toLowerCase() === 'goalsandachievement') {
                    inputFormName = formName.goalsAndAchievement;
                    inputFormId = formIds.goalsAndAchievement;
                } else if (args.formName.toLowerCase() === 'productivitymonitoring') {
                    inputFormName = formName.productivityMonitoring;
                    inputFormId = formIds.productivityMonitoring;
                }
                else if (args.formName.toLowerCase() === 'geofencing&selfieattendance') {
                    inputFormName = formName.geoFencingSelfieAttendance;
                    inputFormId = formIds.geoFencingSelfieAttendance;
                }
                else if (args.formName.toLowerCase() === 'accreditation') {
                    inputFormName = formName.accreditation;
                    inputFormId = formIds.accreditation;
                }
                else if (args.formName.toLowerCase() === 'holidays') {
                    inputFormName = formName.holidays;
                    inputFormId = formIds.holidays;
                }
                else if (args.formName.toLowerCase() === 'projects') {
                    inputFormName = formName.projects;
                    inputFormId = formIds.projects;
                }
                else if (args.formName.toLowerCase() === 'timesheets') {
                    inputFormName = formName.timesheets;
                    inputFormId = formIds.timesheets;
                }
                else if (args.formName.toLowerCase() === 'ondutysettings') {
                    inputFormName = formName.leaves;
                    inputFormId = formIds.onDutySettings;
                }
                else if (args.formName.toLowerCase() === 'work from home (pre-approval)') {
                    inputFormName = formName.preApprovalSettings;
                    inputFormId = formIds.workFormHomePreApproval;
                    preApprovalType = 'Work From Home';
                }
                else if (args.formName.toLowerCase() === 'work during week off (pre-approval)') {
                    inputFormName = formName.preApprovalSettings;
                    inputFormId = formIds.workDuringWeekOffPreApprovals;
                    preApprovalType = 'Week Off';
                }
                else if (args.formName.toLowerCase() === 'work during holiday (pre-approval)') {
                    inputFormName = formName.preApprovalSettings;
                    inputFormId = formIds.workDuringHolidayPreApprovals;
                    preApprovalType = 'Holiday';
                }
                else if (args.formName.toLowerCase() === 'on duty (pre-approval)') {
                    inputFormName = formName.preApprovalSettings;
                    inputFormId = formIds.onDutyPreApprovals;
                    preApprovalType = 'On Duty';
                }
                else if (args.formName.toLowerCase() === 'comp off') {
                    inputFormName = formName.compOff;
                    inputFormId = formIds.compOff;
                }
                else if (args.formName.toLowerCase() === 'special wages') {
                    inputFormName = formName.specialWages;
                    inputFormId = formIds.specialWages;
                }
                else if(args.formName.toLowerCase()==="over time"){
                    inputFormName = formName.overTime;
                    inputFormId = formIds.overTimeNew;
                }
                else if (args.formName.toLowerCase() === 'lop recovery') {
                    inputFormName = formName.lopRecovery;
                    inputFormId = formIds.lopRecoverySettings;
                }
                else if (args.formName.toLowerCase() === 'job posts') {
                    inputFormName = formName.jobPost;
                    inputFormId = formIds.jobPost;
                }
                if (inputFormName && inputFormId) {
                    // Check form view access rights
                    let checkRights = await commonLib.func.checkEmployeeAccessRights(orgDb, context.Employee_Id, inputFormName, roles.roleView);

                    if (checkRights === true) {
                        let customGroups = await orgDb.select('CEG.Group_Id as Custom_Group_Id', 'CEG.Group_Name as Custom_Group_Name')
                            .from(ehrTables.cusEmpGroup + ' as CEG')
                            .innerJoin(ehrTables.cusEmpGroupForms + ' as CEGF', 'CEG.Group_Id', 'CEGF.Group_Id')
                            .orderBy('CEG.Group_Name', 'asc')
                            .where('CEGF.Form_Id', inputFormId)
                            .where(function () {
                                if (inputFormName === formName.preApprovalSettings && !!args.preApprovalId) {
                                    this.whereNotIn('CEG.Group_Id', function () {
                                        this.select('Custom_Group_Id').from(ehrTables.preApprovalSettings)
                                            .where("Pre_Approval_Type", preApprovalType)
                                            .whereNot("Pre_Approval_Configuration_Id", args.preApprovalId)
                                    })
                                }
                            })
                        return {
                            success: true,
                            errorCode: null,
                            message: "Custom groups retrieved successfully",
                            customGroups: customGroups[0] ? customGroups : null
                        }
                    } else if (checkRights === false) {
                        let errResult = commonLib.func.getError('', '_DB0100');
                        return {
                            success: false,
                            errorCode: errResult.code,
                            message: errResult.message
                        }
                    } else {
                        console.log("Error while checking rights in listCustomEmployeeGroups", checkRights);
                        let errResult = commonLib.func.getError('', 'SS0140');
                        return {
                            success: false,
                            errorCode: errResult.code,
                            message: errResult.message
                        }
                    }
                } else {
                    validationError['IVE0145'] = commonLib.func.getError('', 'IVE0145').message;

                    // throw validation error
                    throw ('IVE0000');
                }
            } else {
                // throw validation error
                throw ('IVE0000');
            }
        }
        catch (err) {
            console.log('Error in listCustomEmployeeGroups function main catch block', err);
            if (err === 'IVE0000') {
                errResult = commonLib.func.getError('', err);
                throw new UserInputError(errResult.message, { validationError: validationError });
            } else {
                errResult = commonLib.func.getError('', 'SS0140');
                throw new ApolloError(errResult.message, errResult.code)
            }
        } finally {
            // destroy database connection
            orgDb ? orgDb.destroy() : null;
        }
    }
}