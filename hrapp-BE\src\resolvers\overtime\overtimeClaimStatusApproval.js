// require apollo server errors
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require common constant files
const { formName,systemLogs,formIds } = require('../../../common/appconstants');
// require validation file
const { validateUpdateOTStatusInputs,getOvertimeClaimStatusApprovalDetails,validLoginEmployeeIsEligibleApprover,getOvertimeSettings,validateEmployeeClaimMonth } = require('./overtimeCommonFunctions');
// Organization database connection
const knex = require('knex');

// resolver definition
const resolvers = {   
    Mutation: {
        //Additional wage claim form - Approved, Returned, Rejected and Paid status update resolver function
        updateOvertimeDetailsStatus: async (parent, args, context, info) => {
            console.log("Inside updateOvertimeDetailsStatus function.");
            //require common functions
            const {getApprover} =require('./overtimeCommonFunctions');
            // require table names
            const { ehrTables } = require('../../../common/tablealias');
            let orgDb = '';
            let validationError = {};
            try{
                // get db connection
                orgDb = knex(context.connection.OrganizationDb);
                let userIp = context.User_Ip;
                let loginEmpId = context.Employee_Id;

                // Get the additional wage claim form access for the login employee
                let checkRights = await commonLib.func.checkEmployeeAccessRights(orgDb, loginEmpId, formName.overtime, '', 'UI');

                // check checkRights is not empty json or not
                if (Object.keys(checkRights).length <= 0) {
                    throw ('_DB0108');
                }else{
                    //if login employee is having view and update access for the additional wage claim form
                    if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1 && checkRights.Role_Update === 1) {
                        if(args.newApprovalStatus && args.newApprovalStatus.toLowerCase() !== 'completed'){
                            validationError = validateUpdateOTStatusInputs(args);

                            if(Object.keys(validationError).length === 0){
                                let {newApprovalStatus,overtimeClaimIds,oneApprovalRecordComment,filterMonth,filterYear} = args;
                                let validOldApprovalStatus = '';

                                let isLoggedInEmpAdmin = (checkRights.Employee_Role.toLowerCase() === 'admin') ? 1 : 0;
                                // Send employeeId ,dbConnection and another flag as 1(This flag is to get the time zone date with time)
                                let employeeTimeZoneCurrentDateTime = await commonLib.func.getEmployeeTimeZone(loginEmpId, orgDb, 1);

                                /** Form the possible old approval status based on the new approval status */
                                if(newApprovalStatus === 'Approved'){
                                    validOldApprovalStatus = ['Applied'];
                                }else if(newApprovalStatus === 'Completed'){
                                    validOldApprovalStatus = '';
                                }else if(newApprovalStatus === 'Paid'){
                                    validOldApprovalStatus = ['Completed'];
                                }else if(newApprovalStatus === 'Returned' || newApprovalStatus === 'Rejected'){
                                    validOldApprovalStatus = ['Approved','Applied'];
                                }

                                if(validOldApprovalStatus){
                                    let approvalRecordDetails = await getOvertimeClaimStatusApprovalDetails(orgDb,validOldApprovalStatus,overtimeClaimIds);
                                    
                                    if(approvalRecordDetails.length <= 0){
                                        console.log('Overtime claim records are already updated.');
                                        throw('OT0012');
                                    }else{
                                        // get the overtime settings
                                        let overtimeSettings = await getOvertimeSettings(orgDb);

                                        if(Object.keys(overtimeSettings).length > 0){
                                            let isCompOffOrShiftAllowanceEnabled = 0;

                                            /** If the compensatory off or shift allowance is applicable for overtime */
                                            if(overtimeSettings.Comp_Off_Applicable_For_Overtime || overtimeSettings.Shift_Allowance_Applicable_For_Overtime){
                                                isCompOffOrShiftAllowanceEnabled = 1;
                                            }

                                            let otStatusApprovalSuccessCount=0, overtimeClaimId = 0;
                                            let allcommentGenerationParams = [];
                                            let statusUpdatedUIds = [];
                                            let commentGenerationParams = {};
                                            let updateOvertimeStatusErrorCode = '';
                                            let employeeClaimedMonthStartDate = filterYear+'-'+((filterMonth < 10) ? ('0'+filterMonth) : filterMonth)+'-01';//2021-04-01

                                            for (let otDetail of approvalRecordDetails) {  
                                                updateOvertimeStatusErrorCode = '';
                                                overtimeClaimId = otDetail.overtimeClaimId;                                       
                                                commentGenerationParams = {};

                                                let validateLoginEmployeeIsApproverArgs = {
                                                    isLoggedInEmpAdmin:isLoggedInEmpAdmin,
                                                    oldApprovalStatus: otDetail.oldApprovalStatus,
                                                    newApprovalStatus: newApprovalStatus,
                                                    lastApprovedBy: otDetail.lastApprovedBy,
                                                    otEmployeeId: otDetail.otEmployeeId,
                                                    loginEmpId: loginEmpId
                                                };
                                        
                                                let updateOvertimeStatusRecord = await validLoginEmployeeIsEligibleApprover(validateLoginEmployeeIsApproverArgs,orgDb);
                                                
                                                if(parseInt(updateOvertimeStatusRecord) === 1){
                                                    let overtimeClaimMonthPayslipExist = 0;

                                                    //If the overtime claim - compensatory and shift allowance is enabled and if the new approval status is approved
                                                    if(isCompOffOrShiftAllowanceEnabled && newApprovalStatus === 'Approved') {
                                                        overtimeClaimMonthPayslipExist = await validateEmployeeClaimMonth(orgDb,otDetail.otEmployeeId,employeeClaimedMonthStartDate);
                                                    }
                                                    
                                                    /** If the new approval status is 'Approved' and if th compensatory off or shift allowance claim 
                                                     * is enabled in the the overtime settings and if the payslip is generated for the employee for 
                                                     * the additional wage claim processing month.*/
                                                    if(overtimeClaimMonthPayslipExist === 1){
                                                        console.log('Payslip is generated for the claim month. overtime claim id,',overtimeClaimId);
                                                        updateOvertimeStatusErrorCode = 'OT0137';
                                                    }else{
                                                        let statusApprovalDetails = {
                                                            Approval_Status: newApprovalStatus,
                                                            Approved_On: employeeTimeZoneCurrentDateTime,
                                                            Approved_By: loginEmpId
                                                        };

                                                        /** While updating the status to 'approved' get the second level approver id and update it in the table. */
                                                        if(newApprovalStatus === 'Approved'){
                                                            // get second level manager based on the first level approver id
                                                            let secondLevellMangerId = await getApprover(newApprovalStatus,loginEmpId,orgDb);
                                                            if(secondLevellMangerId){
                                                                statusApprovalDetails.Second_Level_Approver = secondLevellMangerId;
                                                            }else{
                                                                /** If the second level approver not exist and then login employee id can be the second level approver */
                                                                statusApprovalDetails.Second_Level_Approver = loginEmpId;
                                                            }
                                                        }
                                                    
                                                        /** Update the OT record status */
                                                        let updateOvertimeStatusResponse = await orgDb(ehrTables.overtime)
                                                        .update(statusApprovalDetails)
                                                        .where('Overtime_Claim_Id', overtimeClaimId)
                                                        .whereIn('Approval_Status', validOldApprovalStatus)
                                                        .where('Lock_Flag', 0)
                                                        .then((otRecStatusUpdateRes)=>{
                                                            /* If overtime status is updated **/
                                                            if(otRecStatusUpdateRes){
                                                                otStatusApprovalSuccessCount += 1;
                                                                statusUpdatedUIds.push(overtimeClaimId);//push overtime claim unique ids
                                                                if(oneApprovalRecordComment){
                                                                    // form params to insert record in comment generation table
                                                                    commentGenerationParams = {
                                                                        Form_Id: formIds.overtime,
                                                                        Emp_Comment: (oneApprovalRecordComment) ? oneApprovalRecordComment : null,
                                                                        Approval_Status: newApprovalStatus,
                                                                        Parent_Id: overtimeClaimId,
                                                                        Employee_Id: loginEmpId,
                                                                        Added_On: employeeTimeZoneCurrentDateTime
                                                                    }

                                                                    allcommentGenerationParams.push(commentGenerationParams);
                                                                }
                                                            }else{
                                                                console.log('Overtime claim status is not updated for the overtime claim id(',overtimeClaimId,') and the update response is,',otRecStatusUpdateRes);
                                                                updateOvertimeStatusErrorCode = 'OT0012';
                                                            }
                                                            return otRecStatusUpdateRes;
                                                        }).catch((otRecStatusUpdateErr) => {
                                                            console.log('Error while updating the records in updateOvertimeDetailsStatus() function',otRecStatusUpdateErr);
                                                            updateOvertimeStatusErrorCode = 'OT0005';
                                                            return 0;
                                                        });
                                                    }
                                                }else{
                                                    console.log('Empty response from the validLoginEmployeeIsEligibleApprover() function, ',updateOvertimeStatusRecord,'.Login employee id is not eligible approver to complete the overtime claim record id, ',overtimeClaimId);
                                                    updateOvertimeStatusErrorCode = '_DB0108';
                                                }
                                            }

                                            let approvalRecordCount = approvalRecordDetails.length;
                                            /** If any one or all the overtime record status are updated */
                                            if( otStatusApprovalSuccessCount > 0){
                                                /** Need to use insertmultiple for comment generation in future the records */
                                                if (allcommentGenerationParams.length > 0) {
                                                    for( commentInsertionRec of allcommentGenerationParams){
                                                        // Insert record in comment generation table
                                                        await commonLib.func.insertCommentsAndStatus(commentInsertionRec, orgDb);
                                                    }
                                                }                                

                                                // form inputs to update system log. Ex: Update 'formName' status - '[overtimeclaimids]' 
                                                let systemLogParams = {
                                                    action: systemLogs.roleUpdate,
                                                    userIp: userIp,
                                                    employeeId: loginEmpId,
                                                    formName: formName.overtime,
                                                    trackingColumn: 'status',
                                                    organizationDbConnection: orgDb,
                                                    uniqueId: statusUpdatedUIds
                                                };

                                                // call function createSystemLogActivities() to update system log activities
                                                await commonLib.func.createSystemLogActivities(systemLogParams);
                                                
                                                /** If all the overtime record status are updated */
                                                if( otStatusApprovalSuccessCount === approvalRecordCount){
                                                    // destroy database connection
                                                    orgDb ? orgDb.destroy() : null;
                                                    // Email notification will be updated in the future
                                                    return { errorCode: '', message: 'Overtime status updated successfully'}; // return response to UI
                                                }else{
                                                    /** If the overtime records status is partially updated, consider this as a success and send an email notification in future.
                                                     * For now, throw this error code if record updated partially
                                                    */
                                                    throw ('OT0007'); 
                                                }
                                            }else{
                                                /** If only one record is given for approval and status is not updated. */
                                                if(approvalRecordCount === 1){
                                                    /** If one overtime record is send for approval and the status is not updated. */
                                                    throw ((updateOvertimeStatusErrorCode) ? updateOvertimeStatusErrorCode : 'OT0006');
                                                }else{
                                                    throw 'OT0006';// throw no record is updated and do not send an email notification
                                                }
                                            }
                                        }else{
                                            console.log('Empty overtime settings. Response from the overtimeSettings() function,',overtimeSettings);
                                            throw('OT0120');
                                        }
                                    }
                                }else{
                                    console.log('Empty old approval status. New approval status is,',newApprovalStatus);
                                    throw('OT0136');//throw empty old approval sstatus
                                }
                            }else{
                                throw ('IVE0000'); // throw validation error
                            }
                        }else{
                            console.log('Invalid new approval status. So overtime claim status cannot be updated.',args.newApprovalStatus);
                            throw ('IVE0106'); // throw invalid new approval status or new approval status should be in completed.
                        }
                    }else{
                        throw('_DB0108'); // throw employee is not having status update access rights
                    }
                }
            }catch(updateOvertimeDetailsStatusMainCatchError){
                console.log('Error while in updateOvertimeDetailsStatus() function main catch block.',updateOvertimeDetailsStatusMainCatchError);                
                // destroy database connection
                orgDb ? orgDb.destroy() : null;
                // check input validation error or not
                if (updateOvertimeDetailsStatusMainCatchError === 'IVE0000') {
                    errResult = commonLib.func.getError('', updateOvertimeDetailsStatusMainCatchError);
                    // return response
                    throw new UserInputError(errResult.message, { validationError: validationError });
                } else {
                    // get error and return it to UI
                    let errResult = commonLib.func.getError(updateOvertimeDetailsStatusMainCatchError, 'OT0112');
                    throw new ApolloError(errResult.message, errResult.code);
                }
        
            }
        },
        //Additional wage claim form - Completed status update resolver function
        updateOvertimeClaimCompletedStatus: async (parent, args, context, info) => {
            console.log('Inside updateOvertimeClaimCompletedStatus function.');
            const {updateOvertimeOtherClaims} = require('./updateOvertimeOtherClaims');

            // get db connection
            let orgDb = '';
            let validationError = {};
            try{
                orgDb = knex(context.connection.OrganizationDb);
                // Check form view access rights
                let loginEmpId = context.Employee_Id;
                // Get the additional wage claim form access for the login employee
                let checkRights = await commonLib.func.checkEmployeeAccessRights(orgDb, loginEmpId, formName.overtime, '', 'UI');
                // check checkRights is not empty json or not
                if (Object.keys(checkRights).length <= 0) {
                    throw ('_DB0108');
                }else{
                    //if login employee is having view and update access for the additional wage claim form
                    if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1 && checkRights.Role_Update === 1) {
                        let newApprovalStatus = 'Completed';
                        args.newApprovalStatus = newApprovalStatus;
                        validationError = validateUpdateOTStatusInputs(args);

                        if(Object.keys(validationError).length === 0){
                            // get the overtime settings
                            let overtimeSettings = await getOvertimeSettings(orgDb);

                            if(Object.keys(overtimeSettings).length > 0){
                                let isCompOffOrShiftAllowanceEnabled = 0;
                                let isLoggedInEmpAdmin = (checkRights.Employee_Role.toLowerCase() === 'admin') ? 1 : 0;

                                // Send employeeId ,dbConnection and another flag as 1(This flag is to get the time zone date with time)
                                let employeeTimeZoneCurrentDateTime = await commonLib.func.getEmployeeTimeZone(loginEmpId, orgDb, 1);
                                
                                /** If the compensatory off or shift allowance is applicable for overtime */
                                if(overtimeSettings.Comp_Off_Applicable_For_Overtime || overtimeSettings.Shift_Allowance_Applicable_For_Overtime){
                                    isCompOffOrShiftAllowanceEnabled = 1;
                                }

                                let validOldApprovalStatus = ['Approved'];
                                let approvalRecordDetails = await getOvertimeClaimStatusApprovalDetails(orgDb,validOldApprovalStatus,args.overtimeClaimIds);

                                if(approvalRecordDetails.length <= 0){
                                    console.log('Overtime claim records are already updated.');
                                    throw('OT0012');
                                }else{
                                    let updatedSucessCount = 0;
                                    let invalidStatusUpdateErrorCode = '';
                                    let invalidResponseResult = [];
                                    let filterMonth = args.filterMonth;
                                    let filterYear = args.filterYear;
                                    let employeeClaimedMonthStartDate = filterYear+'-'+((filterMonth < 10) ? ('0'+filterMonth) : filterMonth)+'-01';//2021-04-01

                                    for (let otDetail of approvalRecordDetails) {
                                        let statusUpdateOtRecord = {
                                            otDetail: otDetail,
                                            loginEmpTimeZoneCurrentDateTime:employeeTimeZoneCurrentDateTime,
                                            newApprovalStatus: newApprovalStatus,
                                            isLoggedInEmpAdmin: isLoggedInEmpAdmin,
                                            overtimeSettings: overtimeSettings,
                                            isCompOffOrShiftAllowanceEnabled: isCompOffOrShiftAllowanceEnabled,
                                            oneApprovalRecordComment: args.oneApprovalRecordComment ? args.oneApprovalRecordComment : '',
                                            validOldApprovalStatus:validOldApprovalStatus,
                                            employeeClaimedMonthStartDate: employeeClaimedMonthStartDate
                                        }
                                        
                                        let updateAdditionalWageClaimResponse = await updateOvertimeOtherClaims ('', statusUpdateOtRecord, context, '');//call sub lambda
                                        // If the overtime claim status and the respective other claim details are updated
                                        if(!updateAdditionalWageClaimResponse.errorCode && updateAdditionalWageClaimResponse.additionalWageClaimUpdated){
                                            updatedSucessCount += 1;    
                                        }else{
                                            invalidResponseResult.push(updateAdditionalWageClaimResponse);
                                            invalidStatusUpdateErrorCode = updateAdditionalWageClaimResponse.errorCode; 
                                        }
                                    }

                                    console.log('invalidResponseResult',invalidResponseResult);//will be removed in the future
                                    let approvalRecordsCount = approvalRecordDetails.length;
                                    //If all the records status are updated
                                    if(updatedSucessCount === approvalRecordsCount){
                                        // destroy database connection
                                        orgDb ? orgDb.destroy() : null;
                                        return {errorCode: '', message: 'Additional wage claim status updated successfully.'};
                                    }else{ 
                                        // If more than one record exist
                                        if(approvalRecordsCount > 1){
                                            if(updatedSucessCount > 0){
                                                //If more than one record status is updated, throw records updated partially
                                                throw('OT0134');
                                            }else{
                                                //If no records status are updated, throw no records are updated
                                                throw('OT0138');
                                            }
                                        }else{
                                            //If error code exist, throw the reason for not updating the status. Otherwise return record is not updated
                                            throw ((invalidStatusUpdateErrorCode) ? invalidStatusUpdateErrorCode: 'OT0139');
                                        }
                                    }
                                }
                            }else{
                                console.log('Empty overtime settings. Response from the overtimeSettings() function,',overtimeSettings);
                                throw('OT0120');
                            }
                        }else{
                            throw ('IVE0000'); // throw validation error
                        }
                    }else{
                        throw('_DB0108'); // throw employee is not having status update access rights
                    }
                }
            }catch(updateOvertimeCompletedStatusMainCatchError){
                console.log('Error in updateOvertimeClaimCompletedStatus() function main catch block.',updateOvertimeCompletedStatusMainCatchError);
                // destroy database connection
                orgDb ? orgDb.destroy() : null;
                // check input validation error or not
                if (updateOvertimeCompletedStatusMainCatchError === 'IVE0000') {
                    console.log('Validation error in the updateOvertimeClaimCompletedStatus() function.',validationError);
                    errResult = commonLib.func.getError('', updateOvertimeCompletedStatusMainCatchError);
                    // return response
                    throw new UserInputError(errResult.message, { validationError: validationError });
                } else {
                    // get error and return it to UI
                    let errResult = commonLib.func.getError(updateOvertimeCompletedStatusMainCatchError, 'OT0133');
                    throw new ApolloError(errResult.message, errResult.code);
                }
            }
        }
    }
}

exports.resolvers = resolvers;