//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
let moment = require('moment');
//Require table alias
const { ehrTables } = require('../../../../common/tablealias');
const { formName,systemLogs } = require('../../../../common/appconstants');

//Function to update attendance configuration
module.exports.updateAttendanceConfiguration = async (parent, args, context, info) => {
    console.log('Inside updateAttendanceConfiguration function');
    let organizationDbConnection;
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.attendanceConfiguration, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
            let updateDetails = {
                Attendance_Regularization_Cut_Off_Days_For_Employee: args.employeeRegularizationCutOffDays,
                Attendance_Regularization_Request_Limit_For_Employee: args.employeeRegularizationRequestLimit,
                Attendance_Approval_Cut_Off_Days_For_Manager: args.attendanceApprovalCutOffDaysForManager,
                Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                Updated_By: loginEmployeeId
            };
           
            return (
            organizationDbConnection(ehrTables.attendanceGeneralConfiguration)
            .update(updateDetails)
            .then(async(result)=>{
                if(result){
                    //Log message: Update Attendance Configuration  - 47
                    let systemLogParam = {
                        action: systemLogs.roleUpdate,
                        userIp: context.User_Ip,
                        employeeId: loginEmployeeId,
                        formName: formName.attendanceConfiguration,
                        message: 'Update '+formName.attendanceConfiguration,
                        organizationDbConnection: organizationDbConnection,
                    };
                    //Call the function to add the system log for the work schedule update action
                    await commonLib.func.createSystemLogActivities(systemLogParam);
                                                
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return {errorCode: '',message:'Attendance configuration updated successfully.'};
                }else{
                    console.log('Attendance configuration details are not updated.',result);
                    throw '_EC0011';
                }
            })
            .catch((catchError) => {
                console.log('Error in updateAttendanceConfiguration .catch() block', catchError);
                let errResult = commonLib.func.getError(catchError, 'SAC0102');
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                //Return error response
                throw new ApolloError(errResult.message, errResult.code);
            })
            )
        } else {
            throw '_DB0102';
        }
    } catch (mainCatchError) {
        console.log('Error in updateAttendanceConfiguration function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(mainCatchError, 'SAC0002');
        // return response
        throw new ApolloError(errResult.message, errResult.code);
    }
}
