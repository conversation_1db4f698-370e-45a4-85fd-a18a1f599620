// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require common constant files
const constants = require('../../../common/appconstants');
let moment = require('moment-timezone');
// resolver definition
const resolvers = {
    Query: {
        // function to get leaves,short timeoff and compensatory off details
        listLeavesAndShortTimeOff: async (parent, args, context, info) => {
            // variable declaration
            let organizationDb='';
            let getLeaves,getShortTimeOff,getCompOff=[];
            let errResult={};
            try{   

                console.log('Inside listLeavesAndShortTimeOff function');
                // make database connection
                organizationDb = knex(context.connection.OrganizationDb);
                let employeeId=context.Employee_Id;

                let orgCode=context.Org_Code;

                let payslipMonth=1;
        
                let timezone = await commonLib.func.getEmployeeTimeZone(employeeId, organizationDb, 0);
        
                let todayDate = moment().tz(timezone).format('YYYY-MM-DD');
                
                let currentYear= moment(todayDate).year();
        
                let salaryDate=await commonLib.func.getSalaryDay(orgCode,organizationDb,payslipMonth,currentYear);
                
                // get leave closure start date
                
                let leaveClosureStartDate=salaryDate.Salary_Date;

                // check whether value exist or not
                if(leaveClosureStartDate)
                {
                    // function to get leave details for the employee
                    getLeaves = await commonLib.employees.listLeavesInDashboard(leaveClosureStartDate, employeeId, organizationDb,constants.formIds.leaves,'leaves');

                    // function to get short timeoff details for the employee
                    getShortTimeOff = await commonLib.employees.listLeavesInDashboard(leaveClosureStartDate, employeeId, organizationDb,constants.formIds.shortTimeOff,'shortTimeOff');

                    // function to get compensatory off details for the employee
                    getCompOff = await commonLib.employees.listLeavesInDashboard(leaveClosureStartDate, employeeId, organizationDb,constants.formIds.compensatoryOff,'compOff');

                    // destroy database connection
                    organizationDb ? organizationDb.destroy() : null;
                    return { errorCode: '', message: 'Leaves details retrieved successfully',leaveDetails : (getLeaves.length>0)?JSON.stringify(getLeaves):'' , shortTimeOffDetails: (getShortTimeOff.length>0)?JSON.stringify(getShortTimeOff):'' , compensatoryOffDetails:(getCompOff.length>0)?JSON.stringify(getCompOff):''};
                }
                // leave closure date is not configured
                else{
                    return { errorCode: '', message: 'Leave closure dates not updated',leaveDetails : '' , shortTimeOffDetails: '' , compensatoryOffDetails:''};
                }
            }
            catch(mainCatchError)
            {
                console.log("Error in listLeavesAndShortTimeOff function catch block",mainCatchError);
                // destroy database connection
                organizationDb ? organizationDb.destroy() : null;
                // get the code and message from common function based on returned error code
                errResult = commonLib.func.getError(mainCatchError, 'DB0008');
                // return response
                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, leaveDetails : '' , shortTimeOffDetails: '', compensatoryOffDetails:''}));                
            }
        },
        listHolidaysInDashboard: async (parent, args, context, info) => {
            // variable declaration
            let organizationDb='';
            try{   
                console.log('Inside listHolidaysInDashboard function');
                // make database connection
                organizationDb = knex(context.connection.OrganizationDb);
                let employeeId=context.Employee_Id;

                // formation of start and end date based on current year
                let currentYear=new Date().getFullYear();
                let startDate= currentYear+ '-'+ constants.defaultValues.yearStartDate;
                let endDate = (currentYear + 1)+'-'+ constants.defaultValues.yearEndDate;

                // function to get holidays details based on start and end date
                let getHolidays = await commonLib.organization.orgHolidayDates(employeeId,startDate,endDate,organizationDb,'No',1);
                // destroy database connection
                organizationDb ? organizationDb.destroy() : null;
                return { errorCode: '', message: 'Holidays retrieved successfully.',holidayDetails :(getHolidays.length>0)? JSON.stringify(getHolidays) :'' }
            }
            catch(mainCatchError)
            {
                console.log("Error in listHolidaysInDashboard function catch block",mainCatchError);
                // destroy database connection
                organizationDb ? organizationDb.destroy() : null;
                // get the code and message from common function based on returned error code
                let errResult = commonLib.func.getError(mainCatchError, 'DB0013');
                // return response
                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, holidayDetails : ''}));                                
            }
        }
    }
};

exports.resolvers = resolvers;
