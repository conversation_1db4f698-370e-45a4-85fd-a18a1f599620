// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require apollo server errors
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require common constant files
const { formName, systemLogs } = require('../../../common/appconstants');
// Organization database connection
const knex = require('knex');
// require table names
const { ehrTables } = require('../../../common/tablealias');
// require validation file
const { validateInputs, getApprover, checkLeaveAndCompOff,getOvertimeSettings,validateEmployeeClaimMonth } = require('./overtimeCommonFunctions');
// require overtime calculation file
const { overtimeWageCalculation } = require('./overtimeWageCalculation');
// require moment timezone
let moment = require('moment-timezone');
// resolver function to add the overtime records for the employees
module.exports.addOvertimeDetails = async (parent, args, context, info) => {
    console.log('Inside addOvertimeDetails() function');
    // get db connection
    const orgDb = knex(context.connection.OrganizationDb);
    let validationError = {};
    try {
        // Check form view access rights
        let loginEmpId = context.Employee_Id;
        // Check form add access rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(orgDb, loginEmpId, formName.overtime, '', 'UI');
        // check checkRights is not empty json or not
        if (Object.keys(checkRights).length === 0) {
            // throw access denied error
            throw ('_DB0101');
        } else {
            // check logged-in employee is employee admin or not
            let checkEmpAdminRights = await commonLib.func.checkEmployeeAccessRights(orgDb, loginEmpId, formName.employeeAdmin, '', 'UI');
            // we can allow the user to add the records if they are admin/superadmin/employee admin/payroll admin
            if (checkRights.Role_Add || (Object.keys(checkEmpAdminRights).length > 0 && checkEmpAdminRights.Role_Update === 1) || (checkRights.Employee_Role.toLowerCase() === 'admin')) {
                // validate only the inputs - employeeId, otStartTime, otEndTime,reason in the validateInputs function
                // validate shiftAllowanceAppliedWorkScheduleId in the overtimeWageCalculation function
                validationError = validateInputs(args);
                // if filter month not exists
                if(!args.filterMonth){
                    validationError['IVE0144'] = commonLib.func.getError('', 'IVE0144').message;
                }

                // if filter year not exists
                if(!args.filterYear){
                    validationError['IVE0143'] = commonLib.func.getError('', 'IVE0143').message;
                }
                // check validation error is exist or not
                if (Object.keys(validationError).length === 0){
                    // get input values
                    let { employeeId, otStartTime, otEndTime, status, shiftAllowanceAppliedWorkScheduleId } = args;
                    // get workSchedule details
                    let currentWorkScheduleDetails = await commonLib.employees.getCurrentWorkScheduleDetails(employeeId, otStartTime, orgDb);
                    let { regularFrom, regularTo, considerationFrom, considerationTo } = currentWorkScheduleDetails;
                    // Check regularFrom, regularTo, considerationFrom, considerationTo is not empty
                    if (Object.keys(currentWorkScheduleDetails).length > 0 && regularFrom && regularTo && considerationFrom && considerationTo){
                        let overtimeSettings = await getOvertimeSettings(orgDb);

                        if(Object.keys(overtimeSettings).length > 0){
                            let overtimeClaimMonthPayslipExist = 0;

                            /** If the compensatory off or shift allowance is applicable for overtime */
                            if(overtimeSettings.Comp_Off_Applicable_For_Overtime || overtimeSettings.Shift_Allowance_Applicable_For_Overtime){
                                let { filterMonth,filterYear } = args;
                                let employeeClaimMonthStartDate = filterYear+'-'+((filterMonth < 10) ? ('0'+filterMonth) : filterMonth)+'-01';//2021-04-01
                                overtimeClaimMonthPayslipExist = await validateEmployeeClaimMonth(orgDb,employeeId,employeeClaimMonthStartDate);
                            }

                            /** If compensatory off or shift allowance claim is enabled in the the overtime settings and 
                             * if the payslip is generated for the employee for the additional wage claim processing month.*/
                            if(overtimeClaimMonthPayslipExist === 1){
                                console.log('Payslip is generated for the claim month.');
                                throw('OT0137');
                            }else{
                                // Form considerationFrom
                                considerationFrom = considerationFrom.toISOString().replace(/T/, ' ').replace(/\..+/, '');// "2020-09-09 23:50:00"
                                regularFrom = regularFrom.toISOString().replace(/T/, ' ').replace(/\..+/, '');// "2020-09-09 23:50:00"
                                // convert date time to date format.
                                let regularFromDate = moment(regularFrom).format("YYYY-MM-DD");
                                /** The overtime claim start date may fall on next day in case of overlapping shift. We need to check leave and compoff exist 
                                 * for the employee for the shift regular start date. */
                                let { totalLeaveHours, totalCompOffDuration } = await checkLeaveAndCompOff(employeeId, regularFromDate, orgDb);
                                // Check totalLeaveHours is greater than or equal to 9 hours (4.5 -> Half day, 9 -> Full day)
                                // Check totalCompOffDuration is greater than or equal to 1 (0.5 -> Half day, 1 -> Full day)
                                // If leave or compOff applied for half day then we can allow them to apply Overtime record
                                // Scenarios:
                                // Leave half day - CompOff half day - Not allow
                                // Leave None     - CompOff None     - Allow
                                // Leave full day - CompOff half day - Not allow
                                // Leave full day - CompOff full day - Not allow (Scenario will not come )
                                // Leave None     - CompOff half day - Allow
                                // Leave half day - CompOff full day - Not allow
                                if ((parseFloat(totalLeaveHours) < 4.5 && parseFloat(totalCompOffDuration) < 0.5) || 
                                (parseFloat(totalLeaveHours) <= 4.5 && parseFloat(totalCompOffDuration) < 0.5) || 
                                (parseFloat(totalLeaveHours) < 4.5 && parseFloat(totalCompOffDuration) <= 0.5)){
                                    /** The overtime claim start date may fall on next day in case of overlapping shift. So get attendance count by sending the 
                                     * regular from date and current work schedule details. The attendance count will be fetched based on the consideration start and end time. */
                                    let getAttendanceDetails = await commonLib.employees.getAttendance(regularFromDate, employeeId, orgDb, 'get-count', '',currentWorkScheduleDetails);
                                    
                                    if(!getAttendanceDetails.errorCode){
                                    let isAttendanceExist = getAttendanceDetails.attendanceDetails;
                                    // check isAttendanceExist is 0 or not. If attendance exist then allow them to apply overtime records
                                    if (isAttendanceExist){
                                    // Call function getEmployeeTimeZone() to get current date and time based on employee time zone 
                                    // Send employeeId ,dbConnection and another flag as 1(This flag is to get the time zone date with time)
                                    let empTimeZone = await commonLib.func.getEmployeeTimeZone(loginEmpId, orgDb, 1);
                                    args.source = "Back-End";
                                    args.action = "Add";
                                    args.inputWorkScheduleDetails = currentWorkScheduleDetails;
                                    // Call overtimeWageCalculation to calculate the overtime wages
                                    let response = await overtimeWageCalculation(parent, args,context,info);
                                    let totalHours = response.totalHours;
                                    let overtimeWages = response.overtimeWages;
                                    let managerId = 0;
                                    let inputData = {};
                                    // check status based on that form input data
                                    if(status.toLowerCase() === 'applied'){
                                        // get approver manager based on the status from UI
                                        managerId = await getApprover(status, employeeId, orgDb);
                                        inputData = {
                                            Employee_Id: employeeId,
                                            Start_Date_Time: otStartTime,
                                            End_Date_Time: otEndTime,
                                            Total_Hours: totalHours,
                                            Overtime_Wage: overtimeWages,
                                            Approval_Status: status,
                                            First_Level_Approver: managerId ? managerId : loginEmpId,
                                            Reason: args.reason ? args.reason : null,
                                            Shift_Allowance_Applied_Work_Schedule_Id: shiftAllowanceAppliedWorkScheduleId,
                                            Added_On: empTimeZone,
                                            Added_By: loginEmpId
                                        };
                                    }else{
                                        // get approver manager based on the status from UI
                                        managerId = await getApprover(status, loginEmpId, orgDb);
                                        inputData = {
                                            Employee_Id: employeeId,
                                            Start_Date_Time: otStartTime,
                                            End_Date_Time: otEndTime,
                                            Total_Hours: totalHours,
                                            Overtime_Wage: overtimeWages,
                                            Approval_Status: status,
                                            First_Level_Approver: loginEmpId,
                                            Second_Level_Approver: managerId ? managerId : loginEmpId,
                                            Reason: args.reason ? args.reason : null,
                                            Shift_Allowance_Applied_Work_Schedule_Id: shiftAllowanceAppliedWorkScheduleId,
                                            Approved_By: loginEmpId,
                                            Approved_On: empTimeZone, 
                                            Added_On: empTimeZone,
                                            Added_By: loginEmpId
                                        };
                                    }
                                    
                                    // Insert the record in overtime_claim table
                                    let insertedId = await orgDb(ehrTables.overtime)
                                                    .insert(inputData)
                                                    .then(insertRecord => {
                                                        // return response
                                                        return insertRecord[0];
                                                    })
                                                    .catch(insertError =>{
                                                        console.log('Error while inserting the overtime record in addOvertimeDetails() function .catch block.',insertError);
                                                        errResult = commonLib.func.getError('', 'OT0003');
                                                        throw (errResult);
                                                    });
                                        // form inputs to update system log activities commonly
                                        let systemLogParams = {
                                            action: systemLogs.roleAdd,
                                            userIp: context.User_Ip,
                                            employeeId: loginEmpId,
                                            formName: formName.overtime,
                                            trackingColumn: '',
                                            organizationDbConnection: orgDb,
                                            uniqueId: insertedId
                                        };
                                        // call function createSystemLogActivities() to update system log activities
                                        await commonLib.func.createSystemLogActivities(systemLogParams);
                                        // destroy database connection
                                        orgDb ? orgDb.destroy() : null;
                                        // return response back to function
                                        return { errorCode: '', message: 'Overtime record added successfully'};
                                    }else{
                                        // Cannot apply overtime because attendance exist for this date
                                        console.log('Attendance does not exist for this date.');
                                        throw ('OT0117');// throw error 
                                    }
                                    }else{
                                        // Cannot apply overtime as error occured while validating the attendance
                                        console.log('Error response from the getAttendance() function.',getAttendanceDetails);
                                        throw (getAttendanceDetails.errorCode);// throw error
                                    }
                                }else{
                                    // Cannot apply overtime because either leave or comp off already exist for this date
                                    console.log('Leave or compensentory off already exist for this date.');
                                    // throw error 
                                    throw ('OT0118');
                                }
                            }
                        }else{
                            console.log('Empty overtime settings. Response from the overtimeSettings() function,',overtimeSettings);
                            throw('OT0120');
                        }
                    }else{
                        // throw error if shift is not scheduled
                        throw ('OT0107');
                    }
                }else{
                    // throw validation error
                    throw ('IVE0000');
                }
            }else{
                // throw access denied error
                throw ('_DB0101');
            }
        }
    } catch (addOvertimeDetailsMainCatchError){
        console.log('Error in addOvertimeDetails() function main catch block.',addOvertimeDetailsMainCatchError);
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
        // check input validation error or not
        if (addOvertimeDetailsMainCatchError === 'IVE0000') {
            errResult = commonLib.func.getError('', addOvertimeDetailsMainCatchError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        }else{
            // get error and return it to UI
            let errResult = commonLib.func.getError(addOvertimeDetailsMainCatchError, 'OT0105');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
};

// resolver function to update the overtime records for the employees
module.exports.updateOvertimeDetails = async (parent, args, context, info) => {
    console.log('Inside updateOvertimeDetails() function');
    // get db connection
    const orgDb = knex(context.connection.OrganizationDb);
    let validationError = {};
    try{
        // Check form view access rights
        let loginEmpId = context.Employee_Id;
        // Check form edit access rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(orgDb, loginEmpId, formName.overtime, '', 'UI');
        // check checkRights is not empty json or not
        if (Object.keys(checkRights).length === 0) {
            // throw access denied error
            throw ('_DB0102');
        } else {
            // check logged-in employee is employee admin or not
            let checkEmpAdminRights = await commonLib.func.checkEmployeeAccessRights(orgDb, loginEmpId, formName.employeeAdmin, '', 'UI');
            // we can allow the user to update the records if they are admin/superadmin/employee admin/payroll admin
            if (checkRights.Role_Update === 1 || (Object.keys(checkEmpAdminRights).length > 0 && checkEmpAdminRights.Role_Update === 1) || (checkRights.Employee_Role.toLowerCase() === 'admin')) {
                // validate only the inputs - employeeId, otStartTime, otEndTime,reason in the validateInputs function
                // validate shiftAllowanceAppliedWorkScheduleId in the overtimeWageCalculation function
                validationError = validateInputs(args);
                // if filter month not exists
                if(!args.filterMonth){
                    validationError['IVE0144'] = commonLib.func.getError('', 'IVE0144').message;
                }

                // if filter year not exists
                if(!args.filterYear){
                    validationError['IVE0143'] = commonLib.func.getError('', 'IVE0143').message;
                }
                // check validation error is exist or not
                if (Object.keys(validationError).length === 0) {
                    // get input values
                    let { employeeId, overtimeClaimId, otStartTime, otEndTime, status, shiftAllowanceAppliedWorkScheduleId } = args;
                    // get workSchedule details
                    let currentWorkScheduleDetails = await commonLib.employees.getCurrentWorkScheduleDetails(employeeId, otStartTime, orgDb);
                    let { regularFrom, regularTo, considerationFrom, considerationTo } = currentWorkScheduleDetails;
                    // Check regularFrom, regularTo, considerationFrom, considerationTo is not empty
                    if (Object.keys(currentWorkScheduleDetails).length > 0 && regularFrom && regularTo && considerationFrom && considerationTo){
                        let overtimeSettings = await getOvertimeSettings(orgDb);

                        if(Object.keys(overtimeSettings).length > 0){
                            let overtimeClaimMonthPayslipExist = 0;
                            /** If the compensatory off or shift allowance is applicable for overtime */
                            if(overtimeSettings.Comp_Off_Applicable_For_Overtime || overtimeSettings.Shift_Allowance_Applicable_For_Overtime){
                                let { filterMonth,filterYear } = args;
                                let employeeClaimMonthStartDate = filterYear+'-'+((filterMonth < 10) ? ('0'+filterMonth) : filterMonth)+'-01';//2021-04-01
                                overtimeClaimMonthPayslipExist = await validateEmployeeClaimMonth(orgDb,employeeId,employeeClaimMonthStartDate);
                            }
                            
                            /** If compensatory off or shift allowance claim is enabled in the the overtime settings and 
                             * if the payslip is generated for the employee for the additional wage claim processing month.*/
                            if(overtimeClaimMonthPayslipExist === 1){
                                console.log('Payslip is generated for the claim month. overtime claim id,',overtimeClaimId);
                                throw('OT0137');
                            }else{   
                                // Form regularFrom
                                regularFrom = regularFrom.toISOString().replace(/T/, ' ').replace(/\..+/, '');// "2020-09-09 23:50:00"                        
                                // convert date time to date format.
                                let regularFromDate = moment(regularFrom).format("YYYY-MM-DD");

                                // The overtime claim start date may fall on next day in case of overlapping shift. So we need to check leave and compoff exist for the employee for the shift regular start date
                                let { totalLeaveHours, totalCompOffDuration } = await checkLeaveAndCompOff(employeeId,regularFromDate,orgDb);
                                // Check totalLeaveHours is greater than or equal to 9 hours (4.5 -> Half day, 9 -> Full day)
                                // Check totalCompOffDuration is greater than or equal to 1 (0.5 -> Half day, 1 -> Full day)
                                // If leave or compOff applied for half day then we can allow them to apply Overtime record
                                // Scenarios:
                                // Leave half day - CompOff half day - Not allow
                                // Leave None     - CompOff None     - Allow
                                // Leave full day - CompOff half day - Not allow
                                // Leave full day - CompOff full day - Not allow (Scenario will not come )
                                // Leave None     - CompOff half day - Allow
                                // Leave half day - CompOff full day - Not allow
                                if ((parseFloat(totalLeaveHours) < 4.5 && parseFloat(totalCompOffDuration) < 0.5) ||
                                    (parseFloat(totalLeaveHours) <= 4.5 && parseFloat(totalCompOffDuration) < 0.5) ||
                                    (parseFloat(totalLeaveHours) < 4.5 && parseFloat(totalCompOffDuration) <= 0.5)) {
                                    /** The overtime claim start date may fall on next day in case of overlapping shift. So get attendance count by sending the 
                                     * regular from date and current work schedule details. The attendance count will be fetched based on the consideration start and end time. */
                                    let getAttendanceDetails = await commonLib.employees.getAttendance(regularFromDate, employeeId, orgDb, 'get-count', '',currentWorkScheduleDetails);
                                    
                                    if(!getAttendanceDetails.errorCode){
                                    let isAttendanceExist = getAttendanceDetails.attendanceDetails;
                                    // check isAttendanceExist is 0 or not. If attendance exist then allow them to apply overtime records
                                    if (isAttendanceExist) {
                                        // get record and check lock flag
                                        let overtimeRecord = await orgDb(ehrTables.overtime)
                                                            .select('Lock_Flag')
                                                            .where('Overtime_Claim_Id', overtimeClaimId);

                                        // check record exists or not
                                        if (overtimeRecord[0]){
                                            // check lockflag is exists
                                            if(overtimeRecord[0].Lock_Flag === 0){
                                                // Call function getEmployeeTimeZone() to get current date and time based on employee time zone 
                                                // Send employeeId ,dbConnection and another flag as 1(This flag is to get the time zone date with time)
                                                let empTimeZone = await commonLib.func.getEmployeeTimeZone(loginEmpId, orgDb, 1);
                                                args.source = "Back-End";
                                                args.action = "Edit";
                                                args.inputWorkScheduleDetails = currentWorkScheduleDetails;
                                                // Call overtimeWageCalculation to calculate the overtime wages
                                                let response = await overtimeWageCalculation(parent, args, context, info);
                                                let totalHours = response.totalHours;
                                                let overtimeWages = response.overtimeWages;
                                                let inputData = {};
                                                let managerId = 0;
                                                // check status based on that form input data
                                                if (status.toLowerCase() === 'applied') {
                                                    // get approver manager based on the status from UI
                                                    managerId = await getApprover(status, employeeId, orgDb);
                                                    inputData = {
                                                        Employee_Id: employeeId,
                                                        Start_Date_Time: otStartTime,
                                                        End_Date_Time: otEndTime,
                                                        Total_Hours: totalHours,
                                                        Overtime_Wage: overtimeWages,
                                                        Approval_Status: status,
                                                        First_Level_Approver: managerId ? managerId : loginEmpId,
                                                        Reason: args.reason ? args.reason : null,
                                                        Shift_Allowance_Applied_Work_Schedule_Id: shiftAllowanceAppliedWorkScheduleId,
                                                        Updated_On: empTimeZone,
                                                        Updated_By: loginEmpId
                                                    };
                                                } else {
                                                    // get approver manager based on the status from UI
                                                    managerId = await getApprover(status, loginEmpId, orgDb);
                                                    inputData = {
                                                        Employee_Id: employeeId,
                                                        Start_Date_Time: otStartTime,
                                                        End_Date_Time: otEndTime,
                                                        Total_Hours: totalHours,
                                                        Overtime_Wage: overtimeWages,
                                                        Approval_Status: status,
                                                        First_Level_Approver: loginEmpId,
                                                        Second_Level_Approver: managerId ? managerId : loginEmpId,
                                                        Reason: args.reason ? args.reason : null,
                                                        Shift_Allowance_Applied_Work_Schedule_Id: shiftAllowanceAppliedWorkScheduleId,
                                                        Updated_On: empTimeZone,
                                                        Updated_By: loginEmpId
                                                    };
                                                }
                                                // update overtime record in overtime_claims table
                                                await orgDb(ehrTables.overtime)
                                                    .update(inputData)
                                                    .where('Overtime_Claim_Id',overtimeClaimId)
                                                    .then(() =>{
                                                        return "Successfully updated";
                                                    })
                                                    .catch(updateError =>{
                                                        console.log('Error while updating the overtime details in updateOvertimeDetails() function .catch block.',updateError);
                                                        errResult = commonLib.func.getError('', 'OT0003');
                                                        throw(errResult);
                                                    });
                                                // form inputs to update system log activities commonly
                                                let systemLogParams = {
                                                    action: systemLogs.roleUpdate,
                                                    userIp: context.User_Ip,
                                                    employeeId: loginEmpId,
                                                    formName: formName.overtime,
                                                    trackingColumn: '',
                                                    organizationDbConnection: orgDb,
                                                    uniqueId: overtimeClaimId
                                                };
                                                // call function createSystemLogActivities() to update system log activities
                                                await commonLib.func.createSystemLogActivities(systemLogParams);
                                                // destroy database connection
                                                orgDb ? orgDb.destroy() : null;
                                                // return response back to function
                                                return { errorCode: '', message: 'Overtime record updated successfully' };
                                            }else{
                                                // throw error if some one already editing this record
                                                throw ('_EC0003');
                                            }
                                        }else{
                                            // throw error if record is not availble
                                            throw ('_EC0001');
                                        }                        
                                    } else {
                                        // Cannot apply overtime because attendance does not exist
                                        console.log('Attendance does not exist for this date.');
                                        throw ('OT0117');// throw error 
                                    }
                                    }else{
                                        // Cannot apply overtime as error occured while validating the attendance
                                        console.log('Error response from the getAttendance() function.',getAttendanceDetails);
                                        throw(getAttendanceDetails.errorCode);// throw error
                                    }
                                } else {
                                    // Cannot apply overtime because either leave or comp off already exist for this date
                                    console.log('Leave or compensentory off already exist for this date.');
                                    // throw error 
                                    throw ('OT0118');
                                }
                            }
                        }else{
                            console.log('Empty overtime settings. Response from the overtimeSettings() function,',overtimeSettings);
                            throw('OT0120');
                        }
                    }else{
                        // throw error if shift is not scheduled
                        throw ('OT0107');
                    }                
                }else{
                    // throw validation error
                    throw ('IVE0000');
                }
            }else{
                // throw access denied error
                throw ('_DB0102');
            }            
        }
    } catch (updateOvertimeDetailsMainCatchError){
        console.log('Error while in updateOvertimeDetails() function main catch block.',updateOvertimeDetailsMainCatchError);
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
        // check input validation error or not
        if (updateOvertimeDetailsMainCatchError === 'IVE0000') {
            errResult = commonLib.func.getError('', updateOvertimeDetailsMainCatchError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            // get error and return it to UI
            let errResult = commonLib.func.getError(updateOvertimeDetailsMainCatchError, 'OT0111');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
};