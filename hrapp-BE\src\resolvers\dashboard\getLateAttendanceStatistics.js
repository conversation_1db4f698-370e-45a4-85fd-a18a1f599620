module.exports.getLateAttendanceStatistics = async(parent, args, context, info) => {
    console.log("In getLateAttendanceStatistics function");
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // Organization database connection
    const knex = require('knex');
    // get the organization data base connection
    let orgDb = knex(context.connection.OrganizationDb);
    let moment = require('moment-timezone');
    const { ehrTables } = require('../../../common/tablealias');

    try
    {
        let logInEmpId = context.Employee_Id;
        let orgCode=context.Org_Code;

        let payslipMonth=1;

        let timezone = await commonLib.func.getEmployeeTimeZone(logInEmpId, orgDb, 0);

        let todayDate = moment().tz(timezone).format('YYYY-MM-DD');
        
        let currentYear= parseInt(moment(todayDate).year());
        let currentMonth = parseInt(moment(todayDate).format('MM'));

        let salaryDate=await commonLib.func.getSalaryDay(orgCode,orgDb,payslipMonth,currentYear);

        
        // get leave closure start date
        let leaveClosureStartDate=salaryDate.Salary_Date;

        let attendanceSetting = await commonLib.employees.getAttendanceSettings(orgDb, logInEmpId)
        if(!leaveClosureStartDate)
        {
            return {
                errorCode: null,
                message: "Leave closure dates not updated",
                lateAttendanceStatistics: null
            }
        }
        else if(!attendanceSetting || Object.keys(attendanceSetting).length === 0)
        {
            return {
                errorCode: null,
                message: "Late attendance not configured for the work schedule associated with the employee",
                lateAttendanceStatistics: null
            }
        }

        let startDate;
        if(attendanceSetting.Delayed_Entry_Maximum_Limit_Period && attendanceSetting.Delayed_Entry_Maximum_Limit_Period === 'Per Year')
        {
            // If late attendance period is 'Per Year' then start date should be leave closure start date
            startDate = leaveClosureStartDate;
        }
        else
        {
            // If late attendance period is 'Per Month' then start date should be paycycle start date
            let currentMonthSalaryDates = await commonLib.func.getSalaryDay(orgCode,orgDb,currentMonth,currentYear,todayDate);
            startDate = currentMonthSalaryDates.Salary_Date;
        }
        let usedLateAttendanceCount = await orgDb.count('Late_Attendance as lateAttendance')
                                            .from(ehrTables.attendance)
                                            .where('PunchIn_Date', '>=', startDate)
                                            .where('Employee_Id', logInEmpId)
                                            .whereIn('Late_Attendance', [1,2,3,4])
                                            .first()
                                            .then((res)=>{
                                                return res.lateAttendance
                                            })
        return {
            errorCode: null,
            message: 'Late attendance statistics retrieved successfully',
            lateAttendanceStatistics: JSON.stringify({
                            maximumLateAttendanceCount : attendanceSetting.Delayed_Entry_Maximum_Limit,
                            lateAttendanceLeaveFrequency: attendanceSetting.Late_Attendance_Leave_Frequency,
                            usedLateAttendanceCount
                        })
        }
    }
    catch(getLateAttendanceStatisticsErr)
    {
        console.log('Error in getLateAttendanceStatistics() function main catch block.',getLateAttendanceStatisticsErr);
        
        errResult = commonLib.func.getError('', 'DB0023');

        throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, lateAttendanceStatistics: null}));
    }
    finally
    {
        orgDb.destroy();
    }
}