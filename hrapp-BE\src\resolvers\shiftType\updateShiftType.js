const { ApolloError } = require('apollo-server-lambda');
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require table names
const { ehrTables } = require('../../../common/tablealias');
// require common constant files
const { formName, roles, systemLogs } = require('../../../common/appconstants');
// Organization database connection
const knex = require('knex');

// function to create and update shift types
module.exports.updateShiftType = async (parent, args, context, info) =>{

    const orgDb = knex(context.connection.OrganizationDb);
    try {
        let action ;
        let role ;
        if(args.shiftTypeId) {
            //Edit
            action = "Edit";
            role = roles.roleUpdate;
        } else {
            //Add
            action = "Add";
            role = roles.roleAdd;
        }
        // Check form view access rights
        let loginId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(orgDb, loginId, formName.shiftScheduling, role);

        if (checkRights === true) {

            let isShiftTypeExist = await orgDb(ehrTables.empShiftType).count('Shift_Type_Id as count')
                                        .where("WorkSchedule_Id",args.workScheduleId)
                                        .andWhere((qb)=>{
                                            if(args.shiftTypeId)
                                            {
                                                qb.whereNot("Shift_Type_Id",args.shiftTypeId);
                                            }
                                        })
                                        .then((res)=>{
                                            return res[0];
                                        })

            if(isShiftTypeExist.count > 0)
            {
                let errResult = commonLib.func.getError('', 'ST0102');
                return {
                    success: false,
                    errorCode: errResult.code,
                    message: errResult.message
                }
            }
            else
            {
                if(action == "Edit")
                {
                    let isShiftUsed = await orgDb(ehrTables.shiftEmpMapping).count('Shift_Type_Id as count')
                                                .where('Shift_Type_Id', args.shiftTypeId)
                                                .then((res)=>{
                                                    return res[0].count > 0 ? true : false;
                                                })
                    if(isShiftUsed)
                    {
                        let oldWorkScheduleId = await orgDb(ehrTables.empShiftType).where("Shift_Type_Id",args.shiftTypeId).first()
                                                        .then((shiftDetails)=>{
                                                            return shiftDetails['WorkSchedule_Id']
                                                        })
                        
                        if(oldWorkScheduleId != args.workScheduleId)
                        {
                            let errResult = commonLib.func.getError('', 'ST0116');
                            return {
                                success: false,
                                errorCode: errResult.code,
                                message: errResult.message
                            }
                        }
                    }

                    // Edit shift types
                    await orgDb(ehrTables.empShiftType)
                            .where("Shift_Type_Id",args.shiftTypeId)
                            .update({
                                WorkSchedule_Id: args.workScheduleId,
                                Minimum_Employee_Count: args.minimumEmployeeCount,
                                Maximum_Employee_Count: args.maximumEmployeeCount,
                                Holiday_Override: args.holidayOverride,
                                Comments: args.comments ? args.comments : null,
                                Colour_Code: args.colourCode ? args.colourCode : null,
                                Updated_By: loginId
                            })
                            .then(async()=>{
                                // form inputs to update system log activities
                                let systemLogParams = {
                                    action: systemLogs.roleUpdate,
                                    userIp: context.User_Ip,
                                    employeeId: loginId,
                                    formName: 'Shift Type',
                                    trackingColumn: '',
                                    organizationDbConnection: orgDb,
                                    uniqueId: args.shiftTypeId
                                };

                                // call function createSystemLogActivities() to update system log activities
                                await commonLib.func.createSystemLogActivities(systemLogParams);

                                return "Shift type updated successfully."
                                
                            })
                            .catch((err)=>{
                                console.log('Error in updateShiftType', err);
                                let errResult = commonLib.func.getError('', 'ST0103');
                                throw new ApolloError(errResult.message,errResult.code )
                            })

                    // return response to UI
                    return {
                        success: true,
                        errorCode: null,
                        message: "Shift type updated successfully"
                    };
                    
                } else {
                    // Add shift types
                    await orgDb(ehrTables.empShiftType)
                            .insert({
                                Shift_Type_Id: args.shiftTypeId,
                                WorkSchedule_Id: args.workScheduleId,
                                Minimum_Employee_Count: args.minimumEmployeeCount,
                                Maximum_Employee_Count: args.maximumEmployeeCount,
                                Holiday_Override: args.holidayOverride,
                                Comments: args.comments ? args.comments : null,
                                Colour_Code: args.colourCode ? args.colourCode : null,
                                Added_By: loginId
                            })
                            .then(async(insertShiftTypes)=>{
                                // form inputs to update system log activities
                                let systemLogParams = {
                                    action: systemLogs.roleAdd,
                                    userIp: context.User_Ip,
                                    employeeId: loginId,
                                    formName: 'Shift Type',
                                    trackingColumn: '',
                                    organizationDbConnection: orgDb,
                                    uniqueId: insertShiftTypes[0]
                                };  

                                // call function createSystemLogActivities() to update system log activities
                                await commonLib.func.createSystemLogActivities(systemLogParams);

                                return "Shift added successfully";
                            })
                            .catch((err)=>{
                                console.log('Error in updateShiftType', err);
                                let errResult = commonLib.func.getError('', 'ST0103');
                                throw new ApolloError(errResult.message,errResult.code )
                            });

                    // return response to UI
                    return {
                        success: true,
                        errorCode: null,
                        message: "Shift type added successfully"
                    };
                }
            }
        } else if(checkRights === false) {
            let errResult;
            if(action == "Edit") {
                errResult = commonLib.func.getError('', '_DB0102');
            } else {
                errResult = commonLib.func.getError('', '_DB0101');
            }
            return {
                success : false,
                errorCode : errResult.code,
                message : errResult.message
            }
        } else {
            console.log("Error while checking rights in updateShiftType", checkRights);
            let errResult = commonLib.func.getError('', 'ST0103');
            return {
                success : false,
                errorCode : errResult.code,
                message : errResult.message
            }
        }
    }
    catch(err) {
        console.log('Error in updateShiftType function main catch block',err);
        let errResult = commonLib.func.getError('', 'ST0103');
        throw new ApolloError(errResult.message,errResult.code )
    } finally {
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
    }
}

// Update the shift type status
module.exports.updateShiftStatus = async (parent, args, context, info) =>{
    const orgDb = knex(context.connection.OrganizationDb);
    try
    {
        let loginId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(orgDb, loginId, formName.shiftScheduling, roles.roleUpdate);

        if (checkRights === true) {
            let {shiftTypeId, status} = args;

            let shiftExist = await orgDb.select('Status').from(ehrTables.empShiftType)
                                        .where('Shift_Type_Id',shiftTypeId)
    
            if(shiftExist[0])
            {
                if((shiftExist[0].Status == 'Active' && status == 'InActive') ||
                    (shiftExist[0].Status == 'InActive' && status == 'Active'))
                {
                    let isUpdated = await orgDb(ehrTables.empShiftType)
                                            .update({
                                                Status: status
                                            })
                                            .where('Shift_Type_Id', shiftTypeId)
                    if(isUpdated)
                    {
                        // form inputs to update system log activities
                        let systemLogParams = {
                            action: systemLogs.roleUpdate,
                            userIp: context.User_Ip,
                            employeeId: loginId,
                            formName: 'Shift Type',
                            trackingColumn: (status.toLowerCase() === 'active') ? ' - Activate the shift' : ' - Inactivate the shift',
                            organizationDbConnection: orgDb,
                            uniqueId: shiftTypeId
                        };

                        // call function createSystemLogActivities() to update system log activities
                        await commonLib.func.createSystemLogActivities(systemLogParams);

                        // return response back to UI
                        return {
                            success: true,
                            errorCode: null,
                            message: 'Status updated successfully'
                        }
                    }
                    else
                    {
                        let errResult = commonLib.func.getError('', 'ST0111');
                        return {
                            success: false,
                            errorCode: errResult.code,
                            message: errResult.message
                        }
                    }
                }
                else
                {
                    let errResult = commonLib.func.getError('', 'ST0110');
                    return {
                        success: false,
                        errorCode: errResult.code,
                        message: errResult.message
                    }
                }
            }
            else
            {
                let errResult = commonLib.func.getError('', 'ST0108');
                return {
                    success: false,
                    errorCode: errResult.code,
                    message: errResult.message
                }
            }
        } else if (checkRights === false) {
            let errResult = commonLib.func.getError('', '_DB0102');
            return {
                success : false,
                errorCode : errResult.code,
                message : errResult.message
            }
        } else {
            console.log("Error while checking rights in updateShiftStatus", checkRights);
            let errResult = commonLib.func.getError('', 'ST0115');
            return {
                success : false,
                errorCode : errResult.code,
                message : errResult.message
            }
        }
    }
    catch(err)
    {
        console.log("Error in updateShiftStatus", err);
        let errResult = commonLib.func.getError('', 'ST0112');
        throw new ApolloError(errResult.message,errResult.code )
    } finally {
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
    }
}