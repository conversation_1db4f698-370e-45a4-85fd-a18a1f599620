// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
// require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../../common/appconstants');
// require table alias
const { ehrTables } = require('../../../common/tablealias');
// require common function
const { getPmsAlreadyExistEmpIds } = require('../../../common/performanceManagementCommonFunction');
// require pms validation function
const performanceManagementValidation = require('../../../common/performanceManagementValidation');

// list the PMS employees
module.exports.listPmsEmployees = async (parent, args, context, info) => {
    console.log("Inside listPmsEmployees() function.");
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const orgCode = context.Org_Code;

        // Check Goals and achievement form access rights.
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, context.Employee_Id, formName.goalsAndAchievement, '', 'UI');

        // If the login employee is admin or manager and having view access for the 'Goals and achievement' form
        if ((Object.keys(checkRights).length >0 && checkRights.Role_View===1) && (checkRights.Is_Manager===1 || checkRights.Employee_Role==='admin')) {
            // function to validate inputs
            validationError=await performanceManagementValidation.employeeGoalsAndRatingInputValidation(args,'validateMonthYear');
            // check whether there is no validation error
            if(Object.keys(validationError).length ===0){
                //Call the function to get the employee ids that has to be excluded in the pms employee list dropdown
                let alreadyexistEmpIds = await getPmsAlreadyExistEmpIds(organizationDbConnection,args.month,args.year);
                args.alreadyexistEmpIds = alreadyexistEmpIds;

                return(
                organizationDbConnection(ehrTables.performanceManagementSettings)
                .select('Present_Only_Team_Members_To_Managers')
                .then(async(settingsResponse) => {
                    if(settingsResponse.length>0){
                        //Form inputs
                        args.isAdmin = (checkRights.Employee_Role && checkRights.Employee_Role.toLowerCase() === 'admin') ? 1 : 0;
                        args.employeeId = context.Employee_Id;

                        let presentOnlyTeamMembersToManagers = settingsResponse[0].Present_Only_Team_Members_To_Managers;

                        let fetchPmsEmployeesType = '';
                        if(presentOnlyTeamMembersToManagers === 'Yes'){
                            fetchPmsEmployeesType = 'getGoalsAndAchievementEmployeesByManager';
                        }else{
                            fetchPmsEmployeesType = 'getGoalsAndAchievementEmployees';
                        }

                        // Get the pms employee details based on the coverage and filter options
                        let pmsEmployeesList = await commonLib.func.getAllEmployeeDetails(organizationDbConnection, fetchPmsEmployeesType, orgCode,
                        process.env.domainName, process.env.region, process.env.hrappProfileBucket, args, null);

                        // destroy database connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        
                        return {
                            errorCode : '',
                            message : "PMS employees list retrieved successfully.",
                            employees: (pmsEmployeesList && pmsEmployeesList.length > 0) ? pmsEmployeesList : []
                        };
                    }else{
                        console.log('Performance management settings does not exist',settingsResponse);
                        throw 'EPM0031';
                    }
                }).catch((catchError) => {
                    console.log('Error in the listPmsEmployees() .catch block',catchError);
                    // destroy database connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    errResult = commonLib.func.getError(catchError, 'EPM0031');
                    // return response
                    throw new ApolloError(errResult.message,errResult.code);
                })
                );
            }else{
                throw 'IVE0000';// throw validation error
            }
        }else{
            throw '_DB0100';// throw employee does not have view access
        }
    }
    catch(listPmsEmployeesMainCatchErr) {
        console.log('Error in the listPmsEmployees() function main catch block. ',listPmsEmployeesMainCatchErr);
        // destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (listPmsEmployeesMainCatchErr === 'IVE0000') {
            console.log('Validation error in the listPmsEmployees() function. ',validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else{
            errResult = commonLib.func.getError(listPmsEmployeesMainCatchErr, 'EPM0107');
            // return response
            throw new ApolloError(errResult.message,errResult.code);
        }
    }
};

// list the PMS reviewers
module.exports.listPmsReviewers = async (parent, args, context, info) => {
    console.log(" Inside listPmsReviewers() function.");
    let organizationDbConnection;
    let errResult;
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const orgCode = context.Org_Code;

        // Check Goals and achievement form access rights.
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, context.Employee_Id, formName.goalsAndAchievement, '', 'UI');

        // If the login employee is admin or manager and having view access for the 'Goals and achievement' form
        if ((Object.keys(checkRights).length >0 && checkRights.Role_View===1) && (checkRights.Is_Manager===1 || checkRights.Employee_Role==='admin')) {
            // Get the pms reviewers details
            let pmsReviewersList = await commonLib.func.getAllEmployeeDetails(organizationDbConnection, 'getGoalsAndAchievementReviewers', orgCode,
            process.env.domainName, process.env.region, process.env.hrappProfileBucket, args, null);

            // destroy database connection
            organizationDbConnection ? organizationDbConnection.destroy() : null;

            return {
                errorCode : '',
                message : "PMS reviewers list retrieved successfully.",
                reviewers: (pmsReviewersList && pmsReviewersList.length > 0 ) ? pmsReviewersList : []
            }
        }else{
            throw '_DB0100';
        }
    }catch(listPmsReviewersMainCatchErr) {
        console.log('Error in the listPmsReviewers() function main catch block. ',listPmsReviewersMainCatchErr);
        // destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(listPmsReviewersMainCatchErr, 'EPM0108');
        // return response
        throw new ApolloError(errResult.message,errResult.code);
    }
};