// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require file to access constant values
const { formName } = require('../../common/appconstants');
// require table alias
const { ehrTables } = require('../../common/tablealias');

// resolver definition
const resolvers = {
    Query:{
        // function to list all the service provider details
        listAllServiceProviderDetails: async (parent, args, context, info) => {
            // variable declarations
            let organizationDb='';
            let loggedInEmpId=context.Employee_Id;
            try{
                console.log('Inside listAllServiceProviderDetails function',args);
                // get the organization database connection
                organizationDb = knex(context.connection.OrganizationDb);
                // check general form access rights
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDb, loggedInEmpId, formName.general,'','UI');
                if (Object.keys(checkRights).length > 0) {
                    if(checkRights.Role_View === 1 && checkRights.Employee_Role.toLowerCase() === 'admin'){
                       // retrieve all the service provider admin details
                        return(
                            organizationDb
                            .select('SP.Service_Provider_Id as serviceProviderId', 'SP.Service_Provider_Name as serviceProviderName', 'SP.Contact_Person_Name as contactPerson', 
                            'SP.Contact_Person_Phone_Number as phoneNumber','SP.Email_Id as emailId','SP.Website as website','SP.Service_Provider_Logo as serviceProviderLogo',
                            'SP.Service_Provider_Mou as serviceProviderMou','L.Location_Name as location')
                            .from(ehrTables.serviceProvider +' as SP')
                            .innerJoin(ehrTables.location + ' as L','SP.Location_Id','L.Location_Id')
                            .then(async (getDetails) =>{
                                organizationDb ? organizationDb.destroy() : null;
                                return { errorCode:'',message:'Service provider details listed successfully.',getProviderDetails:(getDetails.length>0)?JSON.stringify(getDetails):''};
                            })
                            .catch(function (catchError) {
                                console.log('Error in listAllServiceProviderDetails function .catch block.',catchError);
                                organizationDb ? organizationDb.destroy() : null;
                                errResult = commonLib.func.getError(catchError, 'SGE0103');
                                // throw error response to UI
                                throw new ApolloError(errResult.message, errResult.code);
                            })
                        );
                    }
                    else{
                        throw '_DB0100';
                    }
                }
                else{
                    throw '_DB0100';
                }
            }
            catch(mainCatchError)
            {
                console.log('Error in listAllServiceProviderDetails function main catch block',mainCatchError);
                // destroy database connection
                organizationDb ? organizationDb.destroy() : null;
                let errResult = commonLib.func.getError(mainCatchError, 'SGE0002');
                throw new ApolloError(errResult.message,errResult.code);
            }
        }
    }
};

exports.resolvers = resolvers;
