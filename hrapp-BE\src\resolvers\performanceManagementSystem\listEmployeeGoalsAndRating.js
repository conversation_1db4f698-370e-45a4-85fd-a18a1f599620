// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require moment package
const moment = require('moment-timezone');
// require common constant files
const { formName,defaultValues } = require('../../../common/appconstants');
// require table alias
const { ehrTables } = require('../../../common/tablealias');
// require validation file
const performanceManagementValidation = require('../../../common/performanceManagementValidation');
// require common function
const { getEmployeeIdBasedOnReviewer,getPerformanceMonthYear } = require('../../../common/performanceManagementCommonFunction');

// variable declarations
let errResult = {};
let organizationDbConnection = '';
let validationError;

// resolver definition
const resolvers = {
    Query: {
        // function to list the employee performance goals and ratings
        listEmployeeGoalsAndRating: async (parent, args, context, info) => {
            try{
                console.log('Inside listEmployeeGoalsAndRating function');
                // get the organization data base connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                // variable declarations
                let logInEmpId = context.Employee_Id;
                let goalsSubQuery;
                // check whether the employee has view access to goalsAndAchievement form or not & loggedIn employee should be either admin or manager
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.goalsAndAchievement, '', 'UI');
                if ((Object.keys(checkRights).length >0 && checkRights.Role_View===1) && (checkRights.Is_Manager===1 || checkRights.Employee_Role==='admin')) {
                    // function to validate inputs
                    validationError=await performanceManagementValidation.employeeGoalsAndRatingInputValidation(args,'validateMonthYear');
                    // check whether there is no validation error
                    if(Object.keys(validationError).length ===0){
                        // form subquery to get the goals and achievement for the input year and month
                        let subQuery=organizationDbConnection(ehrTables.performanceGoalAchievement + ' as PGA')
                        .select('PGA.Performance_Assessment_Id as performanceAssessmentId','PGA.Employee_Id as employeeId','PGA.Reviewer_Employee_Id as reviewerEmployeeId',
                        'PGA.Assessment_Status as assessmentStatus','PGA.Goal_Publish_Status as goalPublishStatus',
                        'PGA.Rating_Publish_Status as ratingPublishStatus','PGA.Overall_Rating as overallRating',
                        'PGA.Average_Yearly_Rating as averageYearlyRating','PGA.Comments as comments','PGA.Added_On as addedOn','PGA.Updated_On as updatedOn',
                        organizationDbConnection.raw("CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as addedByEmployeeName"),
                        organizationDbConnection.raw('CASE WHEN EJ1.User_Defined_EmpId IS NOT NULL THEN EJ1.User_Defined_EmpId ELSE EJ1.Employee_Id END as addedByEmployeeId'),
                        organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name,EPI1.Emp_Middle_Name, EPI2.Emp_Last_Name) as updatedByEmployeeName"),
                        organizationDbConnection.raw('CASE WHEN EJ2.User_Defined_EmpId IS NOT NULL THEN EJ2.User_Defined_EmpId ELSE EJ2.Employee_Id END as updatedByEmployeeId'))
                        .leftJoin(ehrTables.empPersonalInfo+' as EPI1','EPI1.Employee_Id','PGA.Added_By')
                        .leftJoin(ehrTables.empJob+' as EJ1','EJ1.Employee_Id','PGA.Employee_Id')
                        .leftJoin(ehrTables.empPersonalInfo+' as EPI2','EPI2.Employee_Id','PGA.Updated_By')
                        .leftJoin(ehrTables.empJob+' as EJ2','EJ2.Employee_Id','PGA.Employee_Id')
                        .where('PGA.Performance_Assessment_Month',args.month)
                        .where('PGA.Performance_Assessment_Year',args.year)
                        // if loggedIn employee is admin then list all the employees  
                        // if loggedIn employee is an manager then return records of employee under this employee along with employee whose reviewer is loggedIn employee
                        if(checkRights.Employee_Role!=='admin'){
                            // function to get employeeid under the loggedIn employeeId
                            let employeeIdArray=await getEmployeeIdBasedOnReviewer(organizationDbConnection,logInEmpId);
                            goalsSubQuery=subQuery
                            .where(qb => {
                                qb.where('PGA.Reviewer_Employee_Id', logInEmpId)
                                qb.orWhereIn('PGA.Employee_Id', employeeIdArray)
                            });
                        }
                        else{
                            goalsSubQuery=subQuery
                        }
                        return(
                            goalsSubQuery
                            .then(async(listDetails)=>{
                                // check whether data exist or not
                                if(listDetails.length>0)
                                {
                                    // get organization date format
                                    let {Date_Format}=await commonLib.func.getOrgDetails(context.Org_Code,organizationDbConnection,0);

                                    // iterate the user record in employee list
                                    for (let userRecord of listDetails){
                                        // function to get employee details
                                        let userProfile= await commonLib.func.getAllEmployeeDetails(organizationDbConnection, 'listEmployeeDetails', context.Org_Code,
                                        process.env.domainName, process.env.region, process.env.hrappProfileBucket, { employeeId: [userRecord.employeeId] }, null);
                                        // check data exist or not
                                        if(userProfile.length>0){
                                            userRecord.designation=userProfile[0].designation_name;
                                            userRecord.department=userProfile[0].department_name;
                                            userRecord.employeeName=userProfile[0].employee_name;
                                            userRecord.userDefinedEmpId=userProfile[0].user_defined_empid;
                                            userRecord.dateOfJoining=userProfile[0].doj?await commonLib.func.getFormattedDateString('',Date_Format,userProfile[0].doj):'';
                                            userRecord.employeeStatus=userProfile[0].emp_status;
                                            userRecord.photoPath=userProfile[0].photo_path;
                                        }
                                        // if reviewer id exist then get reviewer name
                                        if(userRecord.reviewerEmployeeId){
                                            let userProfile=await commonLib.func.getAllEmployeeDetails(organizationDbConnection, 'listEmployeeDetails', context.Org_Code,
                                            process.env.domainName, process.env.region, process.env.hrappProfileBucket, { employeeId: [userRecord.reviewerEmployeeId] }, null);
                                            userRecord.reviewerName=(userProfile.length)?userProfile[0].employee_name:'';
                                            userRecord.reviewerUserDefinedEmpId=(userProfile.length)?userProfile[0].user_defined_empid:''
                                        }
                                    }
                                }
                                // destroy database connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode:'',message:'Employee goals and ratings listed successfully.',listEmployeeGoalRatingsDetails:listDetails};
                            })
                            .catch(catchError => {
                                console.log('Error in listEmployeeGoalsAndRating function .catch block',catchError);
                                errResult = commonLib.func.getError(catchError, 'EPM0105');
                                // destroy database connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                // return response
                                throw new ApolloError(errResult.message,errResult.code);              
                            })
                        );
                    }
                    else{
                        throw 'IVE0000';
                    }
                }
                else{
                    throw '_DB0100';
                }
            }catch(mainCatchError){
                console.log('Error in listEmployeeGoalsAndRating function main catch block',mainCatchError);
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                if (mainCatchError === 'IVE0000') {
                    errResult = commonLib.func.getError('', 'IVE0000');
                    // return response
                    throw new UserInputError(errResult.message, { validationError: validationError });
                } else {
                    errResult = commonLib.func.getError(mainCatchError, 'EPM0007');
                    // return response
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        },
        // function to list the employee's achievement
        // for presenting employee view - for employees,manager and admin (My Goals and Achievement)
        listEmployeesAchievement: async (parent, args, context, info) => {
            try{
                console.log('Inside listEmployeesAchievement function');
                // get the organization data base connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                // variable declarations
                let logInEmpId = context.Employee_Id;
                // check whether the employee has view access to goalsAndAchievement form or not
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.goalsAndAchievement, '', 'UI');
                if ((Object.keys(checkRights).length >0 && checkRights.Role_View===1)) {
                    // function to validate inputs
                    validationError=await performanceManagementValidation.employeeGoalsAndRatingInputValidation(args,'listEmployeeAchievements');
                    // check whether there is no validation error
                    if(Object.keys(validationError).length ===0){
                        let input={
                            "month": args.startMonth,
                            "year": args.startYear
                        }
                        // get the months and year for the given performance year
                        let performanceYearMonths = await getPerformanceMonthYear(context.Org_Code,organizationDbConnection,'getseparatemonthsyear',input);
                        let subQuery=organizationDbConnection(ehrTables.performanceGoalAchievement)
                        .select('Performance_Assessment_Month as performanceAssessmentMonth','Performance_Assessment_Year as performanceAssessmentYear','Employee_Id',
                        'Assessment_Status as assessmentStatus','Overall_Rating as overallRating','Goal_Publish_Status as goalPublishStatus','Rating_Publish_Status as ratingPublishStatus')

                        // iterate all the performance month and year to form the where condition to get the average rating
                        for(let inputMonthYear of performanceYearMonths){
                            subQuery = subQuery.orWhere(qb => {
                                qb.where('Performance_Assessment_Month', inputMonthYear.month)
                                qb.where('Performance_Assessment_Year', inputMonthYear.year)
                                qb.andWhere('Employee_Id',logInEmpId)
                            });
                        }

                        return(
                            subQuery
                            .orderBy('Performance_Assessment_Year', 'DESC')    
                            // get the performance details for the input year/month for the employee
                            // for now it is order based on year alone. Need to update this based on month and date field. 
                            .then(async(listDetails)=>{
                                // check data exist or not
                                if(listDetails.length>0){
                                    for (let userRecord of listDetails){
                                        // based on the month number get short form of month name
                                        // example, monthNumber-12,assessmentMonth will be Dec
                                        let assessmentMonth=moment.monthsShort((userRecord.performanceAssessmentMonth) - 1);
                                        // concat month and year - Dec 2020
                                        let assessmentMonthYear=assessmentMonth.concat(" ",userRecord.performanceAssessmentYear);
                                        userRecord.assessmentMonthYear=assessmentMonthYear;
                                        // if rating is not published then return the overal rating as 0
                                        if(userRecord.ratingPublishStatus===defaultValues.goalsAndAchievementGoalUnPublishStatus){
                                            userRecord.overallRating=0;
                                        }
                                    }
                                }
                                // destroy database connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode:'',message:'Employee goals and achievements listed successfully.',listEmployeeAchievementDetails:(listDetails.length>0)?listDetails:[]};
                            })
                            .catch(catchError => {
                                console.log('Error in listEmployeesAchievement function .catch block',catchError);
                                errResult = commonLib.func.getError(catchError, 'EPM0115');
                                // destroy database connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                // return response
                                throw new ApolloError(errResult.message,errResult.code);              
                            })
                        );
                    }
                    else{
                        throw 'IVE0000';
                    }
                }
                else{
                    throw '_DB0100';
                }
            }catch(mainCatchError){
                console.log('Error in listEmployeesAchievement function main catch block',mainCatchError);
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                if (mainCatchError === 'IVE0000') {
                    errResult = commonLib.func.getError('', 'IVE0000');
                    // return response
                    throw new UserInputError(errResult.message, { validationError: validationError });
                } else {
                    errResult = commonLib.func.getError(mainCatchError, 'EPM0013');
                    // return response
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        }
    }
};

exports.resolvers = resolvers;