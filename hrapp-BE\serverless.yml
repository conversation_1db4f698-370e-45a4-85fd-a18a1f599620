service: HRAPPBACKEND # service name

plugins:
  - serverless-domain-manager
  - serverless-prune-plugin # PlugIn to maintain lambda versoning
  - serverless-offline # require plugins
  # - '@haftahave/serverless-ses-template' # Plug-In to deploy SES templates
  - serverless-step-functions

provider:
  name: aws
  runtime: nodejs22.x #nodejs run time
  stage: ${opt:stage} # get current stage name
  region: ${opt:region} #region in which to be deployed
  role: ${file(config.${self:provider.stage}.json):lambdaRole} # Assign role to the lambda functions
  vpc:
    securityGroupIds: ${file(./config.${self:provider.stage}.json):securityGroupIds}
    subnetIds: ${file(./config.${self:provider.stage}.json):subnetIds}
custom:
  # sesTemplatesConfigFile: '../ses-email-templates/index.js'
  sesTemplatesRegion: ${file(./config.${self:provider.stage}.json):sesRegion}
  customDomain:
    domainName: ${file(./config.${self:provider.stage}.json):customDomainName}
    basePath: 'hrappBe'
    stage: ${self:provider.stage}
    createRoute53Record: true
    endpointType: 'edge'
  prune:
    automatic: true
    number: 3

# Lambda functions
functions:
  graphql:
    handler: src/handler.graphql
    timeout: 900 # Lambda timeout
    events:
      - http:
          path: graphql
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - user_ip
              - refresh_token
              - partnerid
              - additional_headers
            allowCredentials: false
          authorizer:
            arn: ${file(config.${self:provider.stage}.json):authorizerARN}
            resultTtlInSeconds: 0
            type: request
    environment: # environment variables
      stageName: ${self:provider.stage}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      hrappProfileBucket: ${file(config.${self:provider.stage}.json):hrappProfileBucket}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}
      screenshotsBucket: ${file(config.${self:provider.stage}.json):screenshotsBucket}
      emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
      sesTemplatesRegion: ${self:custom.sesTemplatesRegion}
      webAddress: ${file(config.${self:provider.stage}.json):webAddress}
      desktopClientURL: ${file(config.${self:provider.stage}.json):desktopClientURL}
      updateRosterLeaveBatch: ${file(config.${self:provider.stage}.json):updateRosterLeaveBatch}

  beforesigninhandler:
    handler: src/beforesigninhandler.beforesigninhandler
    timeout: 29 # Lambda timeout
    events:
      - http:
          path: signinhandler
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - user_ip
              - partnerid
              - refresh_token
              - additional_headers
    environment: # environment variables
      stageName: ${self:provider.stage}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      firebaseApiKey: ${file(config.${self:provider.stage}.json):firebaseApiKey}

  settingshandler:
    handler: src/settingshandler.settingshandler
    timeout: 29 # Lambda timeout
    events:
      - http:
          path: settingsgraphql
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - user_ip
              - refresh_token
              - partnerid
              - additional_headers
            allowCredentials: false
          authorizer:
            arn: ${file(config.${self:provider.stage}.json):authorizerARN}
            resultTtlInSeconds: 0
            type: request
    environment: # environment variables
      stageName: ${self:provider.stage}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      hrappProfileBucket: ${file(config.${self:provider.stage}.json):hrappProfileBucket}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}

  settingsroGraphql:
    handler: src/settingsrohandler.settingsroGraphql
    timeout: 29 # Lambda timeout
    events:
      - http:
          path: settingsroGraphql
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - user_ip
              - refresh_token
              - partnerid
              - additional_headers
            allowCredentials: false
          authorizer:
            arn: ${file(config.${self:provider.stage}.json):authorizerARN}
            resultTtlInSeconds: 0
            type: request
    environment: # environment variables
      stageName: ${self:provider.stage}  
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      hrappProfileBucket: ${file(config.${self:provider.stage}.json):hrappProfileBucket}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}

  settingswoGraphql:
      handler: src/settingswohandler.settingswoGraphql
      timeout: 29 # Lambda timeout
      events:
        - http:
            path: settingswoGraphql
            method: post
            cors:
              origin: '*'
              headers:
                - Content-Type
                - X-Amz-Date
                - X-Api-Key
                - X-Amz-Security-Token
                - X-Amz-User-Agent
                - authorization
                - org_code
                - user_ip
                - refresh_token
                - partnerid
                - additional_headers
              allowCredentials: false
            authorizer:
              arn: ${file(config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      environment: # environment variables
        stageName: ${self:provider.stage}
        domainName: ${file(config.${self:provider.stage}.json):domainName}
        source: BE
        region: ${self:provider.region}
        hrappProfileBucket: ${file(config.${self:provider.stage}.json):hrappProfileBucket}
        dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
        dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}

  triggerEmailNotificationStepFunction:
    handler: src/resolvers/performanceManagementSystem/emailNotifications/initiateEmailNotification.triggerEmailNotificationStepFunction
    timeout: 900 # Lambda timeout
    events:
      - schedule:
          name: pms-goal-rating-reminder-notification-${self:provider.stage}
          description: 'This rule is used to trigger email reminder to configure goals and provide ratings for the current performance month.'
          rate: cron(30 18 * * ? *) #12AM
          enabled: true
    environment:
      stateMachineArn: ${file(config.${self:provider.stage}.json):pmsEmailNotificationArn}

  checkReminderNotificationExist:
    handler: src/resolvers/performanceManagementSystem/emailNotifications/checkReminderNotificationExist.checkReminderNotificationExist
    memorySize: 3008
    timeout: 900
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}

  sendReminderMail:
    handler: src/resolvers/performanceManagementSystem/emailNotifications/sendReminderMail.sendReminderMail
    memorySize: 3008
    timeout: 900
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
      sesTemplatesRegion: ${self:custom.sesTemplatesRegion}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      webAddress: ${file(config.${self:provider.stage}.json):webAddress}

  triggerBatchEmailNotificationStepFunction:
    handler: src/resolvers/dashboard/stepFunctions/triggerBatchEmailNotification.triggerBatchEmailNotification
    timeout: 900 # Lambda timeout
    events:
      - schedule:
          name: batch-email-notification-${self:provider.stage}
          description: 'This rule is used to trigger email based on the batch email notification configuration.'
          rate: cron(30 18 * * ? *) #12AM
          enabled: true
    environment:
      stateMachineArn: ${file(config.${self:provider.stage}.json):batchEmailNotificationArn}

  initiateBatchEmailNotification:
    handler: src/resolvers/dashboard/stepFunctions/initiateBatchEmailNotification.initiateBatchEmailNotification
    memorySize: 3008
    timeout: 900
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}

  sendProbationNotification:
    handler: src/resolvers/dashboard/stepFunctions/sendProbationNotification.sendProbationNotification
    memorySize: 3008
    timeout: 900
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
      sesTemplatesRegion: ${self:custom.sesTemplatesRegion}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      webAddress: ${file(config.${self:provider.stage}.json):webAddress}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}

  sendProbationNotificationToHr:
    handler: src/resolvers/dashboard/stepFunctions/sendProbationNotificationToHr.sendProbationNotificationToHr
    memorySize: 3008
    timeout: 900
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
      sesTemplatesRegion: ${self:custom.sesTemplatesRegion}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      webAddress: ${file(config.${self:provider.stage}.json):webAddress}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}

  sendResignationNotificationToManager:
    handler: src/resolvers/dashboard/stepFunctions/sendResignationNotificationToManager.sendResignationNotificationToManager
    memorySize: 3008
    timeout: 900
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
      sesTemplatesRegion: ${self:custom.sesTemplatesRegion}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      webAddress: ${file(config.${self:provider.stage}.json):webAddress}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}

  sendResignationNotificationToHr:
    handler: src/resolvers/dashboard/stepFunctions/sendResignationNotificationToHr.sendResignationNotificationToHr
    memorySize: 3008
    timeout: 900
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
      sesTemplatesRegion: ${self:custom.sesTemplatesRegion}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      webAddress: ${file(config.${self:provider.stage}.json):webAddress}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}

  sendLongLeaveNotificationToManager:
    handler: src/resolvers/dashboard/stepFunctions/sendLongLeaveNotificationToManager.sendLongLeaveNotificationToManager
    memorySize: 3008
    timeout: 900
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
      sesTemplatesRegion: ${self:custom.sesTemplatesRegion}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      webAddress: ${file(config.${self:provider.stage}.json):webAddress}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}

  sendLongLeaveNotificationToHr:
    handler: src/resolvers/dashboard/stepFunctions/sendLongLeaveNotificationToHr.sendLongLeaveNotificationToHr
    memorySize: 3008
    timeout: 900
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
      sesTemplatesRegion: ${self:custom.sesTemplatesRegion}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      webAddress: ${file(config.${self:provider.stage}.json):webAddress}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}

  roGraphql:
    handler: src/rohandler.roGraphql
    timeout: 900 # Lambda timeout
    events:
      - http:
          path: roGraphql
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - user_ip
              - refresh_token
              - partnerid
              - additional_headers
            allowCredentials: false
          authorizer:
            arn: ${file(config.${self:provider.stage}.json):authorizerARN}
            resultTtlInSeconds: 0
            type: request
    environment: # environment variables
      stageName: ${self:provider.stage}
      source: BE
      region: ${self:provider.region}
      hrappProfileBucket: ${file(config.${self:provider.stage}.json):hrappProfileBucket}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      asynchronousreportBucket: ${file(config.${self:provider.stage}.json):asynchronousreportBucket}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      webAddress: ${file(config.${self:provider.stage}.json):webAddress}

  woGraphql:
    handler: src/wohandler.woGraphql
    timeout: 900 # Lambda timeout
    events:
      - http:
          path: woGraphql
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - user_ip
              - refresh_token
              - partnerid
              - additional_headers
            allowCredentials: false
          authorizer:
            arn: ${file(config.${self:provider.stage}.json):authorizerARN}
            resultTtlInSeconds: 0
            type: request
    environment: # environment variables
      stageName: ${self:provider.stage}
      source: BE
      region: ${self:provider.region}
      hrappProfileBucket: ${file(config.${self:provider.stage}.json):hrappProfileBucket}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      webAddress: ${file(config.${self:provider.stage}.json):webAddress}
      asynchronousreportBucket: ${file(config.${self:provider.stage}.json):asynchronousreportBucket}
      emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
      sesTemplatesRegion: ${self:custom.sesTemplatesRegion}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}
      generateReportAsyncStepFunction: ${file(config.${self:provider.stage}.json):generateReportAsyncStepFunction}

  generateReportAsync:
    handler: src/woresolvers/reportGenerator/generateReportAsync.generateReportAsync
    memorySize: 3008
    timeout: 900
    environment:
      region: ${self:provider.region}
      hrappProfileBucket: ${file(config.${self:provider.stage}.json):hrappProfileBucket}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      webAddress: ${file(config.${self:provider.stage}.json):webAddress}
      asynchronousreportBucket: ${file(config.${self:provider.stage}.json):asynchronousreportBucket}
      emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
      sesTemplatesRegion: ${self:custom.sesTemplatesRegion}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}
      stageName: ${self:provider.stage}

stepFunctions:
  stateMachines:
    pmsReminderNotification:
      name: ${opt:stage}-pmsReminderNotification
      events:
        - http:
            path: pmsReminderNotificationStepFunction
            method: POST
            cors: true
            authorizer:
              arn: ${file(config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: 'We need to send reminder email notification for assigning goals and assessing ratings for the performance month.'
        StartAt: checkPMSReminderNotification
        States:
          checkPMSReminderNotification:
            Type: Task
            Resource: ${file(config.${self:provider.stage}.json):resourceArnPrefix}-checkReminderNotificationExist
            Next: NotificationFirstStepChoiceState
          NotificationFirstStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Step2
                Next: sendReminderMail
            Default: EndFunction
          sendReminderMail:
            Type: Task
            Resource: ${file(config.${self:provider.stage}.json):resourceArnPrefix}-sendReminderMail
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: 'Email notification send successfully.'
            End: true

    batchEmailNotification:
      name: ${opt:stage}-batchEmailNotification
      events:
        - http:
            path: batchEmailNotificationStepFunction
            method: POST
            cors: true
            authorizer:
              arn: ${file(config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: 'We need to send email notification based on the batch email notification configuration.'
        StartAt: initiateBatchEmailNotification
        States:
          initiateBatchEmailNotification:
            Type: Task
            Resource: ${file(config.${self:provider.stage}.json):resourceArnPrefix}-initiateBatchEmailNotification
            Next: NotificationFirstStepChoiceState
          NotificationFirstStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Step2
                Next: processParallelEmailNotification
            Default: EndFunction
          processParallelEmailNotification:
            Type: Parallel
            Next: EndFunction
            Branches:
              - StartAt: sendProbationNotification
                States:
                  sendProbationNotification:
                    Type: Task
                    Resource: ${file(config.${self:provider.stage}.json):resourceArnPrefix}-sendProbationNotification
                    End: true
              - StartAt: sendProbationNotificationToHr
                States:
                  sendProbationNotificationToHr:
                    Type: Task
                    Resource: ${file(config.${self:provider.stage}.json):resourceArnPrefix}-sendProbationNotificationToHr
                    End: true
              - StartAt: sendResignationNotificationToManager
                States:
                  sendResignationNotificationToManager:
                    Type: Task
                    Resource: ${file(config.${self:provider.stage}.json):resourceArnPrefix}-sendResignationNotificationToManager
                    End: true
              - StartAt: sendResignationNotificationToHr
                States:
                  sendResignationNotificationToHr:
                    Type: Task
                    Resource: ${file(config.${self:provider.stage}.json):resourceArnPrefix}-sendResignationNotificationToHr
                    End: true
              - StartAt: sendLongLeaveNotificationToManager
                States:
                  sendLongLeaveNotificationToManager:
                    Type: Task
                    Resource: ${file(config.${self:provider.stage}.json):resourceArnPrefix}-sendLongLeaveNotificationToManager
                    End: true
              - StartAt: sendLongLeaveNotificationToHr
                States:
                  sendLongLeaveNotificationToHr:
                    Type: Task
                    Resource: ${file(config.${self:provider.stage}.json):resourceArnPrefix}-sendLongLeaveNotificationToHr
                    End: true
          EndFunction:
            Type: Pass
            Result: 'Email notification sent successfully.'
            End: true

    generateReportAsyncStepFunction:
      name: ${opt:stage}-generateReportAsyncStepFunction
      events:
        - http:
            path: generateReportAsyncStepFunction
            method: POST
            cors: true
            authorizer:
              arn: ${file(config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: 'Initiate report generator.'
        StartAt: processGenerateReportAsync
        States:
          processGenerateReportAsync:
            Type: Task
            Resource: ${file(config.${self:provider.stage}.json):resourceArnPrefix}-generateReportAsync
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: 'Generate report step function execution completed.'
            End: true
resources:
  Resources:
    ApiGatewayRestApi: # Map customized api gateway responses
      Type: AWS::ApiGateway::RestApi
      Properties:
        Name: ${self:service}-${self:provider.stage}

    GatewayResponse4XX: # statusCode 4XX series errorcode
      Type: 'AWS::ApiGateway::GatewayResponse'
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: 'ApiGatewayRestApi'
        ResponseType: DEFAULT_4XX # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "Forbidden." } }'

    GatewayResponse401: # statusCode 401
      Type: 'AWS::ApiGateway::GatewayResponse'
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: 'ApiGatewayRestApi'
        ResponseType: UNAUTHORIZED # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        StatusCode: '401' # API gateway default errorcode
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "Unauthorized request." } }'

    GatewayResponse5XX: # statusCode 5XX series error code
      Type: 'AWS::ApiGateway::GatewayResponse'
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: 'ApiGatewayRestApi'
        ResponseType: DEFAULT_5XX # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "API gateway timeout." } }'
