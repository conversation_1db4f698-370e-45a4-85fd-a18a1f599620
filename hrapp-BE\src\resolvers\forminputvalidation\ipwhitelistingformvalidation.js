// require common validation function file
const validation = require('../../../common/commonvalidation');
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// define functions
module.exports = {
    validateIpWhitelistForm : (args) =>{
        var ipAddresses = args.ipAddresses;
        var description = args.description;
        var validationError = {};
        var validate;
        // validate IP address
        if (ipAddresses.length > 0){
            // iterate and validate the IPAddress
            for(var ip of ipAddresses){
                validate = ip ? validation.ipAddressValidation(ip) : validationError['IVE0009'] = commonLib.func.getError('', 'IVE0009').message1;
                if (validate === false) {
                    validationError['IVE0009'] = commonLib.func.getError('', 'IVE0009').message2;
                    break;
                }                             
            }
            // check same IP Address is repeated or not
            var tmpIpAddressArray = ipAddresses.filter(function (elem, pos) {
                return ipAddresses.indexOf(elem) == pos;
            });
            // check array length if tmpIpAddressArray is lessthan ipAddresses, then IPAddress is reapted
            if (tmpIpAddressArray.length < ipAddresses.length){
                validationError['IVE0009'] = commonLib.func.getError('', 'IVE0009').message3;
            }
        }else{
            validationError['IVE0009'] = commonLib.func.getError('', 'IVE0009').message1;
        }
        // validation for description
        if (description){
            // Check description length also send min and max length
            validate = validation.checkLength(description, 5, 500);
            if (!validate) {
                validationError['IVE0002'] = commonLib.func.getError('', 'IVE0002').message;
            }
        }
        // return input validation error to resolvers
        return validationError;
    }
};