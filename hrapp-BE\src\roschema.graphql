# defining custom data type
scalar Date

type Query {
  listProbationEmployees(isTeamDashboard: Int!): listProbationEmployeesResponse!
  retrievePmsNonComplianceReport(
    month: Int!
    year: Int!
  ): pmsNonComplianceReportResponse!
  checkAttendanceConfiguration(
    employeeId: Int
  ): checkAttendanceConfigurationResponse!
  getEmployeeDetailsBasedOnGroup(
    customGroupId: [Int]!
  ): getEmployeeDetailsBasedOnGroupResponse!
  getReportHistory: getReportHistoryResponse
  getReportSetting: getReportSettingResponse
  getResignationPayslipDetails (employeeId:Int!): getResignationPayslipDetailsResponse
  getTeamLeaveHistory: teamLeaveHistoryResponse
  retrieveDashboardExpiredDocsDetails(selfService: Int!):retrieveDashboardExpiredDocsDetailsResponse
  listShiftRotation: listShiftRotationResponse!
  listShiftLeaveTypes: listShiftLeaveTypesResponse!
  listshiftSwapping(formId: Int!, employeeId: Int,swapIdList:[Int],alternateSwapDate: String): listshiftSwappingResponse!
  getShiftSwapDateRange(employeeId:Int!) : shiftSwapDateRangeResponse!
}
type listShiftLeaveTypesResponse{
  errorCode: String
  message: String
  shiftLeaveTypes: String
}
type listShiftRotationResponse{
  errorCode: String
  message: String
  shiftRotation: String
}
type retrieveDashboardExpiredDocsDetailsResponse {
  errorCode: String
  message: String
  ExpiredDocuments: ExpiredDocuments
}
type ExpiredDocuments{
  getPassportDetails:[passportComplianceDetails]
  getLicenseDetails:[licenseComplianceDetails]
  getAccredidationDetails:[accreditationComplianceDetails]
}
type passportComplianceDetails{
Employee_Id: Int
expiryDate: String
Expiry_Date: String
Document_Name: String
userDefinedEmpId: String
Status: String
Employee_Name: String
Passport_No: String
Issuing_Authority: String
Issuing_Country: String
issueDate: String
}
type licenseComplianceDetails{
Employee_Id: Int
expiryDate: String
Expiry_Date: String
Document_Name: String
userDefinedEmpId: String
Status: String
Employee_Name: String
Driving_License_No: String
Issuing_Authority: String
Issuing_Country: String
Issuing_State: String
Vehicle_Type: String
issueDate: String
}
type accreditationComplianceDetails{
Employee_Id: Int
expiryDate: String
Expiry_Date: String
Document_Name: String
userDefinedEmpId: String
Status: String
Employee_Name: String
Identifier: String
File_Name: String
Accreditation_Category: String
receivedDate: String

}

type listProbationEmployeesResponse {
  errorCode: String
  message: String
  probationEmployeesDetails: [probationEmployeesDetails]
}

type probationEmployeesDetails {
  employeeId: Int
  userDefinedEmployeeId: String
  employeeName: String
  dateOfJoin: String
  probationDate: String
  probationDays: Int
}

type pmsNonComplianceReportResponse {
  errorCode: String
  message: String
  employees: [pmsEmployeesListDetails]
}

type pmsEmployeesListDetails {
  userDefinedEmployeeId: String
  employeeName: String
  performanceMonthName: String
  performanceYear: Int
  reviewerName: String
  dateOfJoin: String
  designationName: String
  departmentName: String
  locationName: String
  status: String
}

type checkAttendanceConfigurationResponse {
  errorCode: String
  message: String
  attendanceConfigurationDetails: [attendenceConfiguration]
}

type attendenceConfiguration {
  attendanceTypeId: Int
  attendanceConfigurationName: String
  geoFencingEnabled: String
  facialRecognitionEnabled: String
  forceFencing: String
  forceFaicalRecognition: String
  enableLivenessDetection: String
  noOfChallenges: Int
  centerPoint: String
  radius: Int
}

type getEmployeeDetailsBasedOnGroupResponse {
  errorCode: String
  message: String
  employeeDetails: [employeeDetails]
}

type employeeDetails {
  employeeId: Int!
  userDefinedEmpId: String
  employeeName: String
  designationName: String
  departmentName: String
  groupId: Int
  groupName: String
}

type getReportHistoryResponse {
  errorCode: String
  message: String
  reportHistoryDetails: [reportHistoryDetails]
}

type reportHistoryDetails {
  historyId: Int
  reportId: Int
  reportTitle: String
  awsPath: String
  presignedUrl: String
  requestParams: String
  employeeId: Int
  requestDateTime: String
  status: String
  errorMessage: String
}

type getReportSettingResponse {
  errorCode: String
  message: String
  reportSettingDetails: [reportSettingDetails]
}

type getResignationPayslipDetailsResponse {
  errorCode: String
  message: String
  payslipDetails : getResignationPayslipDetails
}

type getResignationPayslipDetails {
  payslipGenerated:Int!,
  salaryDate : String!,
  lastSalaryDate:String!,
  resignationDateOverride:Int!
}

type reportSettingDetails {
  reportId: Int
  reportTitle: String
  reportFormat: String
  formName: String
  groupTitle: String
  reportHeaders: String
  filters: String
  orderBy: String
  allowSameRequestTimer: String
  timerSecond: String
  retentionPeriodAllowed: String
  retentionPeriodInMonth: String
}

type teamLeaveHistoryResponse{
  errorCode: String
  message: String
  leaveHistory: String
}
type listshiftSwappingResponse {
  errorCode: String
  message: String
  shiftSwapping: [shiftSwappingDetails]
}

type shiftSwappingDetails {
  Swap_Id: Int
  Employee_Id: Int
  Approver_Id: Int
  Swap_Shift_Type_Id: Int
  Swap_Date: String
  Approval_Status: String
  Added_On: String
  Added_By: Int
  Updated_On: String
  Updated_By: Int
  Shift_Name: String
  Current_Shift_Type_Id:Int
  Current_Shift_Name: String
  Employee_Name: String
  Reason: String
  Added_By_Name: String
  Updated_By_Name: String
  Approver_Name: String
  User_Defined_EmpId: String
}

type shiftSwapDateRangeResponse{
  errorCode: String
  message: String
  minimumDate: Date
  maximumDate: Date
}

schema {
  query: Query
}
