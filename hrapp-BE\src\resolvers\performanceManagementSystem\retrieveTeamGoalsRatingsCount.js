// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
// require apollo server to return error message
const { UserInputError,ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName,defaultValues } = require('../../../common/appconstants');
// require table alias
const { ehrTables } = require('../../../common/tablealias');
// require common function
const { getEmployeeIdBasedOnReviewer } = require('../../../common/performanceManagementCommonFunction');
// require moment package
const moment = require('moment-timezone');
// require pms validation function
const {employeeGoalsAndRatingInputValidation} = require('../../../common/performanceManagementValidation');


// get team count or goal published count or rating published count based on the action and the team
async function getTeamPublishCount(organizationDbConnection,loginEmpId,args,employeeIdArray,isLoginEmployeeAdmin,action='teamTotalCount'){
    try {
        //Query to get the count which are in goals unpublished status for the given assessment period
        let empIdCountQry = organizationDbConnection(ehrTables.performanceGoalAchievement)
        .distinct()
        .count('Employee_Id as count')
        .where('Performance_Assessment_Month', args.month)
        .where('Performance_Assessment_Year', args.year);

        // get goal published count
        if(action.toLowerCase()==='goalspublishedcount'){
            empIdCountQry = empIdCountQry.where('Goal_Publish_Status', defaultValues.goalsAndAchievementGoalPublishStatus);
        }else if(action.toLowerCase()==='ratingspublishedcount'){
            // get rating published count
            empIdCountQry = empIdCountQry.where('Rating_Publish_Status', defaultValues.goalsAndAchievementGoalPublishStatus);
        }else{
            //get the team total employee count
        }

        // if the login employee is not admin
        if(isLoginEmployeeAdmin!=='admin'){
            if(employeeIdArray.length > 0){
                empIdCountQry = empIdCountQry
                .where(qb => {
                    qb.where('Reviewer_Employee_Id', loginEmpId)
                    qb.orWhereIn('Employee_Id', employeeIdArray)
                });
            }else{
                empIdCountQry = empIdCountQry.where('Reviewer_Employee_Id', loginEmpId);
            }
        }

        // get the count
        let employeeCount = await empIdCountQry
        .then(countResponse =>{
            return (countResponse[0].count) ? countResponse[0].count : 0;
        }).catch(retrieveCountCatchError =>{
            console.log('Error in getPublishCount() function .catch block',retrieveCountCatchError);
            throw retrieveCountCatchError;
        });

        return employeeCount;
    }catch(getPublishCountMainCatchError){
        console.log('Error in getPublishCount() function main catch block',getPublishCountMainCatchError);
        throw getPublishCountMainCatchError;
    }
}


// get the team monthly average rating for the assessment period based on the team
async function getTeamMonthlyAvgRating(organizationDbConnection,loginEmpId,args,employeeIdArray,isLoginEmployeeAdmin){
    try {
        // average overall rating query
        let overallRatingQry = organizationDbConnection(ehrTables.performanceGoalAchievement)
        .avg('Overall_Rating as averageMonthlyRating')
        .where('Performance_Assessment_Month', args.month)
        .where('Performance_Assessment_Year', args.year);

        // if the login employee is not admin
        if(isLoginEmployeeAdmin!=='admin'){
            if(employeeIdArray.length > 0){
                overallRatingQry = overallRatingQry
                .where(qb => {
                    qb.where('Reviewer_Employee_Id', loginEmpId)
                    qb.orWhereIn('Employee_Id', employeeIdArray)
                });
            }else{
                overallRatingQry = overallRatingQry.where('Reviewer_Employee_Id', loginEmpId);
            }
        }

        // get the average yearly rating for the employee
        let averageMonthlyRating  = await overallRatingQry.then(averageRatingDetail =>{
            return averageRatingDetail[0].averageMonthlyRating ? (averageRatingDetail[0].averageMonthlyRating.toFixed(1)) : 0;
        }).catch(overallRatingCatchError =>{
            console.log('Error in getTeamMonthlyAvgRating() function .catch block',overallRatingCatchError);
            throw 'EPM0116';
        });

        return averageMonthlyRating;
    }catch(getTeamMonthlyAvgRatingMainCatchError){
        console.log('Error in getTeamMonthlyAvgRating() function main catch block',getTeamMonthlyAvgRatingMainCatchError);
        throw getTeamMonthlyAvgRatingMainCatchError;
    }
}

// retrieve the employee goals count/ rating
module.exports.retrieveTeamGoalsRatingsCount = async (parent, args, context, info) => {
    console.log("Inside retrieveTeamGoalsRatingsCount() function.");
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    try{
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const loginEmpId = context.Employee_Id;

        // check whether the employee has view access to goalsAndAchievement form or not & loggedIn employee should be either admin or manager
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmpId , formName.goalsAndAchievement, '', 'UI');
        if ((Object.keys(checkRights).length >0 && checkRights.Role_View===1) && (checkRights.Is_Manager===1 || checkRights.Employee_Role==='admin')) {
            // function to validate inputs
            validationError=await employeeGoalsAndRatingInputValidation(args,'validateMonthYear');
            // check the input validation error exist or not
            if(Object.keys(validationError).length ===0){
                let responseArray = [];
                let employeeIdArray = [];
                let previousCurrentAssessmentPeriod = [];
                let previousMonthYear = {};

                previousCurrentAssessmentPeriod.push(args);//push the given month and year

                // if month is greater than 1 then month-1 will be previous month and year will be same.
                if(args.month>1){
                    previousMonthYear.month=args.month-1;
                    previousMonthYear.year=args.year;
                }
                else{
                    // if month is 1 then previous month will be 12 and year will be year-1
                    previousMonthYear.month=12;
                    previousMonthYear.year=args.year-1;
                }
                previousCurrentAssessmentPeriod.push(previousMonthYear); //push the previous month and year

                if(previousCurrentAssessmentPeriod.length > 0){
                    let assessmentPeriodResponseJson = {};

                    // if the login employee is not admin
                    if(checkRights.Employee_Role!=='admin'){
                        // get employeeid under the login employeeId
                        employeeIdArray=await getEmployeeIdBasedOnReviewer(organizationDbConnection,loginEmpId);
                    }

                    for(let inputArgs of previousCurrentAssessmentPeriod){
                        assessmentPeriodResponseJson = {};
                        
                        // team employee count
                        let teamEmployeesCount = await getTeamPublishCount(organizationDbConnection,loginEmpId,inputArgs,employeeIdArray,checkRights.Employee_Role,'teamTotalCount');

                        // goals published count
                        let goalsPublishedCount = await getTeamPublishCount(organizationDbConnection,loginEmpId,inputArgs,employeeIdArray,checkRights.Employee_Role,'goalspublishedcount');

                        // ratings published count
                        let ratingsPublishedCount = await getTeamPublishCount(organizationDbConnection,loginEmpId,inputArgs,employeeIdArray,checkRights.Employee_Role,'ratingspublishedcount');

                        // get the assessment period average overall rating
                        let averageOverallRating = await getTeamMonthlyAvgRating(organizationDbConnection,loginEmpId,inputArgs,employeeIdArray,checkRights.Employee_Role);

                        // example response: Apr 2020
                        let previousYearPerformanceStartMonthYear = moment.monthsShort((inputArgs.month) - 1)+" "+(inputArgs.year);

                        assessmentPeriodResponseJson = {
                            teamEmployeesCount: teamEmployeesCount,
                            goalsPublishedCount: goalsPublishedCount,
                            ratingsPublishedCount: ratingsPublishedCount,
                            averageOverallRating: averageOverallRating,
                            monthYear: previousYearPerformanceStartMonthYear
                        }

                        responseArray.push(assessmentPeriodResponseJson)//push the assessment period response
                    }

                    // destroy database connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    
                    return {errorCode:'',message:'Team goals and ratings count retrieved successfully.',teamGoalsRatingsCount:responseArray};
                }else{
                    console.log('Empty assessment period is formed in the retrieveTeamGoalsRatingsCount() function. ');
                    throw 'EPM0121';// throw unable to retrieve the team goals ratings count
                }
            }else{
                throw 'IVE0000';// throw validation error
            }
        }else{
            throw '_DB0100';
        }
    }catch(retrieveTeamGoalsRatingsCountMainCatchErr) {
        console.log('Error in the retrieveTeamGoalsRatingsCount() function main catch block. ',retrieveTeamGoalsRatingsCountMainCatchErr);
        // destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (retrieveTeamGoalsRatingsCountMainCatchErr === 'IVE0000') {
            console.log('Validation error in the retrieveTeamGoalsRatingsCount() function. ',validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError }); // return response
        }else{
            errResult = commonLib.func.getError(retrieveTeamGoalsRatingsCountMainCatchErr, 'EPM0018');
            throw new ApolloError(errResult.message,errResult.code);// return response
        }
    }
};