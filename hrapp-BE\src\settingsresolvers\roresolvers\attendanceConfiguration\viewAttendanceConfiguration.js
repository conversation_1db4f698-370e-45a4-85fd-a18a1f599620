//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const { ehrTables } = require('../../../../common/tablealias');
const { formName } = require('../../../../common/appconstants');

//Resolver function to get the attendance general configuration details
module.exports.viewAttendanceConfiguration = async (parent, args, context, info) => {
    console.log('Inside viewAttendanceConfiguration() function.');
    let organizationDbConnection;
    let errResult;
    try{
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.attendanceConfiguration, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            //Retrieve the details from the table
            return(
                organizationDbConnection(ehrTables.attendanceGeneralConfiguration+' as AGC')
                .select('AGC.Attendance_Regularization_Cut_Off_Days_For_Employee as employeeRegularizationCutOffDays', 'AGC.Attendance_Regularization_Request_Limit_For_Employee as employeeRegularizationRequestLimit',
                'AGC.Attendance_Approval_Cut_Off_Days_For_Manager as attendanceApprovalCutOffDaysForManager','AGC.Updated_On as updatedOn',
                organizationDbConnection.raw("CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as updatedByEmployeeName"))
                .leftJoin(ehrTables.empPersonalInfo+' as EPI1','EPI1.Employee_Id','AGC.Updated_By')
                .then((result)=>{
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    //Return success response
                    return { errorCode:'',message:'Attendance configuration details retrieved successfully.',configurationDetails:(result.length>0)?result[0]:''};
                })
                .catch(catchError => {
                    console.log('Error in viewAttendanceConfiguration() function .catch block',catchError);
                    errResult = commonLib.func.getError(catchError, 'SAC0101');
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    //Return error response
                    throw new ApolloError(errResult.message,errResult.code);
                })
            );
        } else {
            throw '_DB0100';
        }
    }catch(mainCatchError) {
        console.log('Error in the viewAttendanceConfiguration() function main catch block. ',mainCatchError);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(mainCatchError, 'SAC0001');
        //Return error response
        throw new ApolloError(errResult.message,errResult.code);
    }
};