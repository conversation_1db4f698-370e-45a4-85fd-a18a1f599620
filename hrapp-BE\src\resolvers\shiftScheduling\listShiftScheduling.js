// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// list the shift scheduling details
module.exports.listShiftScheduling = async (parent, args, context, info) => {
    const { ApolloError } = require('apollo-server-lambda');
    // require common constant files
    const { formName } = require('../../../common/appconstants');
    // Organization database connection
    const knex = require('knex');
    // require table names
    const { ehrTables } = require('../../../common/tablealias');
    const orgDb = knex(context.connection.OrganizationDb);
    const orgCode = context.Org_Code;

    try{   
        // variable declarations
        let isAdmin=0;
        let sortField;
        let sortOrder = args.sortOrder == 'asc'? 'asc' : 'desc';
        // Check form view access rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(orgDb, context.Employee_Id, formName.shiftScheduling, '','UI');

        // for admin and manager role we will list all the employees
        if (checkRights.Employee_Role.toLowerCase() === 'admin') {
            if(checkRights.Role_View){
                isAdmin = 1;
            }
            else{
                let errResult = commonLib.func.getError('', '_DB0100');
                return {
                    success : false,
                    errorCode : errResult.code,
                    message : errResult.message
                }
            }
        }
        // if employee has view access then list only that employee
        else if(checkRights.Role_View === 1){
            isAdmin = 0;
        }
        else if(checkRights.Role_View === 0)
        {
            let errResult = commonLib.func.getError('', '_DB0100');
            return {
                success : false,
                errorCode : errResult.code,
                message : errResult.message
            }
        }
        else{
            console.log("Error while checking rights in listShiftScheduling", checkRights);
            let errResult = commonLib.func.getError('', 'SS0141');
            return {
                success : false,
                errorCode : errResult.code,
                message : errResult.message
            }
        }

        switch (args.sortField)
        {
            case 1: sortField = 'P.Emp_First_Name'; break;
            case 2: sortField = 'SE.Shift_Start_Date'; break;
            case 3: sortField = 'SE.Shift_End_Date'; break;
            case 4: sortField = 'Shift_Type_Name'; break;
            default:sortField = 'SE.Shift_Start_Date'; sortOrder = 'desc';
        }
        // get month and date
        let [month, year] = args.filterMonth.split(',');

        // form filer start and end date based on the month chosen from the UI (Check payscycle type as well)
        let { Salary_Date, Last_SalaryDate } = await commonLib.func.getSalaryDay(context.Org_Code, orgDb, month, year);

        // form query to list the scheduled shifts
        let subQuery= orgDb.select("SE.Shift_Schedule_Id", "SE.Employee_Id", "P.Photo_Path",
                            "SE.Shift_Type_Id", "WS.Title as Shift_Type_Name", "SE.Shift_Start_Date", "SE.Shift_End_Date",'EJ.User_Defined_EmpId',
                            orgDb.raw('CASE WHEN SE.Week_Off = 0 then "No" Else "Yes" END as Week_Off'),
                            orgDb.raw( "CONCAT_WS(' ',P.Emp_First_Name,P.Emp_Middle_Name, P.Emp_Last_Name) as Employee_Name"),
                            orgDb.raw( "CONCAT_WS(' ',P1.Emp_First_Name,P1.Emp_Middle_Name, P1.Emp_Last_Name) as Added_By"), "SE.Added_On",
                            orgDb.raw( "CONCAT_WS(' ',P2.Emp_First_Name,P2.Emp_Middle_Name, P2.Emp_Last_Name) as Updated_By"), "SE.Updated_On" )
            .from(ehrTables.shiftEmpMapping  + " as SE")
            .leftJoin(ehrTables.empShiftType + " as EST", "EST.Shift_Type_Id", "SE.Shift_Type_Id")
            .leftJoin(ehrTables.workSchedule + " as WS", "WS.WorkSchedule_Id", "EST.WorkSchedule_Id")
            .leftJoin(ehrTables.empPersonal  + " as P", "P.Employee_Id", "SE.Employee_Id")
            .leftJoin(ehrTables.empJob       +' as EJ','P.Employee_Id', 'EJ.Employee_Id')
            .leftJoin(ehrTables.empPersonal  + " as P1", "P1.Employee_Id", "SE.Added_By")
            .leftJoin(ehrTables.empPersonal  + " as P2", "P2.Employee_Id", "SE.Updated_By")
            .orderBy(sortField, sortOrder)
            .where((qb)=>{
                if(args.searchString )
                {
                    qb.andWhere(orgDb.raw("CONCAT_WS(' ',P.Emp_First_Name,P.Emp_Middle_Name, P.Emp_Last_Name)"), 'like', '%' + args.searchString + '%')
                    qb.orWhere('WS.Title', 'like', '%' + args.searchString + '%')
                }
                if(args.employeeName)
                {
                    qb.andWhere(orgDb.raw("CONCAT_WS(' ',P.Emp_First_Name,P.Emp_Middle_Name, P.Emp_Last_Name)"), 'like', '%' + args.employeeName + '%')
                }
                if (args.shiftStartDate && args.shiftEndDate) {
                    qb.where(function() {
                      this.whereBetween('SE.Shift_Start_Date', [args.shiftStartDate, args.shiftEndDate])
                        .andWhereBetween('SE.Shift_End_Date', [args.shiftStartDate, args.shiftEndDate])
                        .orWhere(function() {
                          this.orWhereRaw('? BETWEEN SE.Shift_Start_Date AND SE.Shift_End_Date', [args.shiftStartDate])
                            .orWhereRaw('? BETWEEN SE.Shift_Start_Date AND SE.Shift_End_Date', [args.shiftEndDate]);
                        });
                    });
                }
                  
                if(args.shiftTypeId && args.shiftTypeId.length>0)
                {
                    qb.whereIn('SE.Shift_Type_Id', args.shiftTypeId)
                }
            })
            .where((qb) => {
                if(Salary_Date && Last_SalaryDate){
                    qb.whereBetween('Shift_Start_Date', [Salary_Date, Last_SalaryDate])
                    qb.orWhereBetween('Shift_End_Date', [Salary_Date, Last_SalaryDate])
                }
            });

            // if user does not have admin access 
            if(!isAdmin){
                // If the loggedIn employee is manager then return them and their subordinate employees details otherwise return their record alone
                subQuery = subQuery
                            .where('EJ.Manager_Id', context.Employee_Id)
                            .orWhere('SE.Employee_Id',context.Employee_Id)
            }
            
            return(
                subQuery
                .then(async(shift)=>{

                    let count = shift.length;

                    let getPreSignedUrl = await getProfilePic();
                    for(let i = 0; i < count; i++)
                    {
                        shift[i]["Photo_Path"] = await getPreSignedUrl(shift[i]["Employee_Id"], shift[i]["Photo_Path"], orgCode)
                    }

                    return {
                        success : true,
                        errorCode : null,
                        message : "Successfully retrieved shift scheduling",
                        shiftSchedule : shift
                    }
                })
                .catch(function (err){
                    console.log('Error in listShiftScheduling function .catch block',err);
                    let errResult = commonLib.func.getError(err, 'SS0116');
                    throw new ApolloError(errResult.message,errResult.code )
                })
            )
    } catch (listError) {
        console.log('Error in listShiftScheduling function main catch block',listError);
        let errResult  = commonLib.func.getError('', 'SS0116');
        throw new ApolloError(errResult.message,errResult.code )
    } finally {
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
    }
}

// To create a presigned url for profile picture
async function getProfilePic()
{
    // Many shift can be scheduled for single employee. So once the presigned url is created for an employee
    // save the url in this json with employee id as key ans url as the value.
    // Before creating the presigned url, check this json if the employee id is present then return its value
    // otherwise create a presigned url and save it in json and return the url
    let profilePics = {};

    return async function(employeeId, photoPath, orgCode)
    {
        try 
        {
            if(profilePics.hasOwnProperty(employeeId))
            {
                return profilePics[employeeId];
            }
            else
            {
                let url = null;
                if(photoPath)
                {
                    let fileName = await commonLib.func.formS3FilePath(photoPath, orgCode, 'profile', '', process.env.domainName);
                    url = fileName ? await commonLib.func.getFileURL(process.env.region, process.env.hrappProfileBucket, fileName) : null;
                }

                profilePics[employeeId] = url;
                return url;
            }
        }
        catch(err)
        {
            console.log("Error while creating presigned url",err);
            return null;
        }
    }
}