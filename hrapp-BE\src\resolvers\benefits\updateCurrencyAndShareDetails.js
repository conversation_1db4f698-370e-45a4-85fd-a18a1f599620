// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError,UserInputError } = require('apollo-server-lambda');
// require file to access constant values
const { formName,systemLogs } = require('../../../common/appconstants');
// require table alias function
const { ehrTables } = require('../../../common/tablealias');
// require validation file
const commonInputValidation=require('../../../common/commonvalidation');

// variable declarations
let organizationDbConnection='';
let errResult={};

// resolver definition
const resolvers = {
    Mutation:{
        // function to update currency code and value of a share
        updateCurrencyAndShareDetails: async (parent, args, context, info) => {
            let validationError={};
            try{
                console.log('Inside updateCurrencyAndShareDetails function');
                // variable declarations
                let employeeTimeZone;
                let logInEmpId=context.Employee_Id;
                let ipAddress=context.User_Ip;
                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                // check whether employee have update access for ESOP form
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.esop, '', 'UI');  
                if(Object.keys(checkRights).length>0 && (checkRights.Role_Update === 1)) {
                    // Check benefits admin form - update access rights exist for employee or not
                    let checkBenefitsAdminRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.benefitsAdmin, '', 'UI');
                    if(Object.keys(checkBenefitsAdminRights).length>0 && checkBenefitsAdminRights.Role_Update === 1) {               
                        // validate the input shareValue
                        if(args.shareValue && (args.shareValue>0)){
                            let validate = commonInputValidation.decimalValidation(args.shareValue);
                            if (!validate) {
                                validationError['IVE0158'] = commonLib.func.getError('', 'IVE0158').message;
                            }
                        }
                        else{
                            validationError['IVE0158'] = commonLib.func.getError('', 'IVE0158').message;
                        }
                        // validate the input currencyId
                        if(args.currencyId && (args.currencyId>0)){
                            let validate = commonInputValidation.numberValidation(args.currencyId);
                            if (!validate) {
                                validationError['IVE0157'] = commonLib.func.getError('', 'IVE0157').message;
                            }
                        }
                        else{
                            validationError['IVE0157'] = commonLib.func.getError('', 'IVE0157').message;
                        }
                        // check validation error exist or not
                        if (Object.keys(validationError).length === 0) {
                            // check whether the benefits record exist or not
                            return(
                                organizationDbConnection
                                .select('Currency_Id')
                                .from(ehrTables.benefitsConfiguration)
                                .then(async(getDetails) => {
                                    // get the employee timezone based on location
                                    employeeTimeZone = await commonLib.func.getEmployeeTimeZone(logInEmpId, organizationDbConnection,1);
                                    // form the input params
                                    let inputParams={
                                        Currency_Id:args.currencyId,
                                        Value_Of_Share:args.shareValue,
                                        Updated_By:logInEmpId,
                                        Updated_On:employeeTimeZone
                                    }
                                    /** if record already exist then update the record else insert record in benefit configuration table. */
                                    if(getDetails.length>0){
                                        return(
                                            organizationDbConnection(ehrTables.benefitsConfiguration)
                                            .update(inputParams)
                                            .then(async() => {
                                                // update the system log
                                                await updateSystemLog(organizationDbConnection,logInEmpId,ipAddress,'Currency and share value');
                                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                return { errorCode:'',message:'Currency and share value details updated successfully.'};
                                            })
                                        );
                                    }
                                    else{
                                        return(
                                            organizationDbConnection(ehrTables.benefitsConfiguration)
                                            .insert(inputParams)
                                            .then(async() => {
                                                // update the system log
                                                await updateSystemLog(organizationDbConnection,logInEmpId,ipAddress,'Currency and share value');
                                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                return { errorCode:'',message:'Currency and share value details inserted successfully.'};
                                            })
                                        );
                                    }
                                })
                                .catch(function (catchError) {
                                    console.log('Error in updateCurrencyAndShareDetails function .catch block',catchError);
                                    errResult = commonLib.func.getError(catchError, 'BES0103');
                                    // destroy database connection
                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                    throw new ApolloError(errResult.message,errResult.code);
                                })
                            );
                        }
                        else {
                            throw ('IVE0000');
                        }
                    }
                    else{
                        throw '_DB0109';
                    }
                }
                else if (checkRights === false) {
                    throw '_DB0102';
                } else {
                    throw (checkRights);
                }
            }
            catch(mainCatchError)
            {
                console.log('Error in updateCurrencyAndShareDetails function main catch block',mainCatchError);
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                if(mainCatchError==='IVE0000'){
                    errResult = commonLib.func.getError('', 'IVE0000');
                    throw new UserInputError(errResult.message, { validationError: validationError });
                }
                else{
                    errResult = commonLib.func.getError(mainCatchError, 'BES0003');
                    // return error response
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        },
        // function to update the employee share status
        updateEmployeeShareStatus: async (parent, args, context, info) => {
            let validationError={};
            try{
                console.log('Inside updateEmployeeShareStatus function');
                // variable declarations
                let logInEmpId=context.Employee_Id;
                let ipAddress=context.User_Ip;
                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                // check whether employee have update access for ESOP form
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.esop, '', 'UI');  
                if(Object.keys(checkRights).length>0 && (checkRights.Role_Update === 1)) {
                    // Check benefits admin form - update access rights exist for employee or not
                    let checkBenefitsAdminRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.benefitsAdmin, '', 'UI');
                    if(Object.keys(checkBenefitsAdminRights).length>0 && checkBenefitsAdminRights.Role_Update === 1) {
                        // validate the employeeId
                        if(args.employeeId){
                            let validate = commonInputValidation.numberValidation(args.employeeId);
                            if (!validate) {
                                validationError['IVE0083'] = commonLib.func.getError('', 'IVE0083').message;
                            }
                        }
                        else{
                            validationError['IVE0083'] = commonLib.func.getError('', 'IVE0083').message;
                        }
                        // validate the share status
                        if(args.shareStatus){
                            if((args.shareStatus!=='Active') && (args.shareStatus!=='Void')){
                                validationError['IVE0162'] = commonLib.func.getError('', 'IVE0162').message;
                            }
                        }
                        else{
                            validationError['IVE0162'] = commonLib.func.getError('', 'IVE0162').message;
                        }
                        // check validation error exist or not
                        if (Object.keys(validationError).length === 0) {
                            // update the share status in empjob table
                            return(
                                organizationDbConnection(ehrTables.empJob)
                                .update({
                                    ESOP_Status:args.shareStatus
                                })
                                .where('Employee_Id',args.employeeId)
                                .then(async() => {
                                    // update the system log
                                    await updateSystemLog(organizationDbConnection,logInEmpId,ipAddress,'- Update share status as ' +args.shareStatus+' for employeeId',args.employeeId);
                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                    return { errorCode:'',message:'Employee share status updated successfully.'};
                                })
                                .catch(function (catchError) {
                                    console.log('Error in updateCurrencyAndShareDetails function .catch block',catchError);
                                    errResult = commonLib.func.getError(catchError, 'BES0106');
                                    // destroy database connection
                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                    throw new ApolloError(errResult.message,errResult.code);
                                })
                            );
                        }
                        else {
                            throw ('IVE0000');
                        }
                    }
                    else{
                        throw '_DB0109';
                    }
                }
                else if (checkRights === false) {
                    throw '_DB0102';
                } else {
                    throw (checkRights);
                }
            }
            catch(mainCatchError)
            {
                console.log('Error in updateEmployeeShareStatus function main catch block',mainCatchError);
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                if(mainCatchError==='IVE0000'){
                    errResult = commonLib.func.getError('', 'IVE0000');
                    throw new UserInputError(errResult.message, { validationError: validationError });
                }
                else{
                    errResult = commonLib.func.getError(mainCatchError, 'BES0007');
                    // return error response
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        }
    }
};

exports.resolvers = resolvers;

// function to update system logs
async function updateSystemLog(organizationDbConnection,logInEmpId,userIp,message,uniqueId=null){
    try{
        // form inputs to update system log activities commonly
        let systemLogParams = {
            action: systemLogs.roleUpdate,
            userIp: userIp,
            employeeId: logInEmpId,
            formName: formName.esop,
            trackingColumn: message,
            organizationDbConnection: organizationDbConnection,
            uniqueId: (uniqueId)?(uniqueId):''
        }
        // call function createSystemLogActivities() to update system log activities
        await commonLib.func.createSystemLogActivities(systemLogParams);
        return 'success';
    }
    catch(error){
        console.log('Error in updateSystemLog function catch block',error);
        return '';
    }
}