// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require file to access constant values
const { formName,systemLogs } = require('../../../common/appconstants');
// require table alias function
const { ehrTables } = require('../../../common/tablealias');
// require validation file
const commonInputValidation=require('../../../common/commonvalidation');
const moment = require('moment-timezone')

// variable declarations
let organizationDbConnection='';
let errResult={};

// resolver definition
const resolvers = {
    Mutation:{
        // function to bulk import allocate shares to the employees
        bulkImportESOP: async (parent, args, context, info) => {
            try{
                console.log('Inside bulkImportESOP function');
                // variable declarations
                let logInEmpId=context.Employee_Id;
                let ipAddress=context.User_Ip;
                let allocateEmployeeShares=[];
                let errorResponse={};
                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                // check whether employee have add access for ESOP form
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.esop, '', 'UI');  
                if(Object.keys(checkRights).length>0 && (checkRights.Role_Add === 1)) {
                    // Check benefits admin form - edit access rights exist for employee or not
                    let checkBenefitsAdminRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.benefitsAdmin, '', 'UI');
                    if(Object.keys(checkBenefitsAdminRights).length>0 && checkBenefitsAdminRights.Role_Update === 1) {
                        let inputLength=args.empShareDetails.length;
                        let employeeIds = args.empShareDetails.map((el)=>el.employeeId)
                        let resignationDates = await commonLib.payroll.getEmployeeResignationDate(organizationDbConnection, employeeIds)
                        let dateOfJoins = await organizationDbConnection(ehrTables.empJob)
                        .select("Employee_Id", "Date_Of_Join")
                        .whereIn('Employee_Id', employeeIds)
                        // Iterate the loop for all the inputs
                        for(i=0;i<inputLength;i++){
                            // validate the inputs
                            let validationError=await validateInputFields(args.empShareDetails[i], dateOfJoins, resignationDates);
                            // In case of any validation error then form the error response else insert the record
                            if(validationError.length>0){
                                errorResponse[i+1]=validationError;
                            }
                            else{
                                allocateEmployeeShares.push(args.empShareDetails[i])
                            }
                        }
                        if(allocateEmployeeShares.length>0){
                            // form the inputs to allocate shares for the employees
                            let empTimeZone=await commonLib.func.getEmployeeTimeZone(logInEmpId, organizationDbConnection,1);
                            let inputParams = allocateEmployeeShares.map(field=>({
                                Employee_Id: field.employeeId,
                                Allocated_Shares:field.allocatedShare,
                                Allocated_Date:field.allocatedDate,
                                Visibility:field.visibility,
                                Added_By: logInEmpId,
                                Added_On: empTimeZone
                            }));
                            // inserted record in employee share details
                            return(
                                organizationDbConnection
                                .insert(inputParams)
                                .from(ehrTables.employeeShareDetails)
                                .then(async() => {
                                    // form system log params
                                    let systemLogParams = {
                                        action: systemLogs.roleAdd,
                                        userIp: ipAddress,
                                        employeeId: logInEmpId,
                                        formName: formName.esop,
                                        trackingColumn: 'Bulk import allocate employee shares',
                                        organizationDbConnection: organizationDbConnection,
                                        uniqueId: ''
                                    }
                                    // call function createSystemLogActivities() to update system log activities
                                    await commonLib.func.createSystemLogActivities(systemLogParams);
                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                    return { errorCode:'',message:'Employee shares allocated successfully.',errorResponse:(Object.keys(errorResponse).length>0)?(JSON.stringify(errorResponse)):''};
                                })
                                .catch(function (catchError) {
                                    console.log('Error in bulkImportESOP function .catch block',catchError);
                                    errResult = commonLib.func.getError(catchError, 'BES0104');
                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                    throw new ApolloError(errResult.message,errResult.code);
                                })
                            );
                        }
                        else{
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            errResult = commonLib.func.getError('', 'BES0004');
                            return { errorCode:errResult.code,message:errResult.message,errorResponse:(Object.keys(errorResponse).length>0)?(JSON.stringify(errorResponse)):''};
                        }
                    }
                    else{
                        throw '_DB0109';
                    }
                }
                else if (checkRights === false) {
                    throw '_DB0101';
                } else {
                    throw (checkRights);
                }
            }
            catch(mainCatchError)
            {
                console.log('Error in bulkImportESOP function main catch block',mainCatchError);
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                errResult = commonLib.func.getError(mainCatchError, 'BES0012');
                // return error response
                throw new ApolloError(errResult.message,errResult.code);
            }
        }
    }
};

exports.resolvers = resolvers;

// function to validate the input fields
async function validateInputFields(args, dateOfJoins, resignationDates){
    try{
        let validationErrorArray=[];
        let dateOfJoin = dateOfJoins.find((el)=>el.Employee_Id === args.employeeId)
        let resignationDate = resignationDates.find((el)=>el.Employee_Id === args.employeeId)
        resignationDate = resignationDate !== undefined ? resignationDate.Resignation_Date: null
        dateOfJoin = dateOfJoin.Date_Of_Join

        // validate allocated share
        if(args.allocatedShare && (args.allocatedShare>0)){
            let validate = commonInputValidation.decimalValidation(args.allocatedShare);
            if (!validate) {
                validationErrorArray.push('Invalid allocated share');
            }
        }
        else{
            validationErrorArray.push('Invalid allocated share');
        }
        // validate allocated date
        if(!args.allocatedDate || new Date(args.allocatedDate) == "Invalid Date" || !moment(args.allocatedDate, 'YYYY-MM-DD').isValid){
            validationErrorArray.push('Invalid allocated date');
        }

        //date of join validate
        if(!moment(args.allocatedDate, 'YYYY-MM-DD').isSameOrAfter(dateOfJoin)){
            validationErrorArray.push(`Allocated date is earlier than the employee's date of join`);
        }

        //resignation date validate
        if(resignationDate){
            if(!moment(args.allocatedDate, 'YYYY-MM-DD').isSameOrBefore(resignationDate)){
                validationErrorArray.push(`Allocated date is after the employee's resignation date`);
            }
        }

        // validate visibility
        if(args.visibility){
            if((args.visibility!=='Self') && (args.visibility!=='Super Admins')){
                validationErrorArray.push('Invalid data visibility');
            }
        }
        else{
            validationErrorArray.push('Invalid data visibility');
        }
        // validate employeeId
        if(args.employeeId){
            let validate = commonInputValidation.numberValidation(args.employeeId);
            if (!validate) {
                validationErrorArray.push('Invalid employeeId');
            }
        }
        else{
            validationErrorArray.push('Invalid employeeId');
        }
        return validationErrorArray;
    }
    catch(error){
        console.log('Error in validateInputFields function main catch block.',error);
        throw 'BES0115';
    }
};
