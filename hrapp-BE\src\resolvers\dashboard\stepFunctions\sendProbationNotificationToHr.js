'use strict';
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make database connection
const knex = require('knex');
//Require moment package
const moment=require('moment');
//Require common function
const { getProbationEmployeesList }=require('../../../../common/remindersCommonFunctions');
const { getBatchNotificationDetails,updateOrgBatchNotificationStatus,sendEmailToHr }=require('../../../../common/batchNotificationCommonFunctions');
//Require file to access constant values
const { awsSesTemplates,defaultValues } = require('../../../../common/appconstants');

//Function to iterate the organization codes and get the probation employee for the org code based on the configuration and send the probation reminder to respective employees
module.exports.sendProbationNotificationToHr = async (parent, args, context, info) => {
    console.log("Inside sendProbationNotificationToHr() function.");
    let appManagerDbConnection;
    let organizationDbConnection;
    let response;
    try{
        //Make database connection
        appManagerDbConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
        appManagerDbConnection=knex(appManagerDbConnection.AppManagerDb);
        let orgCodeList = await getBatchNotificationDetails(appManagerDbConnection,'probEndDate','Hr_Email_Status',defaultValues.openEmailNotificationStatus);
        
        if(orgCodeList.length > 0){
            let currentDate = moment().tz('Asia/Kolkata').format('YYYY-MM-DD');
            let currentDateMinusTwo = moment(currentDate).subtract(2, 'days').format("YYYY-MM-DD");
            let updateParams = {};
            //Iterate for all the instances
            for(let i=0;i<orgCodeList.length;i++){
                updateParams = {};
                let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgCodeList[i]);
                
                //Make database connection
                let connection= await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCodeList[i].Org_Code,0,additionalHeaders);
                organizationDbConnection=knex(connection.OrganizationDb);
                //Get the probation employee details
                let probationEmployees = await getProbationEmployeesList(organizationDbConnection,0,0,1,currentDateMinusTwo);
                if(probationEmployees.length > 0){
                    let emailInputs = {
                        orgCode: orgCodeList[i].Org_Code,
                        employeesList: probationEmployees,
                        htmlTableKeyName: 'probationemailtohr',
                        statusKey: 'Hr_Email_Status',
                        notificationForKey: 'probEndDate',
                        commonNotificationTemplate: awsSesTemplates.commonNotification,
                        emailSubject:'Employee confirmation action outstanding from managers due on '+probationEmployees[0].displayProbationDate,
                        emailContent: 'Employment confirmation is due for the following employees'
                    };
                    let {finalResponse} = await sendEmailToHr(organizationDbConnection,appManagerDbConnection,emailInputs);
                    response = finalResponse;
                    //Destroy database connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                }else{
                    console.log('Probation employee does not exist for the current date  for the org code - ',orgCodeList[i].Org_Code);
                    await updateOrgBatchNotificationStatus(appManagerDbConnection,orgCodeList[i].Org_Code,'Hr_Email_Status','Success','probEndDate');
                    //Destroy database connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    response={
                        nextStep:'End',
                        message:'Email notification is not send as the probation employees does not exist.'
                    };
                }
                //If the last organization is processed
                if(i===orgCodeList.length-1){
                    //Destroy database connection
                    appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                }
            }
        }else{
            console.log('Input org code list is empty');
            //Destroy database connection
            appManagerDbConnection ? appManagerDbConnection.destroy() : null;
            response={
                nextStep:'End',
                message:'No records found so quit the process.'
            }
        }
        return response;
    }catch(mainCatchError) {
        console.log('Error in the sendProbationNotificationToHr() function main catch block. ',mainCatchError);
        
        //Destroy database connection
        appManagerDbConnection ? appManagerDbConnection.destroy() : null;
        organizationDbConnection ? organizationDbConnection.destroy() : null;

        response={
            nextStep:'End',
            message:'Error from sendProbationNotificationToHr.So quit the process.'
        }
        return response;
    }
};