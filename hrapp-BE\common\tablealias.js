/** Alias name for ehr tables */
module.exports.ehrTables = {
    empUser : 'emp_user',
    ipAddressWhitelist : 'ip_address_whitelisting',
    whitelistedIps: 'whitelisted_ip_addresses',
    location : 'location',
    orgDetails : 'org_details',
    taxconfiguration : 'tax_configuration',
    employeeMonitorSettings:'employee_monitor_settings',
    domainSettings:"domain_settings",
    hrappRegisterUser :"hrapp_registeruser",
    hrappLogin :"hrapp_login",
    teamMembers:'em_members',
    location:'location',
    department:'department',
    empJob: 'emp_job',
    resignation: 'emp_resignation',
    leavetype: 'leave_types',
    attendanceSettings: 'attendance_settings',
    attendance: 'emp_attendance',
    empLeaves: 'emp_leaves',
    empPersonalInfo: 'emp_personal_info',
    billingRate:'billing_rate',
    hrappPlanForm:'hrapp_plan_form',
    leaveClosureConfiguration: 'leave_closure_configuration',
    salaryPayslip: 'salary_payslip',
    payrollRoundOffSettings : 'payroll_round_off_settings',
    monthlyLeaveBalance: 'monthly_leave_balance',
    modules:'modules',
    customizationForms :'customization_forms',
    forms:'forms',
    designation: 'designation',
    empActivityDetails: 'employee_activity_details',
    empActivityScreenshots: 'employee_activity_screenshots',
    empAccessRights:'emp_accessrights',
    ehrRoles: 'ehr_roles',
    announcements: 'announcements',
    alertSettings: 'alert_settings',
    alertTypes: 'alert_types',
    awards: 'awards',
    awardTypes: 'award_types',
    empType: 'employee_type',
    workSchedule: 'work_schedule',
    shiftEmpMapping: 'shift_emp_mapping',
    empShiftType: 'emp_shift_type',
    contractEmployeeTdsConfiguration:'contract_employee_tds_configuration',
    contractorTaxRates:'contractor_tax_rates',
    contractorTaxSection:'contractor_tax_section',
    orgDetails : 'org_details',
    cusEmpGroup: 'custom_employee_group',
    cusEmpGroupForms: 'custom_employee_group_module_forms',
    cusEmpGroupEmp: 'custom_employee_group_employees',
    empPersonal : "emp_personal_info",
    resignation: "emp_resignation",
    custGroupSettings : "custom_group_settings",
    custGroupEmployees : "custom_group_employees",
    weekdays : "weekdays",
    rosterSettings:'roster_management_settings',
    compensatoryOff:'compensatory_off',
    empAttendance:'emp_attendance',
    overtime:'overtime_claims',
    overtimeSettings:'overtime_settings',
    empSalaryConfiguration: 'employee_salary_configuration',
    overtimeSlab:'overtime_slabs',
    approvalManagement: 'approval_management',
    approvalManagementEmployees: 'approval_manager_list',
    incomeTaxDeclarationSettings: 'incometax_declaration_settings',
    performanceManagementSettings:'performance_management_settings',
    performanceRatingDescription:'performance_rating_description',
    performanceGoalAchievement:'performance_goal_achievement',
    assessmentCycleGoalRatings:'assessment_cycle_goal_ratings',
    performanceGoalsLibrary:'performance_goals_library',
    skillLevels:'skill_levels',
    compensatoryOffBalance: 'compensatory_off_balance',
    shiftType: 'shift_type',//shift-type configuration table for the shift allowance configuration
    shiftTypeConfiguration: 'shift_type_configuration',//shift allowance - shift-type configuration table for the custom group coverage
    empShift: 'emp_shift',//shift allowance table
    auditEmpshift: 'audit_empshift',// history table for shift allowance
    redeemRewardsDetails: 'redeem_rewards_details',
    ehrForms:'ehr_forms',
    currency:'currency',
    employeeShareDetails:'employee_share_details',
    employeeShareVestHistory:'employee_share_vest_history',
    benefitsConfiguration:'benefits_configuration',
    state:"state",
    serviceProvider:"service_provider",
    salaryDeduction:'salary_deduction',
    hourlywagesPayslip:'hourlywages_payslip',
    timezone:'timezone',
    hrGroup: 'hr_group',
    taUserTask:'ta_user_task',
    customGroupAssociated:"custom_group_associated_forms",
    attendanceGeoFacialConfiguration:"AttendanceGeoFacial_Configuration",
    leaveSettings:"leave_settings",
    fileAndWebEventSummary:'file_and_web_event_summary',
    webEventSummary:'web_event_summary',
    assetManagement: 'asset_management',
    reportSettings:'report_settings',
    reportHistory:'report_history',
    empEligibleLeave:'emp_eligible_leave',
    preApprovalSettings: 'pre_approval_settings',
    attendanceGeneralConfiguration:'attendance_general_configuration',
    country:'country',
    state:'state',
    city:'city',
    shiftRotation: 'shift_rotation',
    shiftRotationSchedule: 'shift_rotation_by_schedule',
    employeeShiftSwap:'employee_shift_swap',
    reimbursementSettings: 'reimbursement_settings',
    translatingLanguage: 'translating_language',
    uiFeatureToggleSettings: 'ui_feature_toggle_settings',
    externalApiCredentials: 'external_api_credentials',
    employeeSettings: 'employee_settings'
};

module.exports.appManagerTables = {
    orgRateChoice:'org_rate_choice',
    activityTrackerUpdates:'activity_tracker_release_updates',
    pmsEmailEventsMaster:'pms_email_events_master',
    hrappRegisteruser: 'hrapp_registeruser',
    billingRate:'billing_rate',
    hrappPlanDetails:'hrapp_plan_details',
    helpDetails:'help_details',
    batchEmailNotification: 'batch_email_notification'
}