# defining custom data type
scalar Date 

type Query{
  employeeDirectoryList(designationId:[Int]!,departmentId:[Int]!,empTypeId:[Int]!,locationId:[Int]!):employeeDirectoryListResponse!
  listWhitelistedIp:listWhitelistedIpResponse!
  getEmployeeCount: getEmployeeCountResponse!
  getDomainDetails: getDomainDetailsResponse!
  getOrganizationUserDetails:getOrganizationUserDetailsResponse!
  listModulesAndForms (languageCode: String): listModulesAndFormsResponse!
  checkGeoEnforceAndWorkPlace:checkGeoEnforceAndWorkPlaceResponse!
  listLeavesAndShortTimeOff:listLeavesAndShortTimeOffResponse!
  getUtilization: getUtilizationResponse!
  empLeaveHistory(employeeId:Int,source:String): empLeaveHistoryResponse!
  getEmployeeDetails: getEmployeeDetailsResponse!
  myTeamUpdates: myTeamUpdatesResponse!
  getReminders: getRemindersResponse!
  getWorkPlaceCount: getWorkPlaceCountResponse!
  getAttendanceCount: getAttendanceCountResponse!
  getLeaveDistribution: getLeaveDistributionResponse!
  requestRights: requestRightsResponse!
  getLateAttendanceStatistics: getLateAttendanceStatisticsResponse!
  listHolidaysInDashboard:listHolidaysInDashboardResponse!
  listAnnouncements:listAnnouncementsResponse!
  listNotificationsInDashboard(myApprovals: Boolean):listNotificationsInDashboardResponse!
  listPayrollActions:listPayrollActionsResponse!
  listEmployeeActions:listEmployeeActionsResponse!
  listEmployeeDetails(designationId:[Int]!,departmentId:[Int]!,empTypeId:[Int]!,locationId:[Int]!,workScheduleId:[Int]!):listEmployeeDetailsResponse!
  getStaticContractTaxDetails:getStaticContractTaxDetailsResponse!
  listShiftScheduling(sortField:Int,sortOrder:String,searchString:String,employeeName:String,shiftStartDate:Date,shiftEndDate:Date,shiftTypeId:[Int],filterMonth:String!) : listShiftSchedulingResponse!
  listShiftRoasterEmployees(department:[Int], designation:[Int], location:[Int], customGroupId:[Int], employeeType:[Int],filterMonth:String): listShiftRoasterEmployeesResponse!
  listCustomEmployeeGroups(formName: String!, preApprovalId:Int) : listCustomEmployeeGroupsResponse!
  listShiftType(sortField:Int,sortOrder:String,searchString:String,shiftName:String,minCount:Int,maxCount:Int,isDropDown:Int,employeeId: Int,swapDate: String) : listShiftTypeResponse!
  listWorkSchedule(formName: String) : listWorkScheduleResponse!
  retrieveMonthlyCalenderShiftDetails(employeeId:Int!,shiftMonth:String!):retrieveMonthlyCalenderShiftDetailsResponse!
  getShiftSummary(shiftMonth:String!):getShiftSummaryResponse!
  retrieveWeeklyCalenderShiftDetails(salaryStartDate:String!,salaryEndDate:String!):retrieveWeeklyCalenderShiftDetailsResponse!
  listOvertimeDetails(filterMonth:Int!,filterYear:Int!,status:[String],startDate:Date,endDate:Date):listOvertimeDetailsResponse!
  getOTConfigurationDetails:getOTConfigurationDetailsResponse!
  listOvertimeEmployees(designationId:[Int],departmentId:[Int],empTypeId:[Int],locationId:[Int],workScheduleId:[Int]):listOvertimeEmployeesResponse!
  overtimeWageCalculation(employeeId:Int!,overtimeClaimId:Int!,otStartTime:Date!,otEndTime:Date!,action:String!):overtimeWageCalculationResponse!
  getOvertimePrerequisites(employeeId:Int!,otStartTime:Date!,shiftDate:Date):getOvertimePrerequisitesResponse!
  retrievePerformanceSettings:retrievePerformanceSettingsResponse!
  listGoals:listGoalsResponse!
  listEmployeeGoalsAndRating(year:Int!,month:Int!):listEmployeeGoalsAndRatingResponse!
  listPmsEmployees(departmentId:[Int], designationId:[Int], locationId:[Int], customGroupId:[Int], empTypeId:[Int],month:Int!,year:Int!): listPmsEmployeesResponse!
  listPmsReviewers(departmentId:[Int], designationId:[Int], locationId:[Int], empTypeId:[Int]): listPmsReviewersResponse!
  retrieveEmployeeGoalsAndRating(employeeId:Int!,year:Int!,month:Int!,isAdminView:Int!):retrieveEmployeeGoalsAndRatingResponse!
  retrieveRatings:retrieveRatingsResponse!
  listEmployeesAchievement(startYear:Int!,endYear:Int!,startMonth:Int!,endMonth:Int!):listEmployeesAchievementResponse!
  listPerformanceYear:listPerformanceYearResponse!
  retrieveTeamRatings(year:Int!,month:Int!):retrieveTeamRatingsResponse!
  retrieveTeamGoalsRatingsCount(month:Int!,year:Int!):retrieveTeamGoalsRatingsCountResponse!
  retrieveEmployeeYearlyRating(year:Int!,month:Int!,employeeId:Int!):retrieveEmployeeYearlyRatingResponse!
  retrieveTeamAverageMonthlyRatings(month:Int!,year:Int!):retrieveTeamAverageMonthlyRatingsResponse!
  calculateOvertimeCompOffBalance(employeeId:Int!,otStartTime:Date!,otEndTime:Date!):overtimeCompOffBalanceResponse!
  listShiftAllowanceShiftType(employeeId:Int!,otStartTime:Date!,otEndTime:Date!):listShiftAllowanceShiftTypeResponse!
  calculateOvertimeShiftAllowance(employeeId:Int!,otStartTime:Date!,otEndTime:Date!,workScheduleId:Int!):overtimeShiftAllowanceResponse!
  getRedeemRewardsDetails:getRedeemRewardsDetailsResponse!
  getOrganizationSubscribedPlan:getOrganizationSubscribedPlanResponse!
  retrieveCurrencyDetails:retrieveCurrencyDetailsResponse!
  retrieveCurrencyAndShareValue:retrieveCurrencyAndShareValueResponse!
  listAllEmployeeDetails(designationId: [Int],departmentId: [Int],empTypeId: [Int],workScheduleId: [Int],locationId: [Int], formName: String, onlyActiveEmployees: Int):listAllEmployeeDetailsResponse!
  listEmployeeTotalShares:listEmployeeTotalSharesResponse!
  listEmployeeAllocatedShares(employeeId: Int!,source:String!): listEmployeeAllocatedSharesResponse!
  retrieveShareVestHistory(allocatedShareId:Int!,source:String!):retrieveShareVestHistoryResponse!
  listStates(countryCode:String!):listStatesResponse!
  listHelpUrlAndText(moduleId:Int!,applicableForEmployee:String!,applicableForManager:String!,applicableForAdmin:String!):listHelpUrlAndTextResponse!
}

type Mutation {
  whitelistIPAddress(
    locationId : Int!,
    ipAddresses : [String]!
    description : String,
    isEdit : Int
  ):AddResponse!

  deletewhitelistedIPAddress(
    locationId : Int!
  ) : deleteResponse!

  updateShiftScheduling(
    shiftSchedulingId : Int,
    shiftTypeId : Int!,
    employeeId : [Int]!,
    shiftStartDate : Date!,
    shiftEndDate : Date!,
    rotationId: Int
    weekOff : Int
    weekOffDays: Int
    plusDays: Int
  ) : commonResponse!

  bulkImport(
    data : [BulkImportData]
  ) : bulkImportResponse!

  deleteShiftScheduling(
    shiftSchedulingId : [Int]!
  ): deleteShiftSchedulingResponse!

  updateShiftType(
    shiftTypeId: Int,
    workScheduleId: Int!,
    minimumEmployeeCount: Int!,
    maximumEmployeeCount: Int!,
    holidayOverride: Boolean!,
    comments: String,
    colourCode: String
  ): commonResponse!
  
  deleteShiftType(
    shiftTypeIds: [Int!]!
  ): commonResponse!

  updateShiftStatus(shiftTypeId: Int!, status:String!): updateShiftStatusResponse!

  bulkImportWeekOff(
    weekOffArray: [weekOffDetails]!
  ):bulkImportWeekOffResponse!
  deleteOvertimeDetails(overtimeClaimId:[Int]!):deleteOvertimeDetailsResponse!
  
  addOvertimeDetails(
    employeeId:Int!,
    otStartTime: Date!,
    otEndTime: Date!,
    status:String!,
    reason:String,
    shiftAllowanceAppliedWorkScheduleId: Int!,
    filterMonth:Int!,
    filterYear:Int!
  ):addOvertimeDetailsResponse!

  updateOvertimeDetails(
    employeeId:Int!,
    overtimeClaimId:Int!,
    otStartTime: Date!,
    otEndTime: Date!,
    status:String!,
    reason:String,
    shiftAllowanceAppliedWorkScheduleId: Int!,
    filterMonth:Int!,
    filterYear:Int!
  ):updateOvertimeDetailsResponse!

  updateOvertimeDetailsStatus(
    newApprovalStatus:String!,
    overtimeClaimIds: [Int]!, 
    oneApprovalRecordComment: String,
    filterMonth:Int!,
    filterYear:Int!
  ): commonResponseTwo!
  updatePerformanceSettings(performanceSettings:performanceSettingsInput!,ratings:[ratingsInput]!):updatePerformanceSettingsResponse!
  addGoalsToLibrary(goalsList:[String]!):addGoalsToLibraryResponse!
  addEmployeeGoalsAndAchievement(employeeIds:[Int!], reviewerId:Int!, goalIds:[Int!],month:Int!, year:Int!): commonResponseTwo!
  publishGoals(performanceAssessmentId:[Int]!,month:Int!, year:Int!): commonResponseTwo!
  updateEmployeeGoalsAndAchievement(employeeId:Int!,reviewerId:Int!,goalIdArray:[Int]!,goalsAndRating:[goalsAndRatingInput],overallRating:Int!,comments:String!,month:Int!,year:Int!):updatePerformanceSettingsResponse!
  publishRatings(performanceAssessmentId:[Int]!,month:Int!, year:Int!): commonResponseTwo!
  updateCurrencyAndShareDetails(currencyId:Int!,shareValue:Float!):commonResponseTwo!
  addEmployeesForAllocatingShares(employeeIds:[Int]!,allocatedShare:Float!,allocatedDate:Date!,visibility:String!):commonResponseTwo!
  updateEmployeeShareStatus(employeeId:Int!,shareStatus:String!):commonResponseTwo!
  updateEmployeeAllocatedShares(allocatedShareId:Int!,allocatedShare:Float!,allocatedDate:Date!):commonResponseTwo!
  vestEmployeeShares(allocatedShareId:Int!,vestedShare:Float!,vestedDate:Date!):commonResponseTwo!
  bulkImportESOP(empShareDetails:[addEmployeeShares]!):bulkImportESOPResponse!
  updateOvertimeClaimCompletedStatus(overtimeClaimIds: [Int]!, oneApprovalRecordComment: String,filterMonth:Int!,filterYear:Int!):commonResponseTwo!
  updateGoalsToLibrary(goalId:Int!,goalDescription:String!):addGoalsToLibraryResponse!
  deleteGoalsFromLibrary(goalId:Int!):addGoalsToLibraryResponse!
  updateEmpLanguagePreference(
    employeeId: Int!,
    languagePreference: String!
  ): updateEmpLanguagePreferenceResponse!
}

input addEmployeeShares{
  employeeId:Int!,
  allocatedShare:Float!,
  allocatedDate:Date!,
  visibility:String!
}

input goalsAndRatingInput{
  goalId:Int,
  rating:Int
}

input performanceSettingsInput{
  Performance_Management_Mode: String,
  Maximum_Rating: Int,
  GoalSettings_ReminderOne:Int,
  GoalSettings_ReminderTwo:Int,
  RatingUpdate_ReminderOne:Int,
  RatingUpdate_ReminderTwo:Int,
  Goal_Email_Subject: String,
  Goal_Email_Content: String,
  Rating_Email_Subject: String,
  Rating_Email_Content: String,
  Enable_Email_Notification: String,
  Present_Only_Team_Members_To_Managers: String
}

input ratingsInput{
  Rating_Id:Int,
  Rating_Description:String
}

type getOvertimePrerequisitesResponse{
  errorCode:String,
  message:String,
  otPreRequisites: String
}
type commonResponseTwo{
  errorCode: String,
  message: String
}

input approvalRecordDetails{
  overtimeClaimId : Int!,
  employeeId : Int!,
  otStartDateTime: Date!,
  otEndDateTime: Date!,
  oldApprovalStatus : String!,
  workScheduleId: Int!,
  lastApprovedBy : Int,
  comment: String
}

type updateOvertimeDetailsResponse{
  errorCode: String,
  message: String
}

type overtimeWageCalculationResponse{
  totalHours:Float,
  overtimeWages: Float,
  totalHoursInFormat: String
}
type deleteOvertimeDetailsResponse{
  errorCode: String,
  message: String
}
type employeeDetails{
  employee_id: Int,
  photo_path: String,
  designation_name: String,
  department_name: String,
  user_defined_empid: String,
  employee_name: String,
  manager_id: Int,
  doj: String,
  exitDate: String
}
type listOvertimeEmployeesResponse{
  errorCode: String,
  message: String,
  employeeDetails: [employeeDetails]
}
type addOvertimeDetailsResponse{
  errorCode: String,
  message: String
}
type overtimeSettings{
  Overtime_Part_Of_Payroll: Int,
  Overtime_Payment_Method: String,
  Overtime_Flat_OR_Slab_Rate: String,
  Comp_Off_Applicable_For_Overtime: Int!,
  Shift_Allowance_Applicable_For_Overtime: Int!
}
type configurationData{
  minAndMaxYears: [Int],
  overtimeSettings: overtimeSettings
}
type getOTConfigurationDetailsResponse{
  errorCode:String,
  message:String,
  configurationData : configurationData
}
type listOvertimeDetailsResponse{
  errorCode:String,
  message:String,
  overtimeDetails:String,
  salaryStartDate: Date,
  salaryEndDate: Date
}
type retrieveWeeklyCalenderShiftDetailsResponse{
  errorCode: String,
  message: String,
  shiftDetails:String
}
type getShiftSummaryResponse{
  errorCode: String,
  message: String,
  activeShifts: Int,
  shiftScheduledEmployees: String,
  weekOffAssignedEmployees: String,
  salaryStartDate: String,
  salaryEndDate: String
}

type retrieveMonthlyCalenderShiftDetailsResponse{
  errorCode: String,
  message: String,
  shiftCalendarView: String
}

type bulkImportWeekOffResponse{
  success: Boolean,
  errorCode: String,
  message: String
}

input weekOffDetails{
  employeeId:Int!,
  shiftTypeId:Int!,
  shiftDate:Date!
}

type getUtilizationResponse {
  errorCode: String
  message: String
  utilization: String
}

type getRemindersResponse{
  errorCode: String
  message: String
  reminders: remindersType
}

type getWorkPlaceCountResponse{
  errorCode: String
  message: String
  workPlaceCount: workPlaceType
}

type workPlaceType {
  office: Int
  field: Int
  workFromHome: Int
  others: Int
}

type getAttendanceCountResponse {
  errorCode: String
  message: String
  onTimeArrivals: Int
  lateArrivals: Int
}

type getLeaveDistributionResponse {
  errorCode: String
  message: String
  totalLeaveCount: Int
  leaveDistribution: [leaveDistributionRes]
}

type leaveDistributionRes {
  leaveName: String
  leaveCount: Int
  currentDateLeaveEmployeeDetails: [employeeIdAndNameResponse]
}

type employeeIdAndNameResponse{
  employee_name: String
  user_defined_employee_id: String
}

type remindersType{
  birthday: String
  workAnniversary: String
  awards: String,
  leaveClosure:String
}

type requestRightsResponse{
  errorCode: String
  message: String
}

type myTeamUpdatesResponse {
  errorCode: String
  message: String
  myTeamUpdates: myTeamUpdatesTypes
}

type myTeamUpdatesTypes {
  probation: String
  newJoining: String
  offboarding: String
}

type getEmployeeDetailsResponse{
  errorCode: String
  message: String
  employeeDetails: String
}
type getLateAttendanceStatisticsResponse{
  errorCode: String
  message: String
  lateAttendanceStatistics: String
}

type empLeaveHistoryResponse {
  errorCode: String
  message: String
  leaveHistory: String
}

input screenshotsRequest {
  screenshotName:String!,
  screenshotBase64:String!
}

type listShiftTypeResponse{
  success : Boolean
  errorCode : String
  message : String
  shiftType : [shiftTypeResponse]
  rosterSettings: rosterSettings
}

type shiftTypeResponse{
  Shift_Id: Int
  Shift_Name: String
  WorkSchedule_Id: Int
  Minimum_Employee_Count: Int
  Maximum_Employee_Count: Int
  Holiday_Override: Boolean
  Comments: String
  Colour_Code: String
  Status: String
  Added_By: String
  Added_On: Date
  Updated_By: String
  Updated_On: Date
  loginEmployeeIdShift:String
}
type updateShiftStatusResponse{
  success : Boolean
  errorCode : String
  message : String
}

type updateEmpLanguagePreferenceResponse{
  success : Boolean
  errorCode : String
  message : String
}
type listWorkScheduleResponse{
  success : Boolean
  errorCode : String
  message : String
  workSchedule : [workScheduleResponse]
}

type workScheduleResponse{
  WorkSchedule_Id:Int,
  WorkSchedule_Name:String,
  Target_Effort_In_Hours_Per_Week:Float,
  Zone_Id:Int,
  Time_Zone:String,
  TimeZone_Id:String,
  Regular_Hours_Per_Day:Float,
  currentDateBasedOnTimezone:String
}

input BulkImportData {
  shiftSchedulingId : Int,
  shiftTypeId : Int,
  employeeId : [Int],
  shiftStartDate : Date,
  shiftEndDate : Date
}

type deleteResponse{
  errorCode : String,
  message : String
}

type AddResponse {
  errorCode : String,
  message : String,
  validationError : String
}

type listWhitelistedIpResponse{
  errorCode     : String,
  message       : String,
  whiteListedIpAddress : String,
  ipAddressResction : Int,
  allConfiguredIPAddressed : [String]
}

type listShiftSchedulingResponse{
  success : String
  errorCode : String
  message : String
  shiftSchedule : [shiftScheduleResponse]
}

type rosterSettings{
  Dynamic_Week_Off: Int
  Overlap_Shift_Schedule: Int
}
type shiftScheduleResponse{
  Shift_Schedule_Id : Int
  Employee_Id: Int
  User_Defined_EmpId:String
  Photo_Path: String
  Employee_Name : String
  Shift_Type_Id : Int
  Shift_Type_Name : String
  Shift_Start_Date: Date
  Shift_End_Date: Date
  Week_Off: String
  Added_By: String
  Added_On: Date
  Updated_By: String
  Updated_On: Date
}

type commonResponse{
  success : Boolean
  errorCode : String
  message : String
}

type bulkImportResponse {
  success : Boolean
  errorCode : String
  message : String
}

type deleteShiftSchedulingResponse {
  success : Boolean
  errorCode : String
  message : String
}

type listShiftRoasterEmployeesResponse{
  success : Boolean
  errorCode : String
  message : String
  employees : [EmployeeIdAndName]
}

type EmployeeIdAndName{
  employee_id: Int
  user_defined_empid : String
  employee_name: String
  photo_path: String
  designation_name: String
  department_name: String
  shift_count: String
}

type listCustomEmployeeGroupsResponse {
  success : Boolean
  errorCode : String
  message : String
  customGroups : [CustomGroups]
}

type CustomGroups {
  Custom_Group_Id: Int
  Custom_Group_Name: String
}

type employeeDirectoryListResponse{
  errorCode     : String,
  message       : String,
  employeesList : [employeeslist]
}

type employeeslist {
  user_defined_empid : String,
  first_name       : String,
  middle_name      : String,
  last_name        : String,
  employee_name    : String,
  designation_name : String,
  department_name  : String,
  location_name    : String,
  contact_number   : String,
  country_code     : String,
  photo_path       : String,
  blood_group      : String,
  military_service : String,
  gender           : String,
  employee_type    : String,
  date_of_join     : Date,
  manager_name     : String,
  nick_name        : String,
  work_email       : String,
  is_manager       : Int
}

type getEmployeeCountResponse{
  errorCode : String,
  message : String,
  totalEmployeesCount : Int,
  activeEmployeesCount : Int
}

type getDomainDetailsResponse{
  errorCode : String,
  message : String,
  domainDetails : domainDetails,
  licensingDetails : String
}

type domainDetails{
  supportEmail: String,
  copyRight: Int,
  supportLink: String,
  termsLink: String,
  privacyPolicyLink: String,
  chatBot: Int,
  paymentPortalSiteName: String
}

type getOrganizationUserDetailsResponse{
  errorCode: String,
  message: String,
  organizationName: String,
  organizationAddress: String,
  entomoSyncType: String,
  employeeSettings: employeeSettings,
  serviceProviderName: String,
  serviceProviderLocation: String,
  orgDateFormat: String,
  productIconPath: String, 
  assessmentYear: String,
  paycycle: String,
  employeeId:Int,
  monitoringType:String,
  privacyMode:Int,
  disableLogout:String,
  camuBaseUrl:String,
  payRollIntegrationUrl: String,
  advancePayroll:String,
  restrictEmpAccessForManager: Int,
  closureMonthJson: [String],
  autoUpdateEffectiveDateForJobDetails: String,
  employeeEdit: Int
  fieldForce: Int
  allowConcatenation: Boolean
  userDetails:userDetails,
  organization:locationData,
  serviceProvider: locationData
  uiFeatureSettings: [uiFeatureSettings]
}

type employeeSettings{
  Enable_Workflow_Profile: String,
  Enable_Workflow_Team_Summary: String,
}

type uiFeatureSettings{
  Element_Name: String,
  Is_Enabled: Int,
}

type locationData {
  street1: String,
  street2: String,
  city: String,
  state: String,
  country: String,
  pincode: String
}

type userDetails  {
  employeeId: Int,
  userDefinedEmployeeId: String,
  employeeFullName: String,
  employeeFirstName: String,
  employeeLastName: String,
  languagePreference: String,
  employeePhotoPath: String,
  employeeEmail:String
  designationName: String
  rolesId: Int
}

type listModulesAndFormsResponse {
  errorCode: String,
  message:String,
  modulesAndForms: modulesAndForms
}

type modulesAndForms {
  moduleList: [menuListDetails],
  formAccessList:String
  formIdAccessList:String
}

type menuListDetails{
  moduleId: Int,
  moduleName:String,
  translatedModuleName:String,
  formList: [FormDetails]
}

type FormDetails {
  formId:Int,
  formName:String,
  customFormName:String,
  url:String
}
type checkGeoEnforceAndWorkPlaceResponse{
  errorCode:String,
  message:String,
  isGeoEnable:Boolean,
  isWorkPlaceEnable:Int,
  workPlaceDetails: [workPlaceDetails]
}

type workPlaceDetails{
  workPlaceId:Int
  workPlace: String
  imageUrl:String
  autoAttendance:String
}

type listAnnouncementsResponse {
  errorCode: String,
  message:String,
  announcementDetails: [announcementDetails],
  announcementCount: Int
}

type announcementDetails {
  announcementId:Int,
  title:String,
  announcementType:String,
  flashContent:String,
  announcementText:String,
  embedUrl:String
}

type listLeavesAndShortTimeOffResponse{
  errorCode:String,
  message:String,
  leaveDetails: String,
  shortTimeOffDetails: String,
  compensatoryOffDetails: String
}

type listHolidaysInDashboardResponse{
  errorCode:String,
  message:String,
  holidayDetails: String
}

type listPayrollActionsResponse{
  errorCode:String,
  message:String,
  payrollActionsList:String
}

type listEmployeeActionsResponse{
  errorCode:String,
  message:String,
  employeeActionsList:String
}

type listNotificationsInDashboardResponse{
  errorCode:String,
  message:String,
  notificationList:String,
  notificationCount:Int,
  leaveWorkflowEnabled:String
  reimbursementWorkflowEnabled:String
}

type listEmployeeDetailsResponse{
  errorCode:String,
  message:String,
  employeeDetailsList:[employeeDetailsList]
}

type employeeDetailsList {
    employee_name: String,
    employee_id: Int,
    photo_path: String,
    designation_name: String,
    department_name: String,
    location_name: String,
    country_code: String,
    employee_type: String,
    manager_id: Int,
    is_manager: Int,
    user_defined_empid:String,
    emp_status: String,
    benefits_applicable: Int,
    contact_number: String,
    emp_email: String,
    resident_type:String,
    pan:String,
    eligible_contractor_tds:Int,
    tax_section_id:Int,
    added_by_emp_name: String,
    added_by_emp_id:String,
    added_on: String,
    updated_by_emp_id: String,
    updated_by_emp_name:String,
    updated_on: String,
    eligible_for_pt : Int,
    eligible_for_pension : Int,
    salary_config_added_by_emp_name : String,
    salary_config_added_by_emp_id : String,
    salary_config_added_on : String,
    salary_config_updated_by_emp_name : String,
    salary_config_updated_by_emp_id : String,
    salary_config_updated_on : String,
    allow_user_signin: Int,
    allow_mobile_signin: Int,
    contact_mobile: String,
    contact_mobile_country_code: String,
    invitation_status: String,
    invited_time: String,
    invited_message: String,
}

type getStaticContractTaxDetailsResponse{
  errorCode:String,
  message:String,
  contractTaxDetails:[contractTaxDetails]
}

type contractTaxDetails{
    taxSectionId: Int,
    taxSectionName: String,
    description: String,
    residentThresholdLimit: String,
    nonResidentThresholdLimit: String,
    residentTdsRate: String,
    nonResidentTdsRate: String,
    residentNoPanTdsRate: String,
    nonResidentNoPanTdsRate: String
}

type retrievePerformanceSettingsResponse{
  errorCode:String,
  message:String,
  settingsDetails:settingsDetails
}

type settingsDetails{
  Performance_Management_Mode:String,
  Maximum_Rating:Int,
  GoalSettings_ReminderOne:Int,
  GoalSettings_ReminderTwo:Int,
  RatingUpdate_ReminderOne:Int,
  RatingUpdate_ReminderTwo:Int,
  Goal_Email_Subject:String,
  Goal_Email_Content:String,
  Rating_Email_Subject:String,
  Rating_Email_Content:String,
  Enable_Email_Notification:String,
  Present_Only_Team_Members_To_Managers:String,
  ratingDetails:[ratingDetails],
  isRatingEditable:String
}

type ratingDetails{
  Rating_Id:Int,
  Rating_Description:String
}

type updatePerformanceSettingsResponse{
  errorCode:String,
  message:String
}

type addGoalsToLibraryResponse{
  errorCode:String,
  message:String
}

type listGoalsResponse{
  errorCode:String,
  message:String,
  goalsList:[goalsList]!
}

type goalsList{
  goalId:Int,
  goalDescription:String,
  status:String,
  isGoalAssociated:Int
}

type listEmployeeGoalsAndRatingResponse{
  errorCode:String,
  message:String,
  listEmployeeGoalRatingsDetails:[listEmployeeGoalDetails]
}

type listEmployeeGoalDetails{
  performanceAssessmentId:Int,
  employeeId:Int,
  employeeName:String,
  designation:String,
  department:String,
  userDefinedEmpId:String,
  dateOfJoining:String,
  reviewerName:String,
  assessmentStatus:String,
  goalPublishStatus:String,
  ratingPublishStatus:String,
  overallRating:Int,
  averageYearlyRating:Float,
  comments:String,
  photoPath:String,
  employeeStatus:String,
  reviewerEmployeeId:Int,
  reviewerUserDefinedEmpId:String,
  addedOn: String,
  addedByEmployeeName: String,
  updatedOn: String,
  updatedByEmployeeName: String
}

type listPmsEmployeesResponse{
  errorCode : String
  message : String
  employees : [pmsEmployeesListDetails]
}

type listPmsReviewersResponse{
  errorCode : String
  message : String
  reviewers : [pmsReviewersListDetails]
}

type pmsEmployeesListDetails{
  employee_id: Int
  user_defined_empid : String
  employee_name: String
  photo_path: String
  designation_name: String
  department_name: String
}

type pmsReviewersListDetails{
  employee_id: Int
  user_defined_empid : String
  employee_name: String
  designation_name: String
  department_name: String
}

type retrieveEmployeeGoalsAndRatingResponse{
  errorCode:String,
  message:String,
  employeeGoalsRatings:[employeeGoalsRatings]
}

type employeeGoalsRatings{
  employeeId:Int,
  overallRating:Int,
  averageYearlyRating:Float,
  comments:String,
  goalsAndRating:String,
  lastReviewDate:String
}

type goalsAndRating{
  goalId:Int,
  goalDescription:String,
  rating:Int
}

type retrieveRatingsResponse{
  errorCode:String,
  message:String,
  getRatingDetails:getRatingDetails
}

type getRatingDetails{
  maximumRating:Int,
  ratingDetails:[ratingDetails]
}

type listEmployeesAchievementResponse{
  errorCode:String,
  message:String,
  listEmployeeAchievementDetails:[listEmployeeAchievementDetails]
}

type listEmployeeAchievementDetails{
  assessmentMonthYear:String,
  overallRating:Int,
  assessmentStatus:String,
  goalPublishStatus:String,
  ratingPublishStatus:String
}

type listPerformanceYearResponse{
  errorCode:String,
  message:String,
  performanceYear:String
}

type retrieveTeamRatingsResponse{
  errorCode:String,
  message:String,
  teamRating:teamRating
}

type teamRating{
  rating:[Int],
  employeeCount:[Int]
}

type retrieveTeamGoalsRatingsCountResponse{
  errorCode:String,
  message:String,
  teamGoalsRatingsCount:[teamGoalsRatingsCount]
}

type teamGoalsRatingsCount{
  teamEmployeesCount: Int!,
  goalsPublishedCount: Int!,
  ratingsPublishedCount: Int!,
  averageOverallRating: Float!,
  monthYear: String!
}


type retrieveEmployeeYearlyRatingResponse{
  errorCode:String,
  message:String,
  employeeYearlyRatings:employeeYearlyRatings
}

type employeeYearlyRatings{
  performanceMonthArray:[String],
  ratingArray:[Int],
  performanceYear:String
}

type teamMonthlyAverageRatings{
  performanceMonths:[String],
  averageMonthlyRatings:[Float]
  performanceYear: String
}

type retrieveTeamAverageMonthlyRatingsResponse{
  errorCode:String,
  message:String,
  teamMonthlyAverageRatings:teamMonthlyAverageRatings
}

type overtimeCompOffBalanceResponse{
  errorCode:String,
  message:String,
  compensatoryOffBalance:Float!
}

type listShiftAllowanceShiftTypeResponse{
  errorCode:String,
  message:String,
  shiftAllowanceShiftTypeList:[shiftAllowanceShiftTypes]
}

type shiftAllowanceShiftTypes{
  WorkSchedule_Id: Int,
  Title: String
}

type overtimeShiftAllowanceResponse{
  errorCode:String,
  message:String,
  shiftAllowance: Float
}

type getRedeemRewardsDetailsResponse{
  errorCode:String,
  message:String,
  redeemRewardsDetails:String
}

type getOrganizationSubscribedPlanResponse{
  errorCode:String,
  message:String,
  subscribedDashboard:String
}

type retrieveCurrencyDetailsResponse{
  errorCode:String,
  message:String,
  currencyDetails:[getCurrencyDetails]
}

type getCurrencyDetails{
  currencyId:Int,
  currencyCode:String
}

type retrieveCurrencyAndShareValueResponse{
  errorCode:String,
  message:String,
  currencyAndShareValue:[currencyAndShareValue]
}

type currencyAndShareValue{
  currencyCode:String,
  currencyId:Int,
  shareValue:Float,
}

type listAllEmployeeDetailsResponse{
  errorCode:String,
  message:String,
  employeeList:[employeeList]
}

type employeeList{
  employee_id:Int,
  user_defined_empid:String,
  employee_name:String,
  emp_email:String,
  designation_name:String,  
  department_name:String,
  Designation_Id:Int,
  Department_Id: Int,
  Location_Id: Int,
  EmpType_Id: Int,
  Work_Schedule: Int,
  emp_status: String,
  location_name:String,  
  employee_type:String,
  work_schedule_name:String
  Job_Role_Ids:[Int]
  Job_Role_Details: [Job_Role_Details]
  Business_Unit: String,
  Service_Provider_Name: String
}
type Job_Role_Details{
  Job_Role_Name:String
  Job_Role_Id:Int
}
type listEmployeeTotalSharesResponse{
  errorCode:String,
  message:String,
  employeeTotalSharesList: [employeeTotalSharesList]
}

type employeeTotalSharesList{
  Employee_Id: Int,
  User_Defined_EmpId: String,
  Employee_Name: String,
  Designation_Name: String,
  ESOP_Status: String,
  Employee_Total_Allocated_Shares: Float,
  Employee_Total_Allocated_Shares_Value: Float,
  Employee_Total_Vested_Shares: Float,
  Employee_Total_Vested_Shares_Value: Float
}

type listEmployeeAllocatedSharesResponse{
  errorCode:String,
  message:String,
  employeeAllocatedSharesList: [employeeAllocatedSharesList]
}

type employeeAllocatedSharesList{
  Employee_Allocated_Share_Id: Int,
  Allocated_Shares: Float,
  Total_Share_Value: Float,
  Allocated_Date: Date,
  Vested_Share: Float,
  Vested_Status: String
}

type retrieveShareVestHistoryResponse{
  errorCode:String,
  message:String,
  vestedDetails:[vestedDetails]
}

type vestedDetails{
  vestedDate:Date,
  vestedShare:Float,
  totalShare:Float
}

type bulkImportESOPResponse{
  errorCode:String,
  message:String,
  errorResponse:String
}

type listStatesResponse{
  errorCode:String,
  message:String,
  listStateDetails:[listStateDetails]
}

type listStateDetails{
  stateCode:String,
  stateName:String
}

type listHelpUrlAndTextResponse{
  errorCode:String
  message:String
  helpDetails:[helpDetails]
}

type helpDetails{
  Title:String
  Type:String
  Url:String
  Embed_Url:String
  Text:String
}

schema {
  query: Query,
  mutation : Mutation
}
