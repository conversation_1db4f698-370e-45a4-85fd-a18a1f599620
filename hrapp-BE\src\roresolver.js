// require resolver files
const listProbationEmployees = require('./roresolvers/dashboard/listProbationEmployees');
const retrievePmsNonComplianceReport = require('./roresolvers/performanceManagementSystem/reports/retrievePmsNonComplianceReport');
const checkAttendanceConfiguration = require('./roresolvers/dashboard/checkAttendanceConfiguration');
const getEmployeeDetailsBasedOnGroup = require('./roresolvers/getEmployeeDetailsBasedOnGroup');
const getReportHistory = require('./roresolvers/getReportHistory');
const getReportSetting = require('./roresolvers/getReportSetting');
const getResignationPayslipDetails = require('./roresolvers/getResignationPayslipDetails');
const getTeamLeaveHistory = require('./roresolvers/leaves/getTeamLeaveHisory');
const retrieveDashboardExpiredDocsDetails = require('./roresolvers/dashboard/notifcation/retrieveDashboardExpiredDocsDetails');
const listShiftRotation = require('./roresolvers/shiftRotation/listShiftRotation');
const listShiftLeaveTypes = require('./roresolvers/shiftRotation/listShiftLeaveTypes');
const listshiftSwapping=require('./roresolvers/shiftSwapping/listshiftSwapping');
const getShiftSwapDateRange = require('./roresolvers/shiftSwapping/getShiftSwapDateRange');

// Define resolver
const resolvers = {
    Query: Object.assign({},
        listProbationEmployees,
        retrievePmsNonComplianceReport,
        checkAttendanceConfiguration,
        getEmployeeDetailsBasedOnGroup,
        getReportHistory,
        getReportSetting,
        getResignationPayslipDetails,
        getTeamLeaveHistory,
        retrieveDashboardExpiredDocsDetails,
        listShiftRotation,
        listShiftLeaveTypes,
        listshiftSwapping,
        getShiftSwapDateRange
    )
}

exports.resolvers = resolvers;