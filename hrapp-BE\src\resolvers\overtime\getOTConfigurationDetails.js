// resolver function to list the overtime records of the employee
module.exports.getOTConfigurationDetails = async (parent, args, context, info) => {
    console.log('Inside getOTConfigurationDetails() function');
    // require apollor server errors
    const { ApolloError } = require('apollo-server-lambda');
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // Organization database connection
    const knex = require('knex');
    // require table names
    const { ehrTables } = require('../../../common/tablealias');
    //require moment 
    const moment = require('moment-timezone');
    //Variable declaration
    let orgDb;
    try{
            // get db connection
            orgDb = knex(context.connection.OrganizationDb);
            // variable declarations
            let response = {};
            // get min year from overtime_claims table
            let minYearResponse = await orgDb(ehrTables.overtime).select(
                                    orgDb.raw(`min(Year(Start_Date_Time)) as minYear`))
                                    .then(years =>{
                                        // return response
                                        return years[0]? years[0].minYear:'';
                                    })
                                    .catch(getMinMaxYearsError =>{
                                        console.log('Error while getting the min and max years in getOTConfigurationDetails() function.', getMinMaxYearsError);
                                        return {};
                                    });
            let currentYear = moment.utc().format('YYYY');
            let minMaxYears = [];
            //If the additional wage claim min year is less than the current year
            if(minYearResponse && minYearResponse < currentYear){
                //Iterate from overtime claim start year to the current year
                for(let startYear=minYearResponse; startYear<currentYear;){
                    minMaxYears.push(startYear);
                    startYear++;
                }
            } 
            minMaxYears.push(currentYear);

            //Push the year in a descending order
            minMaxYears = minMaxYears.reverse();
            // form response
            response.minAndMaxYears = minMaxYears;
            // get overtime settings from overtime_settings table
            let overtimeSettings = await orgDb(ehrTables.overtimeSettings)
                                        .select('*')
                                        .then(overtimeSettingsResponse => {
                                            // return response
                                            return overtimeSettingsResponse[0] ? overtimeSettingsResponse[0] : {};
                                        })
                                        .catch(getOvertimeSettingsError =>{
                                            console.log('Error while getting the overtime settings in getOTConfigurationDetails() function.', getOvertimeSettingsError);
                                            // return response
                                            return {};
                                        });
            // form response
            response.overtimeSettings = overtimeSettings;
            // destroy database connection
            orgDb ? orgDb.destroy() : null;
            // return response back to UI
            return { errorCode: '', message: 'Overtime configuration details retrieved successfully.', configurationData:response};
              
    } catch (getOTConfigurationDetailsMainCatchError){
        console.log('Error in getOTConfigurationDetails() function main catch block.', getOTConfigurationDetailsMainCatchError);
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
        // get error and return it to UI
        let errResult = commonLib.func.getError(getOTConfigurationDetailsMainCatchError, 'OT0102');
        throw new ApolloError(errResult.message, errResult.code)
    }
};