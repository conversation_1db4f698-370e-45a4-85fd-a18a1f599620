// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require common constant files
const { formName,defaultValues } = require('../../../common/appconstants');
// require table alias
const { ehrTables } = require('../../../common/tablealias');
// require validation file
const performanceManagementValidation = require('../../../common/performanceManagementValidation');

// variable declarations
let errResult = {};
let organizationDbConnection = '';
let validationError={};

// resolver definition
const resolvers = {
    Query: {
        // function to retrieve the employee performance goals and achievement - for admin and employee view
        retrieveEmployeeGoalsAndRating: async (parent, args, context, info) => {
            try{
                console.log('Inside retrieveEmployeeGoalsAndRating function');
                // get the organization data base connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                // variable declarations
                let logInEmpId = context.Employee_Id;
                let goalsAchievementQuery;
                // check access rights
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.goalsAndAchievement, '', 'UI');
                // if request from admin form need to check whether loggedIn employee have admin or manager access
                // for employee - check only the view access
                let condition=(args.isAdminView===1)?(checkRights.Is_Manager===1 || checkRights.Employee_Role==='admin'):true;
                // check whether the employee has view access to goalsAndAchievement form
                if ((Object.keys(checkRights).length >0 && checkRights.Role_View===1) && condition) {
                    // function to validate inputs            
                    validationError=await performanceManagementValidation.employeeGoalsAndRatingInputValidation(args,'validateMonthYear');
                    // validate employeeid if request from admin view
                    if(args.isAdminView===1){
                        if (!args.employeeId) {
                            validationError['IVE0083'] = commonLib.func.getError('', 'IVE0083').message;
                        }
                    }
                    // validate isAdminView field
                    // if it is 1 it represents admin view , 0 - employee view
                    if (!(args.isAdminView===1 || args.isAdminView===0)) {
                        validationError['IVE0097'] = commonLib.func.getError('', 'IVE0097').message;
                    }
                    // check whether there is no input validation error
                    if(Object.keys(validationError).length ===0){
                        // Get the goals and achievement details for the input employee,year and month
                        let subQuery=organizationDbConnection(ehrTables.performanceGoalAchievement+ ' as PGA')
                        .select('PGA.Employee_Id as employeeId','PGA.Overall_Rating as overallRating','PGA.Average_Yearly_Rating as averageYearlyRating','PGA.Comments as comments','Rating_Publish_Status as ratingPublishStatus',
                        organizationDbConnection.raw(`CONCAT('[',GROUP_CONCAT('{"rating":', AGR.Rating, ',"goalId":',PGL.Goal_Id,',"goalDescription":"',PGL.Goal_Description,'"}'),']') as goalsAndRating`))
                        .leftJoin(ehrTables.assessmentCycleGoalRatings+ ' as AGR','AGR.Performance_Assessment_Id','PGA.Performance_Assessment_Id')
                        .leftJoin(ehrTables.performanceGoalsLibrary+ ' as PGL','PGL.Goal_Id','AGR.Goal_Id')
                        .where('PGA.Performance_Assessment_Month',args.month)
                        .where('PGA.Performance_Assessment_Year',args.year)
                        .groupBy('PGA.Performance_Assessment_Id')

                        // if request from admin view then return the data for the input employeeId
                        if(args.isAdminView===1){
                            goalsAchievementQuery=subQuery
                            .where('PGA.Employee_Id',args.employeeId)
                        }
                        // if request from employee view then return only the corresponding employeeId
                        else{
                            goalsAchievementQuery=subQuery
                            .where('Goal_Publish_Status', defaultValues.goalsAndAchievementGoalPublishStatus)                
                            .where('PGA.Employee_Id',logInEmpId)
                        }
                        return(
                            goalsAchievementQuery
                            .then(async(getDetails) => {
                                console.log('Response from retrieveEmployeeGoalsAndRating function',getDetails);
                                if(Object.keys(getDetails).length>0){
                                    // if request from admin view and rating is unpublished then return the ratings else for employee view return as empty 
                                    if(args.isAdminView===0 && getDetails[0].ratingPublishStatus==='Unpublished'){
                                        getDetails[0].overallRating=0;
                                        getDetails[0].comments=null;
                                    }
                                    // for admin view we need to return the last review date and not for the employee view
                                    getDetails[0].lastReviewDate=(args.isAdminView===1)?await getPreviousMonthReviewDate(organizationDbConnection,args,context.Org_Code):'';
                                }
                                // destroy database connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return {errorCode:'',message:'Employee goals and achievement retrieved successfully.',employeeGoalsRatings:(Object.keys(getDetails).length>0)?getDetails:[]};                                
                            })
                            .catch(catchError => {
                                console.log('Error in retrievePerformanceSettings function .catch block.', catchError);
                                // destroy database connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                errResult = commonLib.func.getError(catchError, 'EPM0106');
                                // throw error response to UI
                                throw new ApolloError(errResult.message, errResult.code);
                            })    
                        );
                    }
                    else{
                        throw 'IVE0000';
                    }
                }
                else{
                    throw '_DB0100';
                }
            }catch(mainCatchError){
                console.log('Error in retrieveEmployeeGoalsAndRating function main catch block',mainCatchError);
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                if (mainCatchError === 'IVE0000') {
                    errResult = commonLib.func.getError('', 'IVE0000');
                    // return response
                    throw new UserInputError(errResult.message, { validationError: validationError });
                } else {
                    errResult = commonLib.func.getError(mainCatchError, 'EPM0008');
                    // return response
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        }
    }
};

exports.resolvers = resolvers;

// function to get the last review date based on the inputs
async function getPreviousMonthReviewDate(organizationDbConnection,args,orgCode){
    try{
        let previousMonth,inputYear;
        // if month is greater than 1 then month-1 will be previous month and year will be same.
        // if month is 1 then previous month will be 12 and year will be year-1
        if(args.month>1){
            previousMonth=args.month-1;
            inputYear=args.year;
        }
        else{
            previousMonth=12;
            inputYear=args.year-1;
        }
        // We need to present the last review date in view form for which previous month and year formed.
        // In add we will validate whether all the previous month ratings completed before adding new goals
        return(
            organizationDbConnection(ehrTables.performanceGoalAchievement)
            .pluck('Updated_On')
            .where('Employee_Id',args.employeeId)
            .where('Performance_Assessment_Month',previousMonth)
            .where('Performance_Assessment_Year',inputYear)
            .then(async(getDate)=>{
                // check data exist or not
                if(getDate.length>0)
                {
                    // split date and time from the timestamp
                    let lastUpdated=getDate[0].split(' ')[0];
                    // formation of date to organization date format
                    let {Date_Format}=await commonLib.func.getOrgDetails(orgCode,organizationDbConnection,0);
                    let lastReviewDate=await commonLib.func.getFormattedDateString('',Date_Format,lastUpdated);
                    return lastReviewDate;
                }
                else{
                    console.log('Last month review date does not exist.');
                    return '';
                }
            })
            .catch(catchError => {
                console.log('Error in getPreviousMonthReviewDate function .catch block',catchError);
                return '';
            })
        );
    }
    catch(error){
        console.log('Error in getPreviousMonthReviewDate function main catch block',error);
        return '';
    }
};
