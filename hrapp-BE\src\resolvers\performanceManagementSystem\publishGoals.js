// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName,systemLogs,defaultValues,awsSesTemplates } = require('../../../common/appconstants');
// require table alias
const { ehrTables } = require('../../../common/tablealias');
// require common function
const { goalsPublishableOrDeletePerformanceIds, sendEmailToEmployees } = require('../../../common/performanceManagementCommonFunction');
// require pms validation function
const {employeeGoalsAndRatingInputValidation} = require('../../../common/performanceManagementValidation');

/**
 * Function to update goal publish status for the performance assessment ids and the success action in the system log table
 * @param {Object} organizationDbConnection - Organization database connection object
 * @param {JSON} updateArgs 
 * @param {String} updateArgs.loginEmpCurrentDateTime - Login employee timezone
 * @param {Number} updateArgs.loginEmpId - Login employee id
 * @param {array} updateArgs.performanceAssessmentIds - Performance assessment ids
 * @param {String} updateArgs.userIp - Login employee user ip
 * @returns {String|JSON} - Returns JSON when an error occurred in the main catch block and return string in the success and .catch block
 */
async function updateGoalsPublishStatusInTable(organizationDbConnection,updateArgs){
    try{
        let { loginEmpCurrentDateTime, loginEmpId, performanceAssessmentIds, userIp } = updateArgs;
        
        let updateDetails = {
            'Goal_Publish_Status': defaultValues.goalsAndAchievementGoalPublishStatus,
            'Updated_On': loginEmpCurrentDateTime,
            'Updated_By': loginEmpId
        };

        // update goal publish details for all the valid employee(s) in the performance goals and achievement table
        let updateGoalsPublishStatusResponse = 
        await organizationDbConnection(ehrTables.performanceGoalAchievement)
        .update(updateDetails)
        .whereIn('Performance_Assessment_Id',performanceAssessmentIds)
        .where('Goal_Publish_Status',defaultValues.goalsAndAchievementGoalUnPublishStatus)
        .then(async(updateResponse) =>{
            if(updateResponse){                
                // form inputs to update system log. 
                let systemLogParams = {
                    action: systemLogs.roleUpdate,
                    userIp: userIp,
                    employeeId: loginEmpId,
                    formName: formName.goalsAndAchievement,
                    trackingColumn: ' - Publish Goals',
                    organizationDbConnection: organizationDbConnection,
                    uniqueId: performanceAssessmentIds
                };
                // call function createSystemLogActivities() to update system log
                await commonLib.func.createSystemLogActivities(systemLogParams);

                return 'success';
            }else{
                console.log('Unable to publish goals as the goals are not in unpublished status or the goals are already published for the performance ids,',performanceAssessmentIds);
                throw 'EPM0022';
            }
        })
        .catch(updateError =>{
            console.log('Error while updating the goal publish status in the publishGoals() function .catch block.',updateError);
            throw ( updateError === 'EPM0022' ?  'EPM0022' : 'EPM0114');
        });

        return updateGoalsPublishStatusResponse;
    }catch(updateGoalsPublishStatusCatchError){
        console.log('Error in the updateGoalsPublishStatusInTable() main catch block.',updateGoalsPublishStatusCatchError);
        throw (updateGoalsPublishStatusCatchError);
    }
}

// publish the employee(s) goals for the whole assessment month or year or for the performance assesssment id(form level)
module.exports.publishGoals = async (parent, args, context, info) => {
    console.log("Inside publishGoals() function.");
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const loginEmpId = context.Employee_Id;

        // Check Goals and achievement form access rights.
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmpId, formName.goalsAndAchievement, '', 'UI');

        // If the login employee is admin or manager and having view access for the 'Goals and achievement' form
        if ((Object.keys(checkRights).length >0 && checkRights.Role_Update===1) && (checkRights.Is_Manager===1 || checkRights.Employee_Role==='admin')) {
            //Function to validate inputs
            validationError=await employeeGoalsAndRatingInputValidation(args,'validateMonthYear');
            if(Object.keys(validationError).length > 0 || (!(args.performanceAssessmentId) 
            || args.performanceAssessmentId.length === 0)){
                console.log('Validation error in the publish ratings function.',validationError);
                throw('_EC0007');
            }else{
                console.log('Inside the performance assessment id - publish goals.');
                // call the function to get the performance ids based on the goal publish status, assessment month, assessment year, login employee-admin/manager role
                let performanceAssessmentIds =await goalsPublishableOrDeletePerformanceIds(organizationDbConnection,loginEmpId,checkRights.Employee_Role,args,'publishgoals');
                if(performanceAssessmentIds && performanceAssessmentIds.length > 0){
                    // get the login employee current date and time based on login employee location
                    let loginEmpCurrentDateTime = await commonLib.func.getEmployeeTimeZone(loginEmpId, organizationDbConnection, 1);
                    let performanceAssessmentIdInArray = performanceAssessmentIds;
                    let performanceMonthName = defaultValues.monthsArray[(args.month)-1];

                    let updateArgs = {
                        loginEmpCurrentDateTime: loginEmpCurrentDateTime,
                        loginEmpId: loginEmpId,
                        performanceAssessmentIds: performanceAssessmentIdInArray,
                        userIp: context.User_Ip
                    };

                    await updateGoalsPublishStatusInTable(organizationDbConnection,updateArgs);

                    let sendEmailArgs = {
                        orgCode: context.Org_Code,
                        performanceAssessmentIds: performanceAssessmentIdInArray,
                        templateName: awsSesTemplates.performanceGoalsPublishedNotification,
                        emailContent: 'Your goals are assigned and published for the month of '+performanceMonthName+' '+args.year,
                        emailSubject: 'Goals published for '+performanceMonthName+' '+args.year
                    }
                    //Send the goals published email to the respective employees
                    await sendEmailToEmployees(organizationDbConnection,sendEmailArgs);

                    //destroy DB connection
                    organizationDbConnection.destroy();
                    return { errorCode: '', message: 'Goals published successfully.' };// return response
                }else{
                    console.log('Login employee is not the eligible approver to approve the performance id,',args.performanceAssessmentId);
                    throw('EPM0023');
                }
            }
        }else{
            throw('_DB0102');//throw employee does not have edit access
        }
    }catch(publishGoalsMainCatchErr) {
        console.log('Error in the publishGoals() function main catch block. ',publishGoalsMainCatchErr);
        // destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(publishGoalsMainCatchErr, 'EPM0012');
        throw new ApolloError(errResult.message,errResult.code);// return response
    }
};