// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require common validation file
const validation = require('../../../common/commonvalidation');
//Require moment package
const moment=require('moment');

// function to validate the update income tax declaration settings inputs
async function validateUpdateITDeclarationSettingsInputs(args,orgcode,orgDb) {
    try {
        let validationError = {};

        // get input values
        let { lockDate, newJoinersGraceDays, notifyITDeclarationRelease,notifyITDeclarationLock,notifyITDeclarationSubmissionBeforeLockDate, remindFiveDaysBefore,remindOneDayBefore,emailSubject,emailContent ='' } = args;
        
        // Validate lockDate
        if (!lockDate) {
            validationError['IVE0108'] = commonLib.func.getError('', 'IVE0108').message1;
        }else{
            // Get the fiscal details
            let fiscalDetails = await commonLib.payroll.getFiscalDetails(orgcode,orgDb);
            
            // If the financial start date and end date exist
            if(fiscalDetails && Object.keys(fiscalDetails).length > 0 && fiscalDetails.fiscalStartDate && fiscalDetails.fiscalEndDate){
                let fiscalStartDate = fiscalDetails.fiscalStartDate;
                let fiscalEndDate = moment(fiscalDetails.fiscalEndDate).add(1, 'M').format("YYYY-MM-DD");

                // If the lock date is less than the fiscal start date or lock date is greater than the fiscal end date
                if( lockDate < fiscalStartDate || lockDate > fiscalEndDate ){
                    validationError['IVE0108'] = commonLib.func.getError('', 'IVE0108').message2;
                }
            }
        }

        // Validate newJoinersGraceDays
        if (newJoinersGraceDays) {
            let validateNewJoinersGraceDays = validation.checkMinMaxValue(newJoinersGraceDays, 1, 60);
            if (!validateNewJoinersGraceDays) {
                validationError['IVE0109'] = commonLib.func.getError('', 'IVE0109').message;
            }
        }

        // Validate notifyITDeclarationRelease
        if (notifyITDeclarationRelease !== 'Yes' &&  notifyITDeclarationRelease !== 'No') {
            validationError['IVE0110'] = commonLib.func.getError('', 'IVE0110').message;
        }

        // Validate notifyITDeclarationLock
        if (notifyITDeclarationLock !== 'Yes' && notifyITDeclarationLock !== 'No') {
            validationError['IVE0111'] = commonLib.func.getError('', 'IVE0111').message;
        }

        // Validate notifyITDeclarationSubmissionBeforeLockDate
        if (notifyITDeclarationSubmissionBeforeLockDate === 'Yes' || notifyITDeclarationSubmissionBeforeLockDate === 'No') {
            if(notifyITDeclarationSubmissionBeforeLockDate === 'Yes'){
                // Validate remindFiveDaysBefore
                if (remindFiveDaysBefore !== 'Yes' && remindFiveDaysBefore !== 'No') {
                    validationError['IVE0113'] = commonLib.func.getError('', 'IVE0113').message;
                }

                // Validate remindOneDayBefore
                if (remindOneDayBefore !== 'Yes' && remindOneDayBefore !== 'No') {
                    validationError['IVE0114'] = commonLib.func.getError('', 'IVE0114').message;
                }

                /** In the Income tax declaration settings form, for the email reminder notification either 1 day or 5 day should be selected to send the email notification before the lock date */
                if(remindFiveDaysBefore === 'No' && remindOneDayBefore === 'No'){
                    validationError['IVE0115'] = commonLib.func.getError('', 'IVE0115').message;
                }

                // vaidate emailSubject if exists
                if(emailSubject){
                    let validateEmailSubject = validation.checkLength(emailSubject, 1, 64);
                    if (!validateEmailSubject) {
                        validationError['IVE0116'] = commonLib.func.getError('', 'IVE0116').message;
                    }
                }

                // vaidate emailContent if exists
                if(emailContent){
                    let validateEmailContent = validation.checkLength(emailContent, 1, 300);
                    if (!validateEmailContent) {
                        validationError['IVE0117'] = commonLib.func.getError('', 'IVE0117').message;
                    }
                }
            }
        }else{
            validationError['IVE0112'] = commonLib.func.getError('', 'IVE0112').message;
        }

        // return validation error back to function
        return validationError;
    } catch (validateUpdateITDeclarationSettingsInputsError) {
        console.log('Error in validateUpdateITDeclarationSettingsInputs() function main catch block.', validateUpdateITDeclarationSettingsInputsError);
        throw (validateUpdateITDeclarationSettingsInputsError);
    }
}
module.exports.validateUpdateITDeclarationSettingsInputs = validateUpdateITDeclarationSettingsInputs;