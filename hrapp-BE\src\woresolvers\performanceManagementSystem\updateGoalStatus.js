//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make database connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError,ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
//Require constants
const { formName,systemLogs,defaultValues } = require('../../../common/appconstants');

//Update the performance goal status
module.exports.updateGoalStatus = async (parent, args, context, info) => {
    console.log("Inside updateGoalStatus() function.");
    //Variable declarations
    let validationError={};
    let errorResult='';
    let organizationDbConnection;
    try{
        //Get database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        //Check whether the employee has update access to goalsAndAchievement form or not & loggedIn employee should be either admin or manager
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId , formName.goalsAndAchievement, '', 'UI');
        //Check whether loggedIn employee is manager/admin
        if ((Object.keys(checkRights).length >0 && checkRights.Role_Update===1) && (checkRights.Is_Manager===1 || checkRights.Employee_Role==='admin')) {
            if (!args.goalId) {
                validationError['IVE0146'] = commonLib.func.getError('', 'IVE0146').message;
            }
            if (!(defaultValues.performanceGoalStatus.includes(args.goalStatus))) {
                validationError['IVE0211'] = commonLib.func.getError('', 'IVE0211').message;
            }
            if(Object.keys(validationError).length ===0){
                //Get the employee timezone based on location
                let loginEmployeeCurrentDateTime = await commonLib.func.getEmployeeTimeZone(loginEmployeeId, organizationDbConnection, 1);
                let newGoalStatus = args.goalStatus;
                let updateDetails = {
                    'Status': args.goalStatus,
                    'Last_Updated_On': loginEmployeeCurrentDateTime,
                    'Last_Updated_By': loginEmployeeId
                };
                let oldGoalStatus='';
                if(newGoalStatus === 'Active'){
                    oldGoalStatus = 'Archive';
                }
                else{
                    oldGoalStatus = 'Active';
                }
                //Update the goal status
                return(
                organizationDbConnection(ehrTables.performanceGoalsLibrary)
                .update(updateDetails)
                .where('Goal_Id',args.goalId)
                .where('Status',oldGoalStatus)
                .then(async(updateResult) =>{
                    if(updateResult){
                        //Function to update system logs. Ex: Update Performance Management Goal status to archive - 58
                        let systemLogParams = {
                            action: systemLogs.roleUpdate,
                            userIp: context.User_Ip,
                            employeeId: loginEmployeeId,
                            formName: formName.performanceManagement,
                            trackingColumn: 'Goal status to ' + args.goalStatus.toLowerCase(),
                            organizationDbConnection: organizationDbConnection,
                            uniqueId: args.goalId
                        };
                        //Call function to update system log activities
                        await commonLib.func.createSystemLogActivities(systemLogParams);
                        //Destroy database connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return {errorCode:'',message:'Performance goal status updated successfully.'};
                    }else{
                        console.log('Goal status is already updated.',updateResult);
                        //Destroy database connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return {errorCode:'',message:'Performance goal status updated successfully.'};
                    }
                }) 
                .catch(function (catchError) {
                    console.log('Error in updateGoalStatus function .catch block',catchError);
                    //Destroy database connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    errorResult = commonLib.func.getError(catchError, 'EPM0130');
                    throw new ApolloError(errorResult.message,errorResult.code);
                })
                );
            }
            else{
                throw 'IVE0000';
            }
        }
        else{
            throw '_DB0102';
        }
    }catch(mainCatchError){
        console.log('Error in updateGoalStatus function main catch block',mainCatchError);
        errorResult = commonLib.func.getError(mainCatchError, 'EPM0030');
        //Destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            console.log('Validation error in the updateGoalStatus function.',validationError);
            errorResult = commonLib.func.getError('', 'IVE0000');
            //Return error response
            throw new UserInputError(errorResult.message, { validationError: validationError });
        } else {
            //Return error response
            throw new ApolloError(errorResult.message,errorResult.code);
        }
    }
};