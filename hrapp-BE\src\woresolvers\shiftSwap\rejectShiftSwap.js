// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formIds } = require('../../../common/appconstants');
// require common constant files
const { rejectShiftSwap } = require('./statusApprovalCommonFunction');

//Function to validate the input fields
async function validateInputs(args){
    try{
        if(args.swapId){
            return 'success';
        }else{
            handleError('validationError','_EC0007', args);
        }
    }catch(error){
        console.log('Error in validateInputs() function main catch block', error);
        throw error;
    }
}

function handleError(error,errorCode, args){
    let errResult;
    console.log('Inside in rejectShiftSwap handleError function.', error,errorCode,args);
    errResult = commonLib.func.getError(errorCode, 'SS0009');
    throw new ApolloError(errResult.message, errResult.code);
}

//function to validate and update the shift swap status
module.exports.rejectShiftSwap = async (parent, args, context, info) => {
    let organizationDbConnection;

    try {
        console.log("Inside rejectShiftSwap function.")
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
       
        //Check login employee access for shift swap form
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, null, 'ui', null, formIds.shiftSwap, {orgCode: context.Org_Code });
        if (!checkRights || !checkRights.Role_Update) {
            console.log('Employee do not have update access.',checkRights);
            throw '_DB0102';
        }

        await validateInputs(args);

        await rejectShiftSwap(organizationDbConnection, args, context);
        
        return { errorCode: "", message: `${checkRights.Custom_Form_Name} request rejected successfully.`};
    }
    catch (e) {
        handleError(error,errorCode, args);
    }
    finally {
        organizationDbConnection ? organizationDbConnection.destroy : null;//Destroy DB connection
    }
}