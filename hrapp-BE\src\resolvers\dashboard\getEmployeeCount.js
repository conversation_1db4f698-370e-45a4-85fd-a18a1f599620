// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require table alias
const { ehrTables } = require('../../../common/tablealias');

module.exports.getEmployeeCount = async (parent, args, context, info) => {
    // get the organization data base connection
    const orgDb = knex(context.connection.OrganizationDb);
    try
    {   
        // Formation of query to get employee count in personal info table based on status
        const employeeSubQuery = orgDb(ehrTables.empPersonalInfo+ ' as EP')
                                .innerJoin(ehrTables.empJob + ' as EJ','EP.Employee_Id','EJ.Employee_Id')
                                .count('EP.Employee_Id as Count')
                                .first()

        let totalEmployeesCount = await employeeSubQuery.select();
        totalEmployeesCount = totalEmployeesCount.Count;

        let activeEmployeesCount = await employeeSubQuery.where('EJ.Emp_Status','Active');
        activeEmployeesCount = activeEmployeesCount.Count;

        return { 
            errorCode: '', 
            message: 'Employees count retrieved successfully', 
            totalEmployeesCount, activeEmployeesCount
        }
    } 
    catch (mainCatchError) 
    {
        console.log('Error in getting employee count main catch block',mainCatchError);

        // return response
        let errResult = commonLib.func.getError(mainCatchError, 'DB0001');
        throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, activeEmployeesCount : 0 ,totalEmployeesCount: 0 }));
    }
    finally
    {
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
    }
}
