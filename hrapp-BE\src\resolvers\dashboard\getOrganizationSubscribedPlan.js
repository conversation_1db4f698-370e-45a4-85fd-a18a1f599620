// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex package
const knex = require('knex');
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require table alias list
const {appManagerTables} = require('../../../common/tablealias');

// variable declarations
let appManagerDbConnection = '';

// resolver definition
const resolvers = {
    Query: {
        // function to retrieve organization subscribed plan
        getOrganizationSubscribedPlan: async (parent, args, context, info) => {
            try{
                console.log('Inside getOrganizationSubscribedPlan function');
                let subscribedDashboard='';
                // Make appmanager database connection
                appManagerConnection = knex(context.connection.AppManagerDb);
                return(
                    appManagerConnection
                    .transaction(function(trx){
                        return(
                            // get the subscribed dashboard for the instance from plan details
                            appManagerConnection(appManagerTables.hrappRegisteruser+' as HRU')
                            .select('HPD.Dashboard')
                            .innerJoin(appManagerTables.billingRate + ' as BR', 'HRU.Billing_Id', 'BR.Billing_Id')
                            .innerJoin(appManagerTables.hrappPlanDetails + ' as HPD', 'BR.Plan_Id', 'HPD.Plan_Id')
                            .where('HRU.Org_Code',context.Org_Code)
                            .transacting(trx)
                            .then(async(getData) => {
                                if(getData.length>0){
                                    subscribedDashboard=getData[0].Dashboard;
                                }
                                else{
                                    console.log('No plan details associated for '+context.Org_Code+' instance');
                                }
                                // return success response
                                return { errorCode: '', message: 'Subscribed plan retrieved successfully',subscribedDashboard : subscribedDashboard };
                            })
                        );
                    })
                    .then(function (result) {
                        return result;
                    })
                    .catch(function (catchErrror) {
                        console.log('Error in getOrganizationSubscribedPlan function .catch block', catchErrror);
                        let errResult = commonLib.func.getError(catchErrror, 'DB0101');
                        // return error response
                        throw new ApolloError(errResult.message,errResult.code);        
                    })
                );
            }catch(mainCatchError){
                console.log('Error in getOrganizationSubscribedPlan function main catch block', mainCatchError);
                let errResult = commonLib.func.getError(mainCatchError, 'DB0030');
                // return error response
                throw new ApolloError(errResult.message,errResult.code);        
            }
            finally{
                // destroy database connection
                appManagerDbConnection ? appManagerDbConnection.destroy() : null;
            }
        }
    }
}

exports.resolvers = resolvers;
