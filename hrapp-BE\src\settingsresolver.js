// require resolver files
const taxconfiguration = require('./settingsresolvers/taxconfiguration');
const empMonitoringSettings = require('./settingsresolvers/employeeMonitoringSettings');
const payrollSettings = require('./settingsresolvers/payrollSettings');
const incomeTaxDeclarationSettings = require('./settingsresolvers/incomeTaxDeclarationSettings');
const addServiceProviderDetails=require('./settingsresolvers/addServiceProviderDetails');
const listAllServiceProviderDetails=require('./settingsresolvers/listAllServiceProviderDetails');
const updateServiceProviderLocation=require('./settingsresolvers/updateServiceProviderLocation');
const checkProviderNameExists=require('./settingsresolvers/checkProviderNameExists');
const viewMoreServiceProviderDetails=require('./settingsresolvers/viewMoreServiceProviderDetails');

// Define resolver
const resolvers = {
    Query: Object.assign({},
        taxconfiguration.resolvers.Query,
        empMonitoringSettings.resolvers.Query,
        payrollSettings.Query,
        incomeTaxDeclarationSettings.resolvers.Query,
        listAllServiceProviderDetails.resolvers.Query,
        checkProviderNameExists.resolvers.Query,
        viewMoreServiceProviderDetails.resolvers.Query
    ),
    Mutation: Object.assign({},
        taxconfiguration.resolvers.Mutation,
        empMonitoringSettings.resolvers.Mutation,
        payrollSettings.Mutation,
        incomeTaxDeclarationSettings.resolvers.Mutation,
        addServiceProviderDetails.resolvers.Mutation,
        updateServiceProviderLocation.resolvers.Mutation
    )
}
exports.resolvers = resolvers;