//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make database connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require form Ids
const { formIds } = require('../../../../common/appconstants');
module.exports.retrieveDashboardExpiredDocsDetails = async (parent, args, context, info) => {
    let organizationDbConnection;

    try {
        console.log("Inside retrieveDashboardExpiredDocsDetails function()", context.Employee_Id)
        let logInEmpId = context.Employee_Id;
        let orgCode = context.Org_Code;
        let employeeIdArray = [];
        // get the organization database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let formAccessRights = await commonLib.func.checkMultipleFormAccessRights(logInEmpId, [formIds.admin, formIds.ServiceProviderAdmin], organizationDbConnection);
        let adminForm=formAccessRights.filter((data)=>(data.Form_Id===formIds.admin))
        let serviceProviderAdminForm=formAccessRights.filter((data)=>(data.Form_Id===formIds.ServiceProviderAdmin))
        if (args.selfService === 0) {
            if (adminForm && adminForm.length && adminForm[0].Role_Update !== 1) {
                if (serviceProviderAdminForm && serviceProviderAdminForm.length && serviceProviderAdminForm[0].Role_Update === 1) {
                    let serviceProviderEmployeeIds =  await commonLib.func.getServiceProviderEmpIdsForFieldForce(organizationDbConnection, logInEmpId, orgCode)
                    employeeIdArray = employeeIdArray.concat(serviceProviderEmployeeIds);
                }
                else if (adminForm.Is_Manager === 1) {
                    let getManagerAccessDetails = await commonLib.func.getManagerHierarchy(organizationDbConnection, logInEmpId);
                    employeeIdArray = employeeIdArray.concat(getManagerAccessDetails);
                }
                else {
                    employeeIdArray = [logInEmpId];
                }
            }
        } else {
            employeeIdArray = [logInEmpId];
        }
        let orgDetails = await commonLib.func.getOrgDetails(context.Org_Code, organizationDbConnection);
        let orgDateFormat = await commonLib.func.getDateFormat(orgDetails['Date_Format']);
        let {getPassportDetails, getLicenseDetails, getAccredidationDetails } = await commonLib.func.getExpiredDocuments(organizationDbConnection, 0, orgDateFormat, employeeIdArray);
        let ExpiredDocuments={
            getPassportDetails,
            getLicenseDetails,
            getAccredidationDetails
        }
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return { errorCode: "", message: "employee compliance details retrieved successfully.", ExpiredDocuments: ExpiredDocuments }



    }
    catch (e) {
        console.log('Error in the retrieveDashboardExpiredDocsDetails() function catch block. ', e);
        organizationDbConnection ? organizationDbConnection.destroy() : null;

        let errResult = commonLib.func.getError(e, 'CHR00104');
        // return response
        throw new ApolloError(errResult.message, errResult.code);
    }
}