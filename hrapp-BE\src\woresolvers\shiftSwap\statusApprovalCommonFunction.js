// require common table alias
const { ehrTables } = require('../../../common/tablealias');
const moment = require('moment');
const {formName} =  require('../../../common/appconstants');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

async function getEmployeeShiftSwapDetails(organizationDbConnection,swapId) {
    try {
        console.log('Inside getEmployeeShiftSwapDetails function: ',swapId)
        let employeeShiftSwapQuery = organizationDbConnection(ehrTables.employeeShiftSwap+' as ESS')
        .select('ESS.Employee_Id','ESS.Approver_Id','ESS.Swap_Shift_Type_Id','ESS.Swap_Date','ESS.Approval_Status','EST.Maximum_Employee_Count','EST.Status as Shift_Type_Status')
        .innerJoin(ehrTables.empShiftType + ' as EST','ESS.Swap_Shift_Type_Id','EST.Shift_Type_Id')
        .where('Swap_ID', swapId);

        const employeeShiftSwapDetailsResult = await employeeShiftSwapQuery;
        if (employeeShiftSwapDetailsResult && employeeShiftSwapDetailsResult.length) {
            let employeeShiftSwapDetails = employeeShiftSwapDetailsResult[0];
            if(employeeShiftSwapDetails.Approval_Status === 'Approved'){
                console.log('Shift swap is not in applied status in the getEmployeeShiftSwapDetails function',employeeShiftSwapDetails);
                return 'SS0159';
            }else if(employeeShiftSwapDetails.Shift_Type_Status === 'Active'){
                return employeeShiftSwapDetails;
            }else{
                console.log('Shift type detail is not active in the getEmployeeShiftSwapDetails function',employeeShiftSwapDetails);
                throw 'SS0151';
            }
        }else{
            console.log('Shift type detail does not exist in the getEmployeeShiftSwapDetails function',employeeShiftSwapDetailsResult);
            throw 'SS0152';
        }
    } catch (error) {
        console.log("Error in getEmployeeShiftSwapDetails function main catch block. ", error);
        throw error;
    }
}

async function getShiftCountByDateAndShiftId(organizationDbConnection,shiftTypeId,swapDate) {
    try {
        console.log('Inside getShiftCountByDateAndShiftId function: ',shiftTypeId,swapDate);
        let shiftCountQuery = organizationDbConnection(ehrTables.shiftEmpMapping)
        .count('Shift_Schedule_Id as scheduledShiftCount')
        .where('Shift_Start_Date', swapDate)
        .where('Shift_Type_Id', shiftTypeId);

        const shiftCount = await shiftCountQuery;

        if (shiftCount && shiftCount.length) {
            return shiftCount[0].scheduledShiftCount;
        }else{
            console.log('Empty Shift count in the getShiftCountByDateAndShiftId function.',shiftCount)
            return 0;
        }
    } catch (error) {
        console.log("Error in getShiftCountByDateAndShiftId function main catch block. ", error);
        throw error;
    }
}

async function compareMaxShiftCountWithAppliedCount(organizationDbConnection,employeeShiftSwapDetails) {
    try {
        console.log('Inside compareMaxShiftCountWithAppliedCount function: ',employeeShiftSwapDetails);
        let swapDate = employeeShiftSwapDetails.Swap_Date;
        let swapShiftTypeId = employeeShiftSwapDetails.Swap_Shift_Type_Id;
        let maxEmployeeCount = employeeShiftSwapDetails.Maximum_Employee_Count;
        
        let shiftCountForSwapShiftId = await getShiftCountByDateAndShiftId(organizationDbConnection,swapShiftTypeId,swapDate);
        shiftCountForSwapShiftId += 1; //add 1 to the count as the current employee requested to swap to this shift
        if (shiftCountForSwapShiftId <= maxEmployeeCount) {
            return true;
        }else{
            console.log("Empty response in compareMaxShiftCountWithAppliedCount function.",shiftCountForSwapShiftId);
            return 'SS0153';
        }
    } catch (error) {
        console.log("Error in compareMaxShiftCountWithAppliedCount function main catch block. ", error);
        throw error;
    }
}
async function getEmployeeCurrentShiftTypeId(organizationDbConnection,employeeId,swapDate) {
    try {
        let employeeCurrentShiftIdQuery = organizationDbConnection(ehrTables.shiftEmpMapping+' as EST')
        .select('EST.Shift_Type_Id')
        .where('EST.Shift_Start_Date', swapDate)
        .where('EST.Employee_Id', employeeId);

        const employeeCurrentShiftId = await employeeCurrentShiftIdQuery;
        if (employeeCurrentShiftId && employeeCurrentShiftId.length) {
            return employeeCurrentShiftId[0].Shift_Type_Id;
        }else {
            console.log('Employee current shift type id does not exist in the getEmployeeCurrentShiftTypeId function.',employeeCurrentShiftId)
            throw 'SS0154';
        }
    } catch (error) {
        console.log("Error in getEmployeeCurrentShiftTypeId function main catch block. ", error);
        throw error;
    }
}

async function getSwapDateShiftDetails(organizationDbConnection,swapDate) {
    try {
        console.log('Inside getSwapDateShiftDetails function.',swapDate);
        let shiftDetailsQuery = organizationDbConnection(ehrTables.shiftEmpMapping+' as EST')
        .select('EST.Employee_Id','EST.Shift_Schedule_Id','EST.Shift_Type_Id','EST.Week_Off','EST.Shift_Start_Date','EST.Shift_End_Date','EST.Rotation_Id')
        .where('EST.Shift_Start_Date', swapDate);

        const swapDateShiftDetails = await shiftDetailsQuery.then();
        if (swapDateShiftDetails && swapDateShiftDetails.length) {
            return swapDateShiftDetails;
        }else {
            console.log('Employee current shift details does not exist in the getSwapDateShiftDetails function.',swapDateShiftDetails)
            throw 'SS0154';
        }
    } catch (error) {
        console.log("Error in getSwapDateShiftDetails function main catch block. ", error);
        throw error;
    }
}

async function getMatchingShiftSwapDetails(organizationDbConnection,currentShiftTypeId,swapShiftTypeId,swapDate,employeeId) {
    try {
        let employeeShiftSwapQuery = organizationDbConnection(ehrTables.employeeShiftSwap+' as ESS')
        .pluck('ESS.Swap_ID')
        .innerJoin(ehrTables.shiftEmpMapping + ' as EST','ESS.Employee_Id','EST.Employee_Id')
        .where('ESS.Swap_Shift_Type_Id', currentShiftTypeId)
        .where('EST.Shift_Type_Id', swapShiftTypeId)
        .where('ESS.Swap_Date', swapDate)
        .where('ESS.Approval_Status', 'Applied')
        .where('EST.Shift_Start_Date', swapDate)
        const matchingShiftSwapDetails = await employeeShiftSwapQuery;
        if (matchingShiftSwapDetails && matchingShiftSwapDetails.length) {
            return matchingShiftSwapDetails;
        }else{
            console.log('Empty response in getMatchingShiftSwapDetails function.',matchingShiftSwapDetails);
            throw 'SS0153';
        }      
    } catch (error) {
        console.log("Error in getMatchingShiftSwapDetails function main catch block. ", error);
        throw error;
    }
}

async function updateShiftSwapStatus(organizationDbConnection, args, context) {
    try {
        console.log('Inside updateShiftSwapStatus function: ',args, context);

        let updateDetails = {
            'Approval_Status': args.swapStatus,
            'Updated_On': moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            'Updated_By': context.Employee_Id
        };
        let updateShiftSwapStatusQuery = organizationDbConnection(ehrTables.employeeShiftSwap)
        .update(updateDetails)
        .where('Swap_ID', args.swapId);

        let updateShiftSwapStatusResult = await updateShiftSwapStatusQuery;
        if (updateShiftSwapStatusResult) {
            return 'success';
        }else{
            console.log('Empty response in the updateShiftSwapStatus() function.',updateShiftSwapStatusResult);
            throw 'SS0157';
        }
    } catch (error) {
        console.log("Error in updateShiftSwapStatus function main catch block. ", error);
        throw error;
    }
}

//Function to create the system log entry in the table
async function createSystemLogEntry(organizationDbConnection,args,context,employeeShiftSwapDetails){
    try{
        console.log("Inside createSystemLogEntry function",args, employeeShiftSwapDetails);
        
        let { swapId, swapStatus } = args;
        let { User_Ip }= context;
        let loginEmployeeId = context.Employee_Id;
        let { Employee_Id, Swap_Date }= args.employeeShiftSwapDetails;

        let systemLogParams = {
            userIp: User_Ip,
            employeeId: loginEmployeeId,
            formName: formName.shiftSwap,
            message: "Shift swap status is "+swapStatus.toLowerCase()+" for swap ID: "+swapId+", Employee ID: "+Employee_Id+", Swap Date: "+Swap_Date,
            organizationDbConnection: organizationDbConnection,
        };

        //call the function to add the system log
        await commonLib.func.createSystemLogActivities(systemLogParams);

        return 'success';
    }catch(error){
        console.log('Error in createSystemLogEntry function main catch block', error);
        throw error;
    }
}

async function initiateSwapApproval(organizationDbConnection, args, context){
    try {
        console.log('Inside initiateSwapApproval function: ',args, context);

        await updateShiftSwapStatus(organizationDbConnection,args, context);

        await createSystemLogEntry(organizationDbConnection,args,context);

        return 'success';   
    } catch (error) {
        console.log("Error in initiateSwapApproval function main catch block. ", error);
        throw error;
    }
}

async function validateShiftSwap(organizationDbConnection, employeeShiftSwapDetails){
    try {
        console.log('Inside validateShiftSwap function: ', employeeShiftSwapDetails);

        let employeeId = employeeShiftSwapDetails.Employee_Id;
        let swapDate = employeeShiftSwapDetails.Swap_Date;
        let swapShiftTypeId = employeeShiftSwapDetails.Swap_Shift_Type_Id;
        let currentShiftTypeId = await getEmployeeCurrentShiftTypeId(organizationDbConnection,employeeId,swapDate);
        let matchingShiftSwapDetails = await getMatchingShiftSwapDetails(organizationDbConnection,currentShiftTypeId,swapShiftTypeId,swapDate,employeeId);
        return {matchingShiftSwapDetails,currentShiftTypeId};
    } catch (error) {
        console.log('Error in the validateShiftSwap function main catch block.', error);
        throw error;
    }
}

async function formShiftTypeUpdateData(employeeId, swapDateShift, swapShiftTypeId){
    try {
        console.log('Inside formShiftTypeUpdateData function.',employeeId,swapDateShift);
        const employeeShiftDetails = swapDateShift.filter(detail => detail.Employee_Id === employeeId);
        if(employeeShiftDetails && employeeShiftDetails.length){
            let { Shift_Schedule_Id, Shift_Start_Date, Shift_End_Date, Rotation_Id, Week_Off } = employeeShiftDetails[0];
            return {
                shiftSchedulingId : Shift_Schedule_Id,
                shiftTypeId : swapShiftTypeId,
                employeeId : [employeeId],
                shiftStartDate : Shift_Start_Date,
                shiftEndDate : Shift_End_Date,
                rotationId: Rotation_Id,
                weekOff : Week_Off
            };
        }else{
            console.log('Error in the formShiftTypeUpdateData function else block..',employeeShiftDetails);
            throw 'SS0154';
        }
    } catch (error) {
        console.log('Error in the formShiftTypeUpdateData function main catch block.',error);
        throw error;
    }
}
async function updateShiftTypeId(organizationDbConnection,context,updateShiftDetails,swapDate){
    try {
        console.log('Inside the updateShiftTypeId function,updateShiftDetails: ',updateShiftDetails,'swapDate:',swapDate)
        let errorMessage = {};
        let swapDateShiftResult = await getSwapDateShiftDetails(organizationDbConnection,swapDate);
        let { updateShiftScheduling } = require('../../resolvers/shiftScheduling/updateShiftScheduling');

        for(let i = 0; i < updateShiftDetails.length; i++) {
            let { employeeShiftSwapDetails } = updateShiftDetails[i];
            let { Employee_Id,Swap_Shift_Type_Id } = employeeShiftSwapDetails;

            let updateShiftMappingInput = await formShiftTypeUpdateData(Employee_Id,swapDateShiftResult,Swap_Shift_Type_Id);
            console.log("updateShiftMappingInput",updateShiftMappingInput);//remove after test
            let updateShiftTypeIdResult = await updateShiftScheduling( "", updateShiftMappingInput, context, "" )
            if(updateShiftTypeIdResult.errorCode) {
                console.log('Error while updating the shift type id in the updateShiftTypeId function,updateShiftTypeIdResult: ',updateShiftTypeIdResult);
                errorMessage[i] = updateShiftTypeIdResult.message;
            }else{    
                await initiateSwapApproval(organizationDbConnection, updateShiftDetails[i], context);
            }
        }

        if(Object.keys(errorMessage).length){
            console.log('ErrorMessage exist in the updateShiftTypeId function,errorMessage: ',errorMessage);
            let groupErrorMessage = JSON.stringify(errorMessage);
            return groupErrorMessage;
        }else{
            return 'success';
        }
    } catch (error) {
        console.log('Error in updateShiftTypeId function main catch block.', error);
        throw error;
    }
}

async function processSwap(organizationDbConnection,context,procesSwapInputs){
    try {
        console.log('Inside processSwap function, procesSwapInputs: ',procesSwapInputs);
        let { args,isShiftLimitNotExceeded, employeeShiftSwapDetails} = procesSwapInputs;

        if (isShiftLimitNotExceeded === true) {
            let updateShiftArray = [{
                swapId: args.swapId,
                swapStatus: 'Approved',
                employeeShiftSwapDetails:employeeShiftSwapDetails
            }];

            let updateShiftAndSwapResult = await updateShiftTypeId(organizationDbConnection,context,updateShiftArray,employeeShiftSwapDetails.Swap_Date);

            return { updateShiftAndSwapResult: updateShiftAndSwapResult, validateShiftSwapResult: '' };
        }else if (isShiftLimitNotExceeded === 'SS0153') {
           let {matchingShiftSwapDetails,currentShiftTypeId} = await validateShiftSwap(organizationDbConnection, employeeShiftSwapDetails);
           if(employeeShiftSwapDetails && employeeShiftSwapDetails.Swap_Shift_Type_Id===currentShiftTypeId){
            let updateShiftArray = [{
                swapId: args.swapId,
                swapStatus: 'Approved',
                employeeShiftSwapDetails:employeeShiftSwapDetails
            }];

            let updateShiftAndSwapResult = await updateShiftTypeId(organizationDbConnection,context,updateShiftArray,employeeShiftSwapDetails.Swap_Date);
            return { updateShiftAndSwapResult: updateShiftAndSwapResult, validateShiftSwapResult: '' };
           }
           return { updateShiftAndSwapResult: '', validateShiftSwapResult: matchingShiftSwapDetails };
        }else {
            throw isShiftLimitNotExceeded;
        }
    } catch (error) {
        console.log('Error in the processSwap function main catch block.', error);
        throw error;
    }
}

async function handleProcessSwapError(updateResult){
    if(updateResult.updateShiftAndSwapResult === 'success'){
        return 'success';
    }else if(updateResult.updateShiftAndSwapResult && updateResult.updateShiftAndSwapResult.length){
        console.log('updateShiftAndSwapResult block in the handleProcessSwapError function.',updateResult,updateResult.updateShiftAndSwapResult);
        return {shiftSwapResponse: updateResult.updateShiftAndSwapResult, matchingSwapIds: []};
    }else if(updateResult.validateShiftSwapResult && updateResult.validateShiftSwapResult.length){
        console.log('validateSwapStatus block in the handleProcessSwapError function.',updateResult,updateResult.validateShiftSwapResult);
        return {shiftSwapResponse: '', matchingSwapIds: updateResult.validateShiftSwapResult};
    }else if(updateResult.overlapErrorCode){
        console.log('validateSwapStatus block in the handleProcessSwapError function.',updateResult);
        return {shiftSwapResponse: '', matchingSwapIds: updateResult.validateShiftSwapResult,overlapErrorCode:updateResult.overlapErrorCode,overlapErrorMessage:updateResult.overlapErrorMessage};
    }else{
        console.log('Error in handleProcessSwapError function. updateResult: ', updateResult);
        throw 'SS0157';
    }
}

async function approvalShiftOverlapValidation(organizationDbConnection,employeeShiftSwapDetails){
    try {
        console.log('Inside approvalShiftOverlapValidation function',employeeShiftSwapDetails);
        let swapDateShiftResult = await getSwapDateShiftDetails(organizationDbConnection,employeeShiftSwapDetails.Swap_Date);
        const employeeShiftDetails = swapDateShiftResult.filter(detail => detail.Employee_Id === employeeShiftSwapDetails.Employee_Id);
        if(employeeShiftDetails && employeeShiftDetails.length){
            let overlapValidationInputs = { 
                organizationDbConnection: organizationDbConnection, 
                employeeId: employeeShiftSwapDetails.Employee_Id,
                swapDate: employeeShiftSwapDetails.Swap_Date,
                shiftTypeId: employeeShiftSwapDetails.Swap_Shift_Type_Id,
                currentShiftScheduleId: employeeShiftDetails[0].Shift_Schedule_Id
            };
            let { overlapErrorCode, overlapErrorMessage } = await validateShiftOverlap(overlapValidationInputs);

            if(overlapErrorCode){
                return {overlapErrorCode,overlapErrorMessage};
            }else{
                return {overlapErrorCode:'',overlapErrorMessage: ''};
            }
        }else{
            console.log('Employee shift details does not exist in the approvalShiftOverlapValidation function.',employeeShiftDetails)
            throw 'SS0154';
        }
    } catch (error) {
        console.log('Error in the approvalShiftOverlapValidation function main catch block.',error);
        throw error;
    }
}

//Function to evaluate and update the shift swap status
async function evaluateAndProcessSwap(organizationDbConnection, args, context){
    try {
        console.log('Inside evaluateAndProcessSwap function',args);
        let swapId = args.swapId;

        let employeeShiftSwapDetails = await getEmployeeShiftSwapDetails(organizationDbConnection,swapId);

        await triggerAttendanceLeaveCompOff(organizationDbConnection,employeeShiftSwapDetails);
      
        let isShiftLimitNotExceeded = await compareMaxShiftCountWithAppliedCount(organizationDbConnection,employeeShiftSwapDetails);
        let procesSwapInputs = { args,isShiftLimitNotExceeded, employeeShiftSwapDetails};
        
        let {overlapErrorCode,overlapErrorMessage} = await approvalShiftOverlapValidation(organizationDbConnection,employeeShiftSwapDetails);

        let updateResult = '';
        if(!overlapErrorCode){
            updateResult = await processSwap(organizationDbConnection, context, procesSwapInputs);
        }else{
            updateResult = {updateShiftAndSwapResult:'',matchingSwapIds:'',overlapErrorCode,overlapErrorMessage};
        }

        return handleProcessSwapError(updateResult);
    } catch (error) {
        console.log("Error in evaluateAndProcessSwap function main catch block. ", error);
        throw error;
    }
}

async function rejectShiftSwap(organizationDbConnection, args, context){
    try {
        console.log('Inside rejectShiftSwap function',args);
        let swapId = args.swapId;
        let employeeShiftSwapDetails = await getEmployeeShiftSwapDetails(organizationDbConnection,swapId);

        args.swapStatus = 'Rejected';
        args.employeeShiftSwapDetails=employeeShiftSwapDetails;
        await initiateSwapApproval(organizationDbConnection, args, context);

        return 'success';
    } catch (error) {
        console.log("Error in rejectShiftSwap function main catch block. ", error);
        throw error;
    }
}

async function validateAlternateSwapOverlap(organizationDbConnection,employeeShiftSwapDetails,matchingEmployeeShiftSwapDetails){
    try {
        console.log('Inside validateAlternateSwapOverlap function',employeeShiftSwapDetails,matchingEmployeeShiftSwapDetails);
        let overlapResultForSwapId = await approvalShiftOverlapValidation(organizationDbConnection,employeeShiftSwapDetails);
        if(!overlapResultForSwapId.overlapErrorCode){
            let overlapResultForMatchingSwapId = await approvalShiftOverlapValidation(organizationDbConnection,matchingEmployeeShiftSwapDetails);
            if(overlapResultForMatchingSwapId.overlapErrorCode){
                console.log('Shift overlapped for the matching swap id in validateAlternateSwapOverlap function.',overlapResultForMatchingSwapId);
                return {overlapErrorCode:overlapResultForMatchingSwapId.overlapErrorCode, overlapErrorMessage : overlapResultForMatchingSwapId.overlapErrorMessage};
            }else{
                return {overlapErrorCode:'', overlapErrorMessage : ''};
            }
        }else{
            console.log('Shift overlapped for the swap id in validateAlternateSwapOverlap function.',overlapResultForSwapId);
            return {overlapErrorCode:overlapResultForSwapId.overlapErrorCode, overlapErrorMessage : overlapResultForSwapId.overlapErrorMessage};
        } 
    } catch (error) {
        console.log('Error in the validateAlternateSwapOverlap function main catch block.',error);
        throw error;
    }
}

//Trigger the attendance, leave and compensatory off validations
async function triggerAttendanceLeaveCompOff(organizationDbConnection,shiftDetails,matchingEmployeeShiftDetails={}){
    try{
        console.log('Inside triggerAttendanceLeaveCompOff function',shiftDetails,matchingEmployeeShiftDetails);
        let { Employee_Id, Swap_Date } = shiftDetails;

        let validationInputs = {
            employeeId :Employee_Id,
            shiftDate: Swap_Date
        };
        await checkAttendanceLeaveCompOff(organizationDbConnection,validationInputs);

        if(Object.keys(matchingEmployeeShiftDetails).length){
            let matchingEmployeeValidationInputs = {
                employeeId : matchingEmployeeShiftDetails.Employee_Id,
                shiftDate: matchingEmployeeShiftDetails.Swap_Date
            };
            await checkAttendanceLeaveCompOff(organizationDbConnection,matchingEmployeeValidationInputs);
        }
        return 'success';
    }catch(error){
        console.log('Error in the triggerAttendanceLeaveCompOff function main catch block.',error);
        throw error;
    }
}
async function evaluateAndApproveAlternateSwap(
    organizationDbConnection,
    args,
    context
  ) {
    try {
        let employeeShiftSwapDetails = await getEmployeeShiftSwapDetails(organizationDbConnection,args.Swap_ID);      
        let matchingEmployeeShiftSwapDetails = await getEmployeeShiftSwapDetails(organizationDbConnection,args.Matching_Swap_ID);

        await triggerAttendanceLeaveCompOff(organizationDbConnection,employeeShiftSwapDetails,matchingEmployeeShiftSwapDetails);

        let {overlapErrorCode,overlapErrorMessage } = await validateAlternateSwapOverlap(organizationDbConnection,employeeShiftSwapDetails,matchingEmployeeShiftSwapDetails);

        if(!overlapErrorCode){
            let procesSwapInputs = { args, employeeShiftSwapDetails,matchingEmployeeShiftSwapDetails};
            await processAlternateSwap(organizationDbConnection,context,procesSwapInputs)
            return {'response':'success'};
        }else{
            return {'response': '',overlapErrorCode,overlapErrorMessage};
        }
    } catch (error) {
      console.error(
        'Error in evaluateAndApproveAlternateSwap function main catch block.',
        error
      );
      throw error
    }
  }

  async function processAlternateSwap(organizationDbConnection,context,procesSwapInputs){
    try {
        let { args, employeeShiftSwapDetails,matchingEmployeeShiftSwapDetails} = procesSwapInputs;
            let updateShiftArray = [{
                swapId: args.Swap_ID,
                swapStatus: 'Approved',
                employeeShiftSwapDetails:employeeShiftSwapDetails
            }];
            updateShiftArray.push({
                swapId: args.Matching_Swap_ID,
                swapStatus: 'Approved',
                employeeShiftSwapDetails:matchingEmployeeShiftSwapDetails
            });
            let updateShiftAndSwapResult = await updateShiftTypeId(organizationDbConnection,context,updateShiftArray,employeeShiftSwapDetails.Swap_Date);

            return { updateShiftAndSwapResult: updateShiftAndSwapResult, validateShiftSwapResult: '' };
        
    } catch (error) {
        console.log('Error in the processSwap function main catch block.', error);
        throw error;
    }
}

async function validateShiftOverlap(overlapValidationInputs){
    try{
        console.log('Inside validateShiftOverlap function',overlapValidationInputs);
        let { organizationDbConnection, employeeId, swapDate, shiftTypeId, currentShiftScheduleId }  = overlapValidationInputs;
        let { getShiftDateExistEmpId } = require('../../resolvers/shiftScheduling/weekOffCommonFunction');
        let { overlapErrorMessage } = await getShiftDateExistEmpId(organizationDbConnection, [employeeId], swapDate, swapDate,shiftTypeId, currentShiftScheduleId);
        if (overlapErrorMessage) {
            return {overlapErrorCode: 'SS0160', overlapErrorMessage : overlapErrorMessage };
        }else{
            return {overlapErrorCode: '', overlapErrorMessage : '' };
        }
    }catch(error){
        console.log('Error in the validateShiftOverlap function main catch block.',error);
        throw error;
    }
}

//Validate whether leaves or attendance or compensatory off exists for the given date
async function checkAttendanceLeaveCompOff(organizationDbConnection,validationInputs){
    try {
        console.log('Inside checkAttendanceLeaveCompOff function',validationInputs);

        let isAttendanceCompOffLeaveExist = 0;
        let {employeeId,shiftDate} = validationInputs;

        let workScheduleDetails = await commonLib.employees.getGraceTimeDetails(employeeId, null, shiftDate, organizationDbConnection);

        if(Object.keys(workScheduleDetails).length > 0){
            let { regularFrom, regularTo, considerationFrom, considerationTo } = workScheduleDetails;

            if(regularFrom && regularTo && considerationFrom && considerationTo){
                // check attendance exists for the new shift type id
                let employeeettendanceDetails = await commonLib.employees.getAttendance(shiftDate, employeeId, organizationDbConnection, 'get-count', '', workScheduleDetails);

                // If an error occured while getting the attendance details for the shift date then consider attendance exist as 1
                if (employeeettendanceDetails.errorCode) {
                    console.log('Error response from the getAttendance function in the checkAttendanceLeaveCompOff function.',employeeettendanceDetails);
                    throw 'SS0161';
                } else {
                    let isAttendanceExist = employeeettendanceDetails.attendanceDetails;

                    // Check leave already exist for this date
                    let isLeaveExists = await commonLib.employees.getLeaves(employeeId, shiftDate, shiftDate, 'update-shift', organizationDbConnection);
                    // check compensatory_off exist for this date
                    let isCompensatoryOffExists = await commonLib.employees.getCompensatoryOff(employeeId, shiftDate, shiftDate, 'update-shift', organizationDbConnection);

                    /** Return the error response when the attendance or leave or compensatory off exist. */
                    if (isLeaveExists || isCompensatoryOffExists || isAttendanceExist) {
                        console.log('Attendance or leave or compensatory-off exist in the checkAttendanceLeaveCompOff function,isLeaveExists:', isLeaveExists, 'isCompensatoryOffExists: ', isCompensatoryOffExists, ' isAttendanceExist: ', isAttendanceExist);
                        isAttendanceCompOffLeaveExist = 1;
                        throw 'SS0162';
                    } else {
                        isAttendanceCompOffLeaveExist = 0;
                        return isAttendanceCompOffLeaveExist;
                    }
                }
            }else{
                let workScheduleError = (Object.keys(workScheduleDetails).length > 0 && workScheduleDetails.errorCode) ? workScheduleDetails.errorCode : 'OT0107';

                // throw shift is not scheduled for this date
                throw (workScheduleError);//OT0128, OT0107
            }
        }else{
            console.log('Work schedule details does not exist in the checkAttendanceLeaveCompOff function.',workScheduleDetails);
            throw 'OT0107';
        }
    } catch (error) {
        console.log('Error in the checkAttendanceLeaveCompOff function main catch block.',error);
        throw error;
    }
}



module.exports = {
    evaluateAndProcessSwap,
    rejectShiftSwap,
    evaluateAndApproveAlternateSwap,
    validateShiftOverlap,
    checkAttendanceLeaveCompOff
};