type Query{
  viewAttendanceConfiguration:viewAttendanceConfigurationResponse!
}

type viewAttendanceConfigurationResponse{
    errorCode: String
    message: String
    configurationDetails: attendanceConfigurationDetails
}

type attendanceConfigurationDetails {
    employeeRegularizationCutOffDays : Int,
    employeeRegularizationRequestLimit : Int,
    attendanceApprovalCutOffDaysForManager : Int,
    updatedOn: String,
    updatedByEmployeeName: String
}

schema {
  query: Query
}
