//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make database connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError,ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
//Require constants
const { formName,systemLogs } = require('../../../common/appconstants');
//Require moment package
const moment=require('moment');
//Require axios to call an ajax api from nodejs
const axios = require('axios');

//Function to call an AJAX api to update employee leave balance
async function updateEmployeeLeaveBalance(orgCode,employeeId,dateOfJoin,probationDate){
    return new Promise(async(resolve,reject)=>{
        try{
            let url = 'https://' + orgCode + '.' + process.env.domainName + process.env.webAddress + '/employees/leaves/update-employee-leave-balance';
            // call AJAX API to update leave balance
            const config = {
                method: 'post',
                url: url,
                data: {
                    employeeId: employeeId,
                    dateOfJoin: dateOfJoin,
                    probationDate: probationDate,
                    updateProbationEmployeeLeaveBalance: 1
                }
            };
            await axios.request(config)
            .then(async function (successResponse) {
                if(successResponse && successResponse.data && successResponse.data.Success==true){
                    resolve('success');
                }else{
                    resolve('EE00003');
                }
            })
            .catch(function (errorResponse) {
                console.log("Error response from update employee leave balance API .catch block", errorResponse);
                resolve('EE00004');
            });
        } catch (updateLeaveBalanceError) {
            console.log('Error in updateEmployeeLeaveBalance() function main catch block.',updateLeaveBalanceError);
            resolve(updateLeaveBalanceError);
        }
    })    
}

//Update the probation and confirmation for the employee
module.exports.updateProbationAndConfirmation = async (parent, args, context, info) => {
    console.log("Inside updateProbationAndConfirmation() function.");
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    try{
        organizationDbConnection = knex(context.connection.OrganizationDb);
        
        // validate employee id
        if (!args.employeeId) {
            validationError['IVE0083'] = commonLib.func.getError('', 'IVE0083').message;
        }

        // validate probation date
        if (!args.dateOfJoin) {
            validationError['IVE0209'] = commonLib.func.getError('', 'IVE0209').message;
        }

        // validate old probation date
        if (!args.oldProbationDate) {
            validationError['IVE0210'] = commonLib.func.getError('', 'IVE0210').message;
        }

        // validate new probation date
        if (!args.newProbationDate) {
            validationError['IVE0208'] = commonLib.func.getError('', 'IVE0208').message;
        }

        if(Object.keys(validationError).length === 0){
            let employeeId = args.employeeId;
            let dateOfJoin = args.dateOfJoin;
            let oldProbationDate = args.oldProbationDate;
            let newProbationDate =  args.newProbationDate;
            let confirmationDate = (args.confirmationDate) ? args.confirmationDate : '';

            let updateJson = {
                Probation_Date: newProbationDate
            };

            //If confirmation date exist, update the confirmation date
            if(confirmationDate){
                updateJson.Confirmation_Date = confirmationDate;
                updateJson.Confirmed = 1;
            }

            /** Update the employment details for the the employee */
            return (
                organizationDbConnection
                .transaction(function () {
                    return (
                    organizationDbConnection(ehrTables.empJob)
                    .update(updateJson)
                    .where('Employee_Id',employeeId)
                    .then(async (updateResponse) => {
                        if(updateResponse){
                            let systemLogMessage = '', responseMessage = '';
                            if(confirmationDate){
                                systemLogMessage = '- update employment confirmation';
                                responseMessage = 'Employment confirmed successfully.';
                            }else{
                                systemLogMessage = '- extend employee probation';
                                responseMessage = 'Employee probation extended successfully.';
                            }
                            //Form inputs to update system log. Log message: Update Employees - extend employee probation - 1000117
                            let systemLogParams = {
                                action: systemLogs.roleUpdate,
                                userIp: context.User_Ip,
                                employeeId: context.Employee_Id,
                                formName: formName.employees,
                                trackingColumn: systemLogMessage,
                                organizationDbConnection: organizationDbConnection,
                                uniqueId: employeeId
                            };
                            //Call function to add the system log
                            await commonLib.func.createSystemLogActivities(systemLogParams);

                            oldProbationDate =  moment(oldProbationDate).format('YYYY-MM-DD');
                            newProbationDate =  moment(newProbationDate).format('YYYY-MM-DD');
                            //If the old probation date and new probation date are same
                            if(!(moment(oldProbationDate).isSame(newProbationDate))){
                                //Call the function to update the employee leave balance
                                let leaveBalanceUpdateResult = await updateEmployeeLeaveBalance(context.Org_Code,employeeId,dateOfJoin,newProbationDate);
                                //If the leave balance is not updated revert the old details
                                if(leaveBalanceUpdateResult !== 'success'){
                                    console.log('Response from the updateEmployeeLeaveBalance() function.',leaveBalanceUpdateResult);
                                    let revertUpdateJson = {
                                        Probation_Date: oldProbationDate
                                    };
                        
                                    //If confirmation date exist in the input, revert it
                                    if(confirmationDate){
                                        revertUpdateJson.Confirmation_Date = "";
                                        revertUpdateJson.Confirmed = 0;
                                    }
                        
                                    //Update the old job details
                                    await organizationDbConnection(ehrTables.empJob)
                                    .update(revertUpdateJson)
                                    .where('Employee_Id',employeeId)
                                    .then();

                                    throw leaveBalanceUpdateResult;
                                }
                            }

                            //Destroy database connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return success response
                            return { errorCode: '', message: responseMessage};
                        }else{
                            console.log('Unable to update the probation date or confirmation details for the employee id, ',employeeId,' as it is already updated.');
                            throw('EE00005');
                        }
                    })
                    )
                })
                .then(async (result) => {
                    return result;
                })
                .catch(function (updateCatchError) {
                    console.log('Error occurred in the updateProbationAndConfirmation() function .catch block. ',updateCatchError,' for the employee id: ',employeeId);
                    //Destroy database connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    errResult = commonLib.func.getError(updateCatchError, 'EE00101');
                    //Return error response
                    throw new ApolloError(errResult.message,errResult.code);
                })
            )

        }else{
            throw 'IVE0000';// throw validation error
        }
    }catch(mainCatchError) {
        console.log('Error in the updateProbationAndConfirmation() function main catch block. ',mainCatchError);
        //Destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            console.log('Validation error in the updateProbationAndConfirmation() function. ',validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            //Return error response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else{
            errResult = commonLib.func.getError(mainCatchError, 'EE00002');
            //Return error response
            throw new ApolloError(errResult.message,errResult.code);
        }
    }
};