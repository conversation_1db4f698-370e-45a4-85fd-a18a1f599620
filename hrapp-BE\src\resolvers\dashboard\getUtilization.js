const { ehrTables } = require('../../../common/tablealias');

/*
Here we have to find the
- (1) working days for previous months (ie) previous payslip generated months
- (2) leaves taken for previous months (ie) previous payslip generated months
- (3) working days for current month
- (4) leaves taken for current month
Then
- (5) total working days = working days for previous months(1) + working days for current month(3)
- (6) total leaves taken = leaves taken for previous months(2) + leaves taken for current month(4)
- (7) total worked days = total working days(5) - total leaves taken(6)

current month working days and worked days also be used to show the current month statistics
*/
module.exports.getUtilization = async(parent, args, context, info) => {
    console.log("In getUtilization function");
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // Organization database connection
    const knex = require('knex');
    // get the organization data base connection
    let orgDb = knex(context.connection.OrganizationDb);
    // get constants
    const { defaultValues } = require('../../../common/appconstants');    
    let moment = require('moment-timezone');

    try
    {
        let logInEmpId = context.Employee_Id;

        let orgCode=context.Org_Code;

        let payslipMonth=1;

        let timezone = await commonLib.func.getEmployeeTimeZone(logInEmpId, orgDb, 0);

        let todayDate = moment().tz(timezone).format('YYYY-MM-DD');
        
        let currentYear= parseInt(moment(todayDate).year());
        let currentMonth = parseInt(moment(todayDate).format('MM'));

        let salaryDate=await commonLib.func.getSalaryDay(orgCode,orgDb,payslipMonth,currentYear);
        
        // get leave closure start date
        
        let leaveClosureStartDate=salaryDate.Salary_Date;

        if(!leaveClosureStartDate)
        {
            return{
                errorCode: null,
                message: 'Leave closure dates not updated',
                utilization: null
            }
        }
        let year = moment(leaveClosureStartDate,"YYYY-MM-DD").year();

        //Form month and year for previous month to get the business working from table
        let previousMonthYear = [];
        //If current month is jan, previous month array should be empty
        if(currentMonth == 1){
            previousMonthYear = [];
        }else{
            let monthNameInArray = defaultValues.monthNumbersArray;
            //Iterate all the months from 1 to 12
            for(let i=0; i<monthNameInArray.length;i++) { 
                //Push month,year in an array before the current month
                if(monthNameInArray[i] < currentMonth){
                    previousMonthYear.push( monthNameInArray[i] + ',' + year); 
                }else{
                    break;
                }
            }
        }
        let previousMonthBusinessWorkingDays = 0;
        // If the previous years is present then take the working days taken from the table
        if(previousMonthYear.length > 0)
        {
            previousMonthBusinessWorkingDays = await orgDb.sum('Emp_Business_Working_Days as days')
                                                                .from(ehrTables.salaryPayslip)
                                                                .where('Employee_Id', logInEmpId)
                                                                .whereIn('Salary_Month', previousMonthYear)
                                                                .first()
                                                                .then((res)=>{
                                                                    return res.days ? res.days : 0
                                                                })
        }

        
        // To get the paycycle start date and end date of current month
        let currentMonthSalaryDates = await commonLib.func.getSalaryDay(orgCode,orgDb,currentMonth,currentYear,todayDate);
        startDate = currentMonthSalaryDates.Salary_Date;

        // If we want to calculate business working days based on workschedule then we have to send the totalWorkingDays as null and formName as leaves
        let currentMonthBusinessWorkingDays = await commonLib.payroll.getBusinessWorkingDays(orgDb, logInEmpId, startDate, todayDate, 1,null,'leaves');

        let totalBusinessWorkingDays = previousMonthBusinessWorkingDays + currentMonthBusinessWorkingDays;

        let leaveStatistics = {};

        //get the leave ids which are used by an employee
        let usedLeaveTypeIds = await orgDb.select('L.Leave_Name','L.Leave_Calculation_Days')
                                            .distinct('L.LeaveType_Id')
                                            .from(ehrTables.leavetype + ' as L')
                                            .innerJoin(ehrTables.empLeaves + ' as EL', 'EL.LeaveType_Id', 'L.LeaveType_Id')
                                            .where('EL.Employee_Id', logInEmpId)
                                            .where('EL.Start_Date', '>=', leaveClosureStartDate)
                                            .where('EL.End_Date', '<=', todayDate)
        let totalLeavesTaken = 0;

        let currentMonthTotalLeaveTaken = 0;
        for(leaveType of usedLeaveTypeIds)
        {
            let previousMonthLeaveCount = await getLeavesCountFromPayslip(orgDb, previousMonthYear, leaveType.LeaveType_Id, logInEmpId)

            let currentMonthLeaveCount = await commonLib.employees.getLeavesTaken(orgDb, orgCode, startDate, todayDate, logInEmpId, leaveType.LeaveType_Id,leaveType.Leave_Calculation_Days);
            currentMonthTotalLeaveTaken += currentMonthLeaveCount;
            totalLeavesTaken += previousMonthLeaveCount + currentMonthLeaveCount;

            if(previousMonthLeaveCount + currentMonthLeaveCount != 0)
            {
                leaveStatistics[leaveType.Leave_Name] = (((previousMonthLeaveCount + currentMonthLeaveCount) / totalBusinessWorkingDays) * 100).toFixed(2);
            }
        }
        
        let totalWorkedDays = totalBusinessWorkingDays - totalLeavesTaken;
        totalWorkedDays = (totalWorkedDays > 0) ? totalWorkedDays : 0;

        let currentMonthWorkedDays = currentMonthBusinessWorkingDays - currentMonthTotalLeaveTaken;
        currentMonthWorkedDays = (currentMonthWorkedDays > 0) ? currentMonthWorkedDays : 0;
        
        let utilizationPercentage = (!totalBusinessWorkingDays) ? 0 : ((totalWorkedDays / totalBusinessWorkingDays) * 100).toFixed(2)

        return{
            errorCode: null,
            message: 'Utilization retrieved successfully',
            utilization: JSON.stringify({
                            totalBusinessWorkingDays,
                            totalWorkedDays,
                            currentMonthBusinessWorkingDays,
                            currentMonthWorkedDays,
                            utilizationPercentage,
                            leaves : leaveStatistics
                        })
        }
    }
    catch(utilizationErr)
    {
        console.log('Error in getUtilization() function main catch block.',utilizationErr);
        // access denied error
        if (utilizationErr === 'DB0022') 
        {
            errResult = commonLib.func.getError('', 'DB0022');
        } 
        else
        {
            errResult = commonLib.func.getError('', 'DB0021');
        }
        // return response
        throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, utilization:""}));
    }
}

async function getLeavesCountFromPayslip(orgDb, previousMonthYear, leaveTypeId, employeeId)
{
    if(previousMonthYear[0])
    {
        return await orgDb.sum('MLB.Leaves_Taken as leavesTaken')
                                                .from(ehrTables.monthlyLeaveBalance + ' as MLB')
                                                .leftJoin(ehrTables.salaryPayslip + ' as SP', 'MLB.Payslip_Id', 'SP.Payslip_Id')
                                                .where('SP.Employee_Id', employeeId)
                                                .where('MLB.LeaveType_Id', leaveTypeId)
                                                .whereIn('SP.Salary_Month', previousMonthYear)
                                                .first()
                                                .then((res)=>{
                                                    return res.leavesTaken ? res.leavesTaken : 0;
                                                })
    }
    else
    {
        return 0;
    }
}