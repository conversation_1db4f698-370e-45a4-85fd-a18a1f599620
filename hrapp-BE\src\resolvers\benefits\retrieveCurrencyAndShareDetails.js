// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../../common/appconstants');
// require table alias function
const { ehrTables } = require('../../../common/tablealias');

// variable declarations
let errResult ={};
let organizationDbConnection = '';

// resolver definition
const resolvers = {
    Query:{
        // function to retrieve the currency details
        retrieveCurrencyDetails:async (parent, args, context, info)=>{
            try{
                console.log('Inside retrieveCurrencyDetails function');
                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                let logInEmpId = context.Employee_Id;
                // Check ESOP form view access rights exist for employee or not
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.esop, '', 'UI');   
                if(Object.keys(checkRights).length>0 && checkRights.Role_View === 1) {
                    // Check benefits admin form - edit access rights exist for employee or not
                    let checkBenefitsAdminRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.benefitsAdmin, '', 'UI');
                    if(Object.keys(checkBenefitsAdminRights).length>0 && checkBenefitsAdminRights.Role_Update === 1) {
                        return(
                            organizationDbConnection
                            .select('Currency_Id as currencyId','Currency_Code as currencyCode')
                            .from(ehrTables.currency)
                            .then(getCurrencyDetails => {
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: '', message: 'Currency details retrieved successfully.' , currencyDetails:getCurrencyDetails};
                            })
                            .catch(function (catchError) {
                                console.log('Error in retrieveCurrencyDetails function .catch block',catchError);
                                errResult = commonLib.func.getError(catchError, 'BES0101');
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                throw new ApolloError(errResult.message,errResult.code);
                            })
                        );
                    }
                    else{
                        throw '_DB0109';
                    }
                }
                else{
                    console.log('User does not have view access to ESOP form');
                    throw ('_DB0100');
                }
            }catch(mainCatchError){
                console.log('Error in retrieveCurrencyDetails function main catch block.', mainCatchError);
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                // get the errorCode and message
                errResult = commonLib.func.getError(mainCatchError, 'BES0001');
                // return response
                throw new ApolloError(errResult.message,errResult.code);
            }
        },
        // function to retrieve the currency and value of 1 share
        retrieveCurrencyAndShareValue:async (parent, args, context, info)=>{
            try{
                console.log('Inside retrieveCurrencyAndShareValue function');
                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                let logInEmpId = context.Employee_Id;
                // Check ESOP form view access rights exist for employee or not
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.esop, '', 'UI');   
                if((Object.keys(checkRights).length>0) && (checkRights.Role_View === 1)) {
                    // get the currency code and value of share from configuration table
                    return(
                        organizationDbConnection(ehrTables.benefitsConfiguration+ ' as BC')
                        .select('CU.Currency_Code as currencyCode','BC.Value_Of_Share as shareValue','BC.Currency_Id as currencyId')
                        .innerJoin(ehrTables.currency+ ' as CU','BC.Currency_Id','CU.Currency_Id')
                        .then(getDetails => {
                            // destroy database connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: '', message: 'Currency and share value details retrieved successfully.' , currencyAndShareValue:getDetails};
                        })
                        .catch(function (catchError) {
                            console.log('Error in retrieveCurrencyAndShareValue function .catch block',catchError);
                            errResult = commonLib.func.getError(catchError, 'BES0102');
                            // destroy database connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            throw new ApolloError(errResult.message,errResult.code);
                        })
                    );
                }
                else{
                    console.log('User does not have view access to ESOP form');
                    throw ('_DB0100');
                }
            }catch(mainCatchError){
                console.log('Error in retrieveCurrencyAndShareValue function main catch block.', mainCatchError);
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                errResult = commonLib.func.getError(mainCatchError, 'BES0002');
                // return response
                throw new ApolloError(errResult.message,errResult.code);
            }
        }        
    }
};

exports.resolvers = resolvers;
