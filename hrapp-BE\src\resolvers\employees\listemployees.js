// require knex package
const knex = require('knex');
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require file to access constant values
const constants = require('../../../common/appconstants');

// resolver definition
const resolvers = {
    Query: {
        // Get employee details list
        employeeDirectoryList: async (parent, args, context, info) => {
            let organizationDbConnection='';
            try {
                console.log('Inside employee directory list function');
                // make database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                // variable declarations
                let loggedInEmpId=context.Employee_Id;
                let orgCode=context.Org_Code;
                let filterList={};

                // Check whether the employee has access to employee directory form
                // format the date of joining based on org dateformat
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection,loggedInEmpId,constants.formName.employeeDirectory, constants.roles.roleView);
                if (checkRights == true) {
                    // formation of filter json
                    filterList['departmentId']  = args.departmentId;
                    filterList['designationId'] = args.designationId;
                    filterList['empTypeId']     = args.empTypeId;
                    filterList['locationId']    = args.locationId;

                    // get org date format from org details table
                    let {Date_Format}=await commonLib.func.getOrgDetails(orgCode,organizationDbConnection,0);

                    // formation of date format
                    let orgDateFormat=await commonLib.func.getDateFormat(Date_Format);
                    
                    // function to get employeedetails based on filter condition
                    let employeeData=await commonLib.func.getAllEmployeeDetails(organizationDbConnection,'employeeDirectoryList',orgCode,process.env.domainName,process.env.region,process.env.hrappProfileBucket,filterList,orgDateFormat);
                    if(employeeData.length>0)
                    {
                        // iterate the loop to get manager name and date of join date in organization format
                        for (let empRecord of employeeData){
                            if(empRecord.manager_id){
                                empRecord.manager_name=await commonLib.func.getManagerName(organizationDbConnection,empRecord.manager_id);
                            }
                        }
                        // destroy database connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return{ errorCode:'', message:'Employees list retrieved successfully.', employeesList:employeeData };
                    }
                    else{
                        // destroy database connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return{ errorCode:'', message:'No employees found.', employeesList:[] };
                    }                                       
                }
                else if (checkRights === false) {
                    // throw error if view rights is not exists
                    throw '_DB0100';
                } else {
                    // throw error
                    throw (checkRights);
                }
            } catch (error) {
                console.log('Error in employeeDirectoryList function main catch block',error);
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                // get the code and message from common function based on returned error code
                let errResult = commonLib.func.getError(error, 'PR0012');
                // return error response
                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, employeesList : []}));                                
            }
        },
        // Function to list employee details based on access rights
        listEmployeeDetails: async (parent, args, context, info) => {
            // variable declaration
            let organizationDb='';
            try{
                console.log('Inside listEmployeeDetails function.');
                // make database connection
                organizationDb = knex(context.connection.OrganizationDb);

                // variable declarations
                let filterList={};
                let employeeIdsArray=[];    
                let employeeId=context.Employee_Id;
                let orgCode=context.Org_Code;
                let isAdmin=0;

                // check whether the employee has access to employee details form
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDb, employeeId, constants.formName.userAccounts, '', 'UI');

                // check checkRights json is empty or not
                if (Object.keys(checkRights).length <= 0) {
                    throw '_DB0001';
                } else {
                    // check whether the logged in employee has admin access
                    // if employee has admin access then we need to list all the employee details
                    if (checkRights.Employee_Role.toLowerCase() === 'admin') {
                        isAdmin = 1;
                    }
                    // check whether the logged employee is manager. 
                    // manager access - then return the employee details of employee under this managerid
                    // if loggedIn employee is not admin or manager then show only details corresponding to this employeeid
                    else if(checkRights.Role_View === 1 && !isAdmin)
                    {
                        // push the employeeid inside an array
                        employeeIdsArray.push(employeeId);
                        // if employee is manager get employees under this managerId
                        if(checkRights.Is_Manager){
                            let getManagerAccessDetails = await commonLib.func.getEmpIdBasedOnManagerId(organizationDb,orgCode,employeeId);
                            employeeIdsArray=employeeIdsArray.concat(getManagerAccessDetails);
                        }
                    }
                    // throw access denied error
                    else
                    {
                        throw '_DB0100';
                    }

                    // formation of filterJson based on which details need to be retrieved
                    filterList['departmentId']  = args.departmentId;
                    filterList['designationId'] = args.designationId;
                    filterList['empTypeId']     = args.empTypeId;
                    filterList['locationId']    = args.locationId;
                    filterList['workScheduleId']= args.workScheduleId;
                    filterList['employeeId']    = employeeIdsArray;
                    // function to get employeedetails based on filter condition
                    let getUserDetails=await commonLib.func.getAllEmployeeDetails(organizationDb,'listEmployeeAndSalaryConfigDetails',orgCode,process.env.domainName,process.env.region,process.env.hrappProfileBucket,filterList);
                    if(getUserDetails.length>0)
                    {
                        let filterJson={};
                        // iterate the loop to get manager name and date of join date in organization format
                        for (let userRecord of getUserDetails){
                            // check whether addedby and updateby if exist then return the empname and user defined empid
                            if(userRecord.added_by){
                                filterJson['employeeId']= userRecord.added_by;
                                let userProfile=await commonLib.func.getAllEmployeeDetails(organizationDb,'getUserIdAndName','','','','',filterJson);
                                userRecord.added_by_emp_name=(userProfile.length>0) ? userProfile[0].employee_name : '';
                                userRecord.added_by_emp_id=(userProfile.length>0) ? userProfile[0].user_defined_empid : '';
                            }
                            if(userRecord.updated_by){
                                filterJson['employeeId']= userRecord.updated_by;
                                let userProfile=await commonLib.func.getAllEmployeeDetails(organizationDb,'getUserIdAndName','','','','',filterJson);
                                userRecord.updated_by_emp_name=(userProfile.length>0) ? userProfile[0].employee_name : '';
                                userRecord.updated_by_emp_id=(userProfile.length>0) ? userProfile[0].user_defined_empid : '';
                            }
                            if(userRecord.salary_config_added_by){//
                                filterJson['employeeId']= userRecord.salary_config_added_by;
                                let userProfile=await commonLib.func.getAllEmployeeDetails(organizationDb,'getUserIdAndName','','','','',filterJson);
                                userRecord.salary_config_added_by_emp_name=(userProfile.length>0) ? userProfile[0].employee_name : '';
                                userRecord.salary_config_added_by_emp_id=(userProfile.length>0) ? userProfile[0].user_defined_empid : '';
                            }
                            if(userRecord.salary_config_updated_by){
                                filterJson['employeeId']= userRecord.salary_config_updated_by;
                                let userProfile=await commonLib.func.getAllEmployeeDetails(organizationDb,'getUserIdAndName','','','','',filterJson);
                                userRecord.salary_config_updated_by_emp_name=(userProfile.length>0) ? userProfile[0].employee_name : '';
                                userRecord.salary_config_updated_by_emp_id=(userProfile.length>0) ? userProfile[0].user_defined_empid : '';
                            }
                        }
                    }
                    
                    // destroy database connection
                    organizationDb ? organizationDb.destroy() : null;
                    // return response
                    return{
                        errorCode:'',
                        message:'Employee details listed successfully.',
                        employeeDetailsList:getUserDetails
                    }
                }
            }
            catch(mainCatchError)
            {
                console.log('Error in listEmployeeDetails function main catch block',mainCatchError);
                // destroy database connection
                organizationDb ? organizationDb.destroy() : null;
                // get the code and message from common function based on returned error code
                let errResult = commonLib.func.getError(mainCatchError, 'DB0016');
                // return error response
                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, employeeDetailsList : []}));                                
            }
        }
    }
};


module.exports.resolvers = resolvers;
module.exports.commonLib = commonLib;
