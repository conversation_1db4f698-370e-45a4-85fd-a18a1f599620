// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
// Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formIds } = require('../../../common/appconstants');

module.exports.listshiftSwapping = async (parent, args, context, info) => {
  let organizationDbConnection;
  let reportees = [];

  try {
    console.log('Inside listshiftSwapping function.');

    const employeeId = context.Employee_Id;
    const accessFormId = args.formId;

    // Initialize the database connection
    organizationDbConnection = knex(context.connection.OrganizationDb);

    // Check the employee's rights
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      employeeId,
      null,
      '',
      'UI',
      false,
      accessFormId
    );

    // Check for valid access based on formId and employee rights
    if (Object.keys(checkRights).length > 0 &&
        ((checkRights.Role_View === 1 && args.formId === 306) ||
        (args.formId === 307 && checkRights.Is_Manager === 1))) {

      // Manager-specific logic for formId 307
      if (checkRights.Is_Manager === 1  && args.formId === 307) {
        const getManagerAccessDetails = await commonLib.func.getManagerHierarchy(organizationDbConnection, employeeId);
        reportees = reportees.concat(getManagerAccessDetails);
        if(reportees && reportees.length){
        reportees = [...new Set(reportees)];
        }
      }

      // Query for shift swapping data if formId is 307 and there are reportees
      const result = (args.formId === 307 && reportees.length > 0 || args.formId === 306) 
        ? await organizationDbConnection(ehrTables.employeeShiftSwap + ' as ESS')
          .select(
            'ESS.Swap_Id',
            'ESS.Employee_Id',
            'ESS.Approver_Id',
            'ESS.Swap_Shift_Type_Id',
            'ESS.Swap_Date',
            'ESS.Approval_Status',
            'ESS.Added_On',
            'ESS.Added_By',
            'ESS.Updated_On',
            'ESS.Updated_By',
            'WS.Title as Shift_Name',
            'SEM.Shift_Type_Id AS Current_Shift_Type_Id',
            'WS1.Title AS Current_Shift_Name',
            'ESS.Reason',
            organizationDbConnection.raw("CONCAT(emp.Emp_First_Name, ' ', emp.Emp_Last_Name) AS Employee_Name"),
            organizationDbConnection.raw("CONCAT(emp1.Emp_First_Name, ' ', emp1.Emp_Last_Name) AS Added_By_Name"),
            organizationDbConnection.raw("CONCAT(emp2.Emp_First_Name, ' ', emp2.Emp_Last_Name) AS Updated_By_Name"),
            organizationDbConnection.raw("CONCAT(emp3.Emp_First_Name, ' ', emp3.Emp_Last_Name) AS Approver_Name"),
            organizationDbConnection.raw('(CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE ESS.Employee_Id END) as User_Defined_EmpId')
          )
          .leftJoin(ehrTables.empPersonalInfo + ' as emp', 'emp.Employee_Id', 'ESS.Employee_Id')
          .leftJoin(ehrTables.empJob + ' as EJ', 'emp.Employee_Id', 'EJ.Employee_Id')
          .leftJoin(ehrTables.empPersonalInfo + ' as emp1', 'emp1.Employee_Id', 'ESS.Added_By')
          .leftJoin(ehrTables.empPersonalInfo + ' as emp2', 'emp2.Employee_Id', 'ESS.Updated_By')
          .leftJoin(ehrTables.empPersonalInfo + ' as emp3', 'emp3.Employee_Id', 'ESS.Approver_Id')
          .leftJoin(ehrTables.empShiftType + ' as ST', 'ST.Shift_Type_Id', 'ESS.Swap_Shift_Type_Id')
          .leftJoin(ehrTables.workSchedule + ' as WS', 'WS.WorkSchedule_Id', 'ST.WorkSchedule_Id')
          .leftJoin(ehrTables.shiftEmpMapping + ' as SEM', function() {
            this.on('SEM.Shift_Start_Date', '=', 'ESS.Swap_Date')
                .andOn('SEM.Employee_Id', '=', 'ESS.Employee_Id');
        })
          .leftJoin(ehrTables.empShiftType + ' as ST1', 'ST1.Shift_Type_Id', 'SEM.Shift_Type_Id')
          .leftJoin(ehrTables.workSchedule + ' as WS1', 'WS1.WorkSchedule_Id', 'ST1.WorkSchedule_Id')
          .modify(function (queryBuilder) {
            if (reportees && reportees.length > 0 && args.formId === 307) {
              queryBuilder.andWhereNot("ESS.Employee_Id", employeeId);
              queryBuilder.whereIn("ESS.Employee_Id", reportees);
              queryBuilder.where('ESS.Approval_Status', 'Applied');
              if (args.swapIdList && args.swapIdList.length) {
                queryBuilder.whereIn('ESS.Swap_Id', args.swapIdList);
              }
            } else if (args.formId === 306) {
              queryBuilder.where("ESS.Employee_Id", employeeId);
            }
          })
      : [];

      // Clean up the database connection
      await organizationDbConnection.destroy();

      return {
        errorCode: null,
        message: "Shift swapping data retrieved successfully",
        shiftSwapping: result
      };

    } else {
      if (Object.keys(checkRights).length > 0 && args.formId === 307) {
        console.log('The employee does not have admin or manager access.');
        throw '_DB0114';
      }
      throw '_DB0100';
    }

  } catch (e) {
    if (organizationDbConnection) {
      await organizationDbConnection.destroy();
    }
    console.log('Error in listshiftSwapping function main catch block.', e);
    const errResult = commonLib.func.getError(e, 'ESS0049');
    throw new ApolloError(errResult.message, errResult.code);
  }
};