// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
// require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName,systemLogs,defaultValues } = require('../../../common/appconstants');
// require table alias
const { ehrTables } = require('../../../common/tablealias');
// require pms validation function
const {addEmployeeGoalsAndAchievementInputValidation} = require('../../../common/performanceManagementValidation');

/**
 * Function to insert the assessment month,year employee performance in the assessment month performance table and the respective goals in
 * the assessment month goals ratings table.
*/
async function insertGoalsAndAchievement(organizationDbConnection,args,performanceEmpId,loginEmpId,empCurrentDateTime){
    try{
        // form the employee performance details
        let performanceGoalAchievementInput = {
            Employee_Id: performanceEmpId,
            Performance_Assessment_Month: args.month,
            Performance_Assessment_Year: args.year,
            Reviewer_Employee_Id: args.reviewerId,
            Assessment_Status: defaultValues.goalsAndAchievementAssessmentStatus,
            Goal_Publish_Status: defaultValues.goalsAndAchievementGoalUnPublishStatus,
            Rating_Publish_Status: null,
            Overall_Rating: 0,
            Average_Yearly_Rating: 0,
            Comments: null,
            Added_On: empCurrentDateTime,
            Added_By: loginEmpId
        };

        return (
        organizationDbConnection
        .transaction(function (trx) {
            // insert the employee performance details
            return(
            organizationDbConnection(ehrTables.performanceGoalAchievement)
            .insert(performanceGoalAchievementInput)
            .transacting(trx)
            .then(async(insertRecord) => {
                let empPerformanceId = insertRecord[0];//unique id

                // form the employee goals details
                let assessmentGoalsInput = args.goalIds.map(field=>({
                    Performance_Assessment_Id: empPerformanceId,
                    Goal_Id: field,
                    Rating: 0,
                    Added_On: empCurrentDateTime,
                    Added_By: loginEmpId
                }))

                // insert the employee goals details
                return(
                organizationDbConnection(ehrTables.assessmentCycleGoalRatings)
                .insert(assessmentGoalsInput)
                .transacting(trx)
                .then(() => {
                    return 'success';
                }))
            }))
        })
        .then(() => {
            return 'success';
        })
        .catch(insertCatchError => {
            console.log('Error occured in the insertGoalsAndAchievement() function .catch block. ',insertCatchError,' for the employee id: ',performanceEmpId);
            return insertCatchError;
        }));
    }catch(insertMainCatchError){
        console.log('Error occured in the insertGoalsAndAchievement() function main catch block. ',insertMainCatchError,' for the employee id: ',performanceEmpId);
        return insertMainCatchError;
    }
}

// Add employee performance and goals for the assessment month and year
module.exports.addEmployeeGoalsAndAchievement = async (parent, args, context, info) => {
    console.log("Inside addEmployeeGoalsAndAchievement() function.");
    
    let organizationDbConnection;
    let errResult;
    let validationError = {};

    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const loginEmpId = context.Employee_Id;

        // Check Goals and achievement form access rights.
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmpId, formName.goalsAndAchievement, '', 'UI');

        // If the login employee is admin or manager and having add access for the 'Goals and achievement' form
        if ((Object.keys(checkRights).length >0 && checkRights.Role_Add===1) && (checkRights.Is_Manager===1 || checkRights.Employee_Role==='admin')) {
            // function to validate inputs
            validationError=await addEmployeeGoalsAndAchievementInputValidation(args);
            // check input validation error exist or not
            if(Object.keys(validationError).length ===0){
                // get the login employee current date and time based on login employee location
                let empCurrentDateTime = await commonLib.func.getEmployeeTimeZone(loginEmpId, organizationDbConnection, 1);

                let inputEmpIds = args.employeeIds;

                let addEmpPerformanceGoalsResponse;
                let insertionSuccessCount = 0;
                //Compare input employee ids already exist or not in the table and return the employee id which does not exist for the performance month and year in the table
                let addEmployeeIds = await
                organizationDbConnection(ehrTables.performanceGoalAchievement)
                .pluck('Employee_Id')
                .whereIn('Employee_Id',inputEmpIds)
                .where('Performance_Assessment_Month',args.month)
                .where('Performance_Assessment_Year',args.year)
                .then(async(existingEmployeeIds) => {
                    if(existingEmployeeIds.length > 0){
                        return inputEmpIds.filter((o) => existingEmployeeIds.indexOf(o) === -1);
                    }else{
                        return inputEmpIds;
                    }
                });

                inputEmpIds=addEmployeeIds;
                let totalRecordCount = inputEmpIds.length;

                if(inputEmpIds.length >0){
                    // iterate the employee id to insert the performance and goals details in the table
                    for(let employeeId of addEmployeeIds){
                        // call the function to insert performance goals and achievement and assessment goals ratings table
                        addEmpPerformanceGoalsResponse = await insertGoalsAndAchievement(organizationDbConnection,args,employeeId,loginEmpId,empCurrentDateTime);
                        // if the performance and goals details are inserted
                        if(addEmpPerformanceGoalsResponse === 'success'){
                            insertionSuccessCount += 1;
                        }
                    }

                    /** If any of the employee performance record(s) are inserted */
                    if(insertionSuccessCount > 0){
                        let responseErrorCode;

                        // function to add the system logs
                        let systemLogParams = {
                            action: systemLogs.roleAdd,
                            userIp: context.User_Ip,
                            employeeId: loginEmpId,
                            formName: formName.goalsAndAchievement,
                            trackingColumn: '',
                            organizationDbConnection: organizationDbConnection,
                            uniqueId: ''
                        };

                        /** If the all the employee(s) - performance and goals are inserted */
                        if(insertionSuccessCount === totalRecordCount){
                            //Log message Ex: 'Add employee(s) Goals and achievement'
                            systemLogParams.message = systemLogs.roleAdd+ ' employee(s) ' + formName.goalsAndAchievement;
                        }else{
                            responseErrorCode = 'EPM0111';//records are partially inserted
                            //Log message Ex: 'Employee(s) Goals and achievement added partially'
                            systemLogParams.message = 'Employee(s) ' +formName.goalsAndAchievement + ' added partially';
                        }

                        // call function to add the system log
                        await commonLib.func.createSystemLogActivities(systemLogParams);

                        // destroy database connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;

                        /** If the records are inserted partially */
                        if(responseErrorCode){
                            throw responseErrorCode;
                        }else{
                            return { errorCode: '', message: 'Employee(s) goals and achievement are added successfully.'};
                        }
                    }else{
                        throw 'EPM0110';//throw records are not inserted
                    }
                }else{
                    console.log('No employees found to add the goals and achievement',inputEmpIds);
                    throw '_EC0008';
                }
            }else{
                throw 'IVE0000';//throw validation error
            }
        }else{
            throw('_DB0101');//throw add access not exist
        }
    }
    catch(addEmployeesPerformanceGoalsMainCatchErr) {
        console.log('Error in the addEmployeeGoalsAndAchievement() function main catch block. ',addEmployeesPerformanceGoalsMainCatchErr);
        // destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        // if validation error exist
        if (addEmployeesPerformanceGoalsMainCatchErr === 'IVE0000') {
            console.log('Validation error in the addEmployeeGoalsAndAchievement() function',validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });// return response
        } else{
            errResult = commonLib.func.getError(addEmployeesPerformanceGoalsMainCatchErr, 'EPM0010');
            throw new ApolloError(errResult.message,errResult.code);// return response
        }
    }
};