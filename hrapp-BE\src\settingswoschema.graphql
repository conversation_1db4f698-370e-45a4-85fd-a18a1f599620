type Query{
  viewAttendanceConfiguration:viewAttendanceConfigurationResponse!
}

type Mutation {
    updateAttendanceConfiguration(
        employeeRegularizationCutOffDays : Int!
        employeeRegularizationRequestLimit : Int!
        attendanceApprovalCutOffDaysForManager : Int!
    ) : successResponse!
}

type viewAttendanceConfigurationResponse{
    errorCode: String
    message: String
    configurationDetails: attendanceConfigurationDetails
}

type attendanceConfigurationDetails {
    employeeRegularizationCutOffDays : Int,
    employeeRegularizationRequestLimit : Int,
    attendanceApprovalCutOffDaysForManager : Int
}

type successResponse {
   errorCode : String,
   message : String
}

schema {
  query: Query,
  mutation : Mutation
}
