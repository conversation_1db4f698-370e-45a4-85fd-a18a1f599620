// resolver function to retrieve the states list based on the country code
module.exports.listStates = async (parent, args, context, info) => {
    console.log('Inside listStates() function.');
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // require knex package
    const knex = require('knex');
    // require apollo server to return error message
    const { UserInputError,ApolloError } = require('apollo-server-lambda');
    // require table names
    const { ehrTables } = require('../../../common/tablealias');
    // require common input validation
    const commonValidation = require('../../../common/commonvalidation');

    // variable declarations
    let errResult = {};
    let organizationDbConnection = '';
    let validationError={};

    try{
        //Validate country code
        if(args.countryCode){
            let validateCountryCode = commonValidation.checkLength(args.countryCode,2,2); // country code length validation
            if (!validateCountryCode) {
                validationError['IVE0129'] = commonLib.func.getError('', 'IVE0129').message2;
            }
        }else{
            validationError['IVE0129'] = commonLib.func.getError('', 'IVE0129').message1;
        }

        if (Object.keys(validationError).length === 0) {
            //Make organization database connection
            organizationDbConnection = knex(context.connection.OrganizationDb);

            //Get the state list in the ascending order based on the country code
            return(
                organizationDbConnection(ehrTables.state)
                .select('State_Code as stateCode','State_Name as stateName')
                .where('Country_Code',args.countryCode)
                .orderBy('State_Name','ASC')
                .then(getStateDetails=>{
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return {errorCode:'',message:'States are retrieved successfully.',listStateDetails:getStateDetails};
                })
                .catch(function (catchError) {
                    console.log('Error in listStates() function .catch block',catchError);
                    errResult = commonLib.func.getError(catchError, 'EED0001');
                    //Destroy database connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    //Throw error response to UI
                    throw new ApolloError(errResult.message,errResult.code);
                })            
            );
        }else{
            throw('IVE0000');//Throw validation error
        }
    }
    catch (mainCatchError){
        console.log('Error in listStates() function main catch block.',mainCatchError);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        //Check input validation error or not
        if (mainCatchError === 'IVE0000') {
            console.log('Validation error in the listStates() function.',validationError);
            errResult = commonLib.func.getError('', mainCatchError);
            //Throw validation error response to UI
            throw new UserInputError(errResult.message, { validationError: validationError });
        }else{
            errResult = commonLib.func.getError(mainCatchError, 'EED0101');
            throw new ApolloError(errResult.message, errResult.code);//Throw error response to UI
        }
    }
};