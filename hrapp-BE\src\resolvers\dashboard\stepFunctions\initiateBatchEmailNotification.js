'use strict';
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex for database connection
const knex = require('knex'); 
//Require file to access constant values
const { defaultValues } = require('../../../../common/appconstants');
//Require table alias
const { appManagerTables }=require('../../../../common/tablealias');
//Require moment package
const moment=require('moment');

module.exports.initiateBatchEmailNotification  = async(event, context) =>{
    let appManagerDbConnection;
    let response;
    try{
        console.log('Inside initiateBatchEmailNotification function');
        //Make database connection
        appManagerDbConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
        appManagerDbConnection=knex(appManagerDbConnection.AppManagerDb);
        //Get the notification enabled organization list
        let isOrgInOpenStatus = await updateBatchEmailNotificationInstance(appManagerDbConnection);
        //Check whether record or not
        if(isOrgInOpenStatus)
        {
            response={
                nextStep:'Step2',
                message:'Record exist process the next step.'
            }
        }
        else{
            response={
                nextStep:'End',
                message:'No records found so quit the process.'
            }
        }
        //Destroy database connection
        appManagerDbConnection ? appManagerDbConnection.destroy() : null;
        
        return response;
    }
    catch(mainCatchError){
        console.log('Error in initiateBatchEmailNotification function main catch block.', mainCatchError);
        appManagerDbConnection ? appManagerDbConnection.destroy() : null;
        response={
            nextStep:'End',
            message:'Error from step1 so quit the process.'
        }
        console.log('catch block response',response)
        return response;
    }
};


//Function to get notification enabled instances
async function updateBatchEmailNotificationInstance(appManagerDbConnection){
    try{
        console.log('Inside updateBatchEmailNotificationInstance function');
        return(
            appManagerDbConnection(appManagerTables.batchEmailNotification)
            .update({
                Manager_Email_Status: 'Open',
                Updated_On: moment().tz('Asia/Kolkata').format('YYYY-MM-DD HH:mm:ss')
            })
            .whereIn('Manager_Email_Status',defaultValues.emailNotificationStatus)
            .then(()=>{
                return(
                appManagerDbConnection(appManagerTables.batchEmailNotification)
                .update({
                    Hr_Email_Status: 'Open',
                    Updated_On: moment().tz('Asia/Kolkata').format('YYYY-MM-DD HH:mm:ss')
                })
                .whereIn('Hr_Email_Status',defaultValues.emailNotificationStatus)
                .then(()=>{
                    return(
                    appManagerDbConnection(appManagerTables.batchEmailNotification)
                    .select('Id')
                    .where('Manager_Email_Status',defaultValues.openEmailNotificationStatus)
                    .orWhere('Hr_Email_Status',defaultValues.openEmailNotificationStatus)
                    .then((openNotificationList)=>{
                        if(openNotificationList && openNotificationList.length > 0){
                            return true;
                        }else{
                            return false;
                        }
                    })
                    .catch(catchError => {
                        console.log('Error in updateBatchEmailNotificationInstance function .catch block',catchError);
                        throw catchError;
                    })                
                    );
                })
                )
            })
            .catch(catchError => {
                console.log('Error in updateBatchEmailNotificationInstance function .catch block',catchError);
                throw catchError;
            })                
        );
    }
    catch(mainCatchError){
        console.log('Error in updateBatchEmailNotificationInstance function main catch block.', mainCatchError);
        throw mainCatchError;
    }
};