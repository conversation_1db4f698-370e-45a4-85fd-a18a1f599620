// require resolver files
const employeeDirectoryList = require('./resolvers/employees/listemployees');
const ipwhitelisting = require('./resolvers/ipwhitelisting');
const getEmployeeCount =  require('./resolvers/dashboard/getEmployeeCount');
const getDomainDetails =require('./resolvers/dashboard/getDomainDetails');
const getOrganizationUserDetails = require('./resolvers/dashboard/getOrganizationUserDetails');
const listModulesAndForms = require('./resolvers/dashboard/listModulesAndForms');
const listAnnouncementAndNotification = require('./resolvers/dashboard/listAnnouncementAndNotification');
const checkGeoEnforceAndWorkPlace =require('./resolvers/dashboard/checkGeoEnforceAndWorkPlace');
const empLeaveHistory = require('./resolvers/dashboard/empLeaveHistory');
const getUtilization = require('./resolvers/dashboard/getUtilization');
const listLeavesAndHolidays=require('./resolvers/dashboard/listLeavesAndHolidays');
const getEmployeeDetails = require('./resolvers/dashboard/getEmployeeDetails');
const listMyActions=require('./resolvers/dashboard/listMyActions');
const getLateAttendanceStatistics = require('./resolvers/dashboard/getLateAttendanceStatistics');
const getStaticContractTaxDetails =require('./resolvers/employees/getStaticContractTaxDetails');
const getReminders = require('./resolvers/dashboard/getReminders');
const getWorkPlaceCount = require('./resolvers/dashboard/getWorkPlaceCount');
const getLeaveDistribution = require('./resolvers/dashboard/getLeaveDistribution');
const requestRights = require('./resolvers/dashboard/requestRights');
const listShiftScheduling = require('./resolvers/shiftScheduling/listShiftScheduling');
const updateShiftScheduling = require('./resolvers/shiftScheduling/updateShiftScheduling');
const listShiftRoasterComponents = require('./resolvers/shiftScheduling/listShiftRoasterComponents')
const bulkImportShiftScheduling = require('./resolvers/shiftScheduling/bulkImportShiftScheduling')
const deleteShiftScheduling = require('./resolvers/shiftScheduling/deleteShiftScheduling')
const listShiftType = require('./resolvers/shiftType/listShiftType')
const updateShiftType = require('./resolvers/shiftType/updateShiftType')
const deleteShiftType = require('./resolvers/shiftType/deleteShiftType')
const listWorkSchedule = require('./resolvers/shiftType/listWorkSchedule')
const bulkImportWeekOff = require('./resolvers/shiftScheduling/bulkImportWeekOff');
const retrieveMonthlyCalenderShiftDetails = require('./resolvers/shiftScheduling/retrieveMonthlyCalenderShiftDetails');
const getShiftSummary = require('./resolvers/shiftScheduling/getShiftSummary');
const retrieveWeeklyCalenderShiftDetails = require('./resolvers/shiftScheduling/retrieveWeeklyCalenderShiftDetails');
const listOvertimeDetails = require('./resolvers/overtime/listOvertimeDetails');
const getOTConfigurationDetails = require('./resolvers/overtime/getOTConfigurationDetails');
const deleteOvertimeDetails = require('./resolvers/overtime/deleteOvertimeDetails');
const addOvertimeDetails = require('./resolvers/overtime/updateOvertimeDetails');
const listOvertimeEmployees = require('./resolvers/overtime/listOvertimeEmployees');
const overtimeWageCalculation = require('./resolvers/overtime/overtimeWageCalculation');
const updateOvertimeDetailsStatus = require('./resolvers/overtime/overtimeClaimStatusApproval');
const getOvertimePrerequisites = require('./resolvers/overtime/getOvertimePrerequisites');
const retrievePerformanceSettings=require('./resolvers/performanceManagementSystem/retrievePerformanceSettings');
const updatePerformanceSettings=require('./resolvers/performanceManagementSystem/updatePerformanceSettings');
const goalsCrud=require('./resolvers/performanceManagementSystem/goalsCrud');
const listGoals=require('./resolvers/performanceManagementSystem/listGoals');
const listEmployeeGoalsAndRating=require('./resolvers/performanceManagementSystem/listEmployeeGoalsAndRating');
const listEmployeesAndReviewers = require('./resolvers/performanceManagementSystem/listEmployeesAndReviewers');
const retrieveEmployeeGoalsAndRating=require('./resolvers/performanceManagementSystem/retrieveEmployeeGoalsAndRating');
const addEmployeeGoalsAndAchievement = require('./resolvers/performanceManagementSystem/addEmployeeGoalsAndAchievement');
const retrieveRatings=require('./resolvers/performanceManagementSystem/retrieveRatings');
const publishGoals=require('./resolvers/performanceManagementSystem/publishGoals');
const updateEmployeeGoalsAndAchievement=require('./resolvers/performanceManagementSystem/updateEmployeeGoalsAndAchievement');
const publishRatings=require('./resolvers/performanceManagementSystem/publishRatings');
const listPerformanceYear=require('./resolvers/performanceManagementSystem/listPerformanceYear');
const retrieveTeamRatings=require('./resolvers/performanceManagementSystem/retrieveTeamRatings');
const retrieveTeamGoalsRatingsCount=require('./resolvers/performanceManagementSystem/retrieveTeamGoalsRatingsCount');
const retrieveEmployeeYearlyRating=require('./resolvers/performanceManagementSystem/retrieveEmployeeYearlyRating');
const retrieveTeamAverageMonthlyRatings=require('./resolvers/performanceManagementSystem/retrieveTeamAverageMonthlyRatings');
const calculateOvertimeCompOffBalance = require('./resolvers/overtime/calculateOvertimeCompOffBalance');
const listShiftAllowanceShiftType = require('./resolvers/overtime/listShiftAllowanceShiftType');
const calculateOvertimeShiftAllowance = require('./resolvers/overtime/calculateOvertimeShiftAllowance');
const getRedeemRewardsDetails = require('./resolvers/employees/getRedeemRewardsDetails');
const getOrganizationSubscribedPlan=require('./resolvers/dashboard/getOrganizationSubscribedPlan');
const retrieveCurrencyAndShareDetails=require('./resolvers/benefits/retrieveCurrencyAndShareDetails');
const updateCurrencyAndShareDetails=require('./resolvers/benefits/updateCurrencyAndShareDetails');
const listAllEmployeeDetails=require('./resolvers/benefits/listAllEmployeeDetails');
const addEmployeesForAllocatingShares=require('./resolvers/benefits/addEmployeesForAllocatingShares');
const listEmployeeTotalShares=require('./resolvers/benefits/listEmployeeTotalShares');
const listEmployeeAllocatedShares=require('./resolvers/benefits/listEmployeeAllocatedShares');
const updateEmployeeAllocatedShares=require('./resolvers/benefits/updateEmployeeAllocatedShares');
const vestEmployeeShares=require('./resolvers/benefits/vestEmployeeShares');
const retrieveShareVestHistory=require('./resolvers/benefits/retrieveShareVestHistory');
const bulkImportESOP=require('./resolvers/benefits/bulkImportESOP');
const updateOvertimeClaimCompletedStatus = require('./resolvers/overtime/overtimeClaimStatusApproval');
const updateOvertimeOtherClaims = require('./resolvers/overtime/updateOvertimeOtherClaims');//sub lambda created for updating overtime claim status to fix the timeout issue
const listStates=require('./resolvers/employees/listStates');
const listHelpUrlAndText=require('./resolvers/help/listHelpUrlAndText');
const updateEmpLanguagePreference = require('./resolvers/employee/updateEmpLanguagePreference');

// Define resolver
const resolvers = {
    Query: Object.assign({},
        employeeDirectoryList.resolvers.Query,
        ipwhitelisting.resolvers.Query,
        getEmployeeCount, getWorkPlaceCount, getLeaveDistribution,
        getDomainDetails.resolvers.Query,
        ipwhitelisting.resolvers.Query,
        listModulesAndForms.resolvers.Query,
        getReminders,
        getOrganizationUserDetails.resolvers.Query,empLeaveHistory, getEmployeeDetails, getLateAttendanceStatistics,
        checkGeoEnforceAndWorkPlace.resolvers.Query, requestRights,
        listLeavesAndHolidays.resolvers.Query, getUtilization,
        listAnnouncementAndNotification.resolvers.Query,
        listMyActions.resolvers.Query,
        getStaticContractTaxDetails.resolvers.Query,
        listShiftScheduling, retrieveMonthlyCalenderShiftDetails, getShiftSummary, retrieveWeeklyCalenderShiftDetails,
        listShiftRoasterComponents,
        listShiftType,listWorkSchedule,
        listOvertimeDetails, getOTConfigurationDetails,listOvertimeEmployees,overtimeWageCalculation,
        getOvertimePrerequisites.resolvers.Query,
        retrievePerformanceSettings.resolvers.Query,
        listGoals.resolvers.Query,
        listEmployeeGoalsAndRating.resolvers.Query,
        listEmployeesAndReviewers,
        retrieveEmployeeGoalsAndRating.resolvers.Query,
        retrieveRatings.resolvers.Query,
        listPerformanceYear,
        retrieveTeamRatings.resolvers.Query,
        retrieveTeamGoalsRatingsCount,
        retrieveEmployeeYearlyRating.resolvers.Query,
        retrieveTeamAverageMonthlyRatings,
        calculateOvertimeCompOffBalance,
        listShiftAllowanceShiftType,
        calculateOvertimeShiftAllowance,
        getRedeemRewardsDetails,
        getOrganizationSubscribedPlan.resolvers.Query,
        retrieveCurrencyAndShareDetails.resolvers.Query,
        listAllEmployeeDetails.resolvers.Query,
        listEmployeeTotalShares,
        listEmployeeAllocatedShares,
        retrieveShareVestHistory.resolvers.Query,
        listStates,
        listHelpUrlAndText,
    ),
    Mutation: Object.assign({},
        ipwhitelisting.resolvers.Mutation,
        updateShiftScheduling,
        bulkImportShiftScheduling,
        deleteShiftScheduling,
        updateShiftType, deleteShiftType, bulkImportWeekOff,
        deleteOvertimeDetails,
        addOvertimeDetails,
        updateOvertimeDetailsStatus.resolvers.Mutation,
        updatePerformanceSettings.resolvers.Mutation,
        goalsCrud.resolvers.Mutation,
        addEmployeeGoalsAndAchievement,
        publishGoals,
        updateEmployeeGoalsAndAchievement.resolvers.Mutation,
        publishRatings,
        updateCurrencyAndShareDetails.resolvers.Mutation,
        addEmployeesForAllocatingShares.resolvers.Mutation,
        updateEmployeeAllocatedShares.resolvers.Mutation,
        vestEmployeeShares.resolvers.Mutation,
        bulkImportESOP.resolvers.Mutation,
        updateEmpLanguagePreference
    )
}
exports.resolvers = resolvers;
