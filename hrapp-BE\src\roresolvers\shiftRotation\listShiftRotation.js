// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formIds } = require('../../../common/appconstants');

//function to list the shift rotation
let organizationDbConnection;
module.exports.listShiftRotation = async (parent, args, context, info) => {
    try {
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, null, 'Role_View', 'Back-end', null, formIds.shiftRotation);
        if (!checkRights) {
            throw '_DB0100';
        }

        //Retrieve shift rotation details
        let data = await organizationDbConnection(ehrTables.shiftRotation + " as SR")
            .select("SR.*", "SRS.*", "WS.Title as Work_Schedule_Name", "LT.Leave_Name",
                organizationDbConnection.raw("CONCAT_WS(' ',EP1.Emp_First_Name, EP1.Emp_Middle_Name, EP1.Emp_Last_Name) as Added_By_Name"),
                organizationDbConnection.raw("CONCAT_WS(' ',EP2.Emp_First_Name, EP2.Emp_Middle_Name, EP2.Emp_Last_Name) as Updated_By_Name"),)
            .leftJoin(ehrTables.shiftRotationSchedule + " as SRS", "SRS.Rotation_Id", "SR.Rotation_Id")
            .leftJoin(ehrTables.empShiftType + " as ST", "ST.Shift_Type_Id", "SRS.ShiftType_Id")
            .leftJoin(ehrTables.workSchedule + " as WS", "WS.WorkSchedule_Id", "ST.WorkSchedule_Id")
            .leftJoin(ehrTables.leavetype + " as LT", "LT.LeaveType_Id", "SR.LeaveType_Id")
            .leftJoin(ehrTables.empPersonal + " as EP1", "EP1.Employee_Id", "SR.Added_By")
            .leftJoin(ehrTables.empPersonal + " as EP2", "EP2.Employee_Id", "SR.Updated_By")
            .orderBy('SR.Rotation_Id', 'asc');

        // Group the SRS details manually in JavaScript
        let groupedData = {};

        data.forEach(row => {
            const rotationId = row.Rotation_Id;

            if (!groupedData[rotationId]) {
                groupedData[rotationId] = {
                    Rotation_Id: row.Rotation_Id,
                    Scheduler_Name: row.Scheduler_Name,
                    Repeat_Schedule: row.Repeat_Schedule,
                    Repeat_Count: row.Repeat_Count,
                    Enable_Roster_Leave: row.Enable_Roster_Leave,
                    Leave_Entitlement_Per_Roster_Day: row.Leave_Entitlement_Per_Roster_Day,
                    Leave_Replenishment_Period: row.Leave_Replenishment_Period,
                    LeaveType_Id: row.LeaveType_Id,
                    Leave_Name: row.Leave_Name,
                    Added_On: row.Added_On,
                    Added_By_Name: row.Added_By_Name,
                    Updated_On: row.Updated_On,
                    Updated_By_Name: row.Updated_By_Name,
                    Shift_Rotation_Schedule: []
                };
            }

            // Push the SRS details to the array
            groupedData[rotationId].Shift_Rotation_Schedule.push({
                Work_Schedule_Id: row.Work_Schedule_Id,
                Work_Schedule_Name: row.Work_Schedule_Name,
                Rotation_Level: row.Rotation_Level,
                Period_Unit: row.Period_Unit,
                Applicable_Period: row.Applicable_Period
            });
        });

        let formedData = Object.values(groupedData);

        //Calculate Days
        let finalData = formedData.map(rotation => {
            let totalDays = 0;
            let repeatCount = rotation.Repeat_Count || 0;
        
            // Loop through each shift rotation schedule to calculate the total days
            rotation.Shift_Rotation_Schedule.forEach(schedule => {
                let applicablePeriod = schedule.Applicable_Period;
        
                // Convert weeks to days if Period_Unit is 'week'
                if (schedule.Period_Unit === 'week') {
                    applicablePeriod *= 7; 
                }
        
                // Total days for this schedule (applicable period + repeated periods)
                totalDays += applicablePeriod + (applicablePeriod * repeatCount);
            });
        
            // Return the rotation object along with the calculated total days
            return {
                ...rotation,
                Total_Days: totalDays - 1
            };
        });


        return { errorCode: "", message: "Shift rotation details retrieved successfully.", shiftRotation: JSON.stringify(finalData) };

    }
    catch (e) {
        console.log('Error in listShiftRotation function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'SS0004');
        throw new ApolloError(errResult.message, errResult.code);
    }
    finally {
        //Destroy DB connection
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
}