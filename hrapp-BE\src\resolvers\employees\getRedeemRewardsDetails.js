//require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common constant files
const { formName } = require('../../../common/appconstants');
// require table alias
const { ehrTables } = require('../../../common/tablealias');
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');

/** Retrieve the redeem rewards form(subform of the employee form) details to present the redeem rewards tab and its details to the login employee. */
module.exports.getRedeemRewardsDetails = async (parent,args,context,info) => {
    console.log('Inside the getRedeemRewardsDetails() function.');
    let organizationDbConnection;
    let errResult;
    try{
        // get the organization data base connection
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // Check 'Redeem Rewards' form view access rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, context.Employee_Id, formName.redeemRewards, '','UI');
        // If the login employee is having view access for the 'Redeem Rewards' form
        if (Object.keys(checkRights).length >0 && checkRights.Role_View===1) {
            return(
                organizationDbConnection(ehrTables.redeemRewardsDetails)
                .select('Enable_Rewards','Brand_Name','Redirection_URL')
                .then((redeemRewardsDetails) => {
                    // destroy database connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return {errorCode:'',message:'Redeem rewards details retrieved successfully.',redeemRewardsDetails:(redeemRewardsDetails.length>0)?JSON.stringify(redeemRewardsDetails[0]):''};
                })
                .catch(catchError => {
                    console.log('Error in getRedeemRewardsDetails() function .catch block.', catchError);
                    // destroy database connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    errResult = commonLib.func.getError(catchError, 'ERR0001');
                    
                    throw new ApolloError(errResult.message, errResult.code);// throw error response to UI
                })
            );
        }else{
            throw('_DB0100');//throw employee does not have view access
        }
    }catch(getRedeemRewardsDetailsMainCatchError){
        console.log('Error in the getRedeemRewardsDetails() function main catch block. ',getRedeemRewardsDetailsMainCatchError);
        errResult = commonLib.func.getError(getRedeemRewardsDetailsMainCatchError, 'ERR0101');
        // destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        throw new ApolloError(errResult.message,errResult.code);// throw error response to UI
    }
};