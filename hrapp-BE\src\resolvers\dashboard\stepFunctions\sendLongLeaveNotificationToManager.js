'use strict';
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make database connection
const knex = require('knex');
//Require moment package
const moment=require('moment');
//Require common function
const { getBatchNotificationDetails,updateOrgBatchNotificationStatus,sendEmailToManager, getLongLeaveTypeIds }=require('../../../../common/batchNotificationCommonFunctions');
const { getLongLeaveEmployeeDetails }=require('../../../../common/remindersCommonFunctions');
//Require file to access constant values
const { awsSesTemplates,defaultValues } = require('../../../../common/appconstants');
//Require table alias
const { ehrTables } = require('../../../../common/tablealias');

//Function to iterate the organization codes and get the long leave employee for the org code based on the configuration and send the reminder to manager
module.exports.sendLongLeaveNotificationToManager = async (parent, args, context, info) => {
    console.log("Inside sendLongLeaveNotificationToManager() function.");
    let appManagerDbConnection;
    let organizationDbConnection;
    let response;
    try{
        //Make database connection
        appManagerDbConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
        appManagerDbConnection=knex(appManagerDbConnection.AppManagerDb);
        let orgCodeList = await getBatchNotificationDetails(appManagerDbConnection,'longLeave','Manager_Email_Status',defaultValues.openEmailNotificationStatus);
        
        if(orgCodeList.length > 0){
            let currentDate = moment().tz('Asia/Kolkata').format('YYYY-MM-DD');
            let updateParams = {};
            //Iterate for all the instances
            for(let i=0;i<orgCodeList.length;i++){
                updateParams = {};
                let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgCodeList[i]);
                
                //Make database connection
                organizationDbConnection= await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCodeList[i].Org_Code,0,additionalHeaders);
                organizationDbConnection=knex(organizationDbConnection.OrganizationDb);
                let leaveTypeIds = await getLongLeaveTypeIds(organizationDbConnection, defaultValues.longLeaveTotalDays);
                if(leaveTypeIds && leaveTypeIds.length > 0){
                    //Get the long leave employee details
                    let longLeaveEmployees = await getLongLeaveEmployeeDetails(organizationDbConnection,leaveTypeIds,currentDate,defaultValues.longLeaveTotalDays,1);
                    if(longLeaveEmployees.length > 0){
                        let emailInputs = {
                            orgCode: orgCodeList[i].Org_Code,
                            employeesList: longLeaveEmployees,
                            managerGroupKey: 'managerEmailAddress',
                            htmlTableKeyName: 'longleaveemailtomanager',
                            statusKey: 'Manager_Email_Status',
                            notificationForKey: 'longLeave',
                            commonNotificationTemplate: awsSesTemplates.commonNotification,
                            emailSubject: 'Employee(s) expected to return to work after '+longLeaveEmployees[0].displayLeaveEndDate,
                            emailContent: 'The following employee(s) are expected to return to work after '+longLeaveEmployees[0].displayLeaveEndDate
                        };
                        let finalResponse = await sendEmailToManager(appManagerDbConnection,organizationDbConnection,emailInputs);
                        response = finalResponse;
                        //Destroy database connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                    }else{
                        console.log('Long leave employee does not exist for the current date for the org code - ',orgCodeList[i].Org_Code);
                        await updateOrgBatchNotificationStatus(appManagerDbConnection,orgCodeList[i].Org_Code,'Manager_Email_Status','Success','longLeave');
                        //Destroy database connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        response={
                            nextStep:'End',
                            message:'Email notification is not send as the long leave employees does not exist.'
                        };
                    }
                }
                //If the last organization is processed
                if(i===orgCodeList.length-1){
                    //Destroy database connection
                    appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                }
            }
        }else{
            console.log('Input org code list is empty');
            //Destroy database connection
            appManagerDbConnection ? appManagerDbConnection.destroy() : null;
            response={
                nextStep:'End',
                message:'No records found so quit the process.'
            }
        }
        return response;
    }catch(mainCatchError) {
        console.log('Error in the sendLongLeaveNotificationToManager() function main catch block. ',mainCatchError);
        
        //Destroy database connection
        appManagerDbConnection ? appManagerDbConnection.destroy() : null;
        organizationDbConnection ? organizationDbConnection.destroy() : null;

        response={
            nextStep:'End',
            message:'Error from sendLongLeaveNotificationToManager.So quit the process.'
        }
        return response;
    }
};