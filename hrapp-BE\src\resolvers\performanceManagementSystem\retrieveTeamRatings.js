// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require common constant files
const { formName,defaultValues } = require('../../../common/appconstants');
// require table alias
const { ehrTables } = require('../../../common/tablealias');
// require validation file
const {employeeGoalsAndRatingInputValidation} = require('../../../common/performanceManagementValidation');

// variable declarations
let errResult = {};
let organizationDbConnection = '';
let validationError={};

// resolver definition
const resolvers = {
    Query: {
        // function to retrieve the team performance ratings for the input month/year
        retrieveTeamRatings: async (parent, args, context, info) => {
            try{
                console.log('Inside retrieveTeamRatings function');
                // get the organization data base connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                let logInEmpId = context.Employee_Id;
                // check whether the employee has admin access to goals and achievement form or not
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.goalsAndAchievement, '', 'UI');
                if ((Object.keys(checkRights).length >0 && checkRights.Role_View===1) && (checkRights.Employee_Role==='admin')) {
                    // function to validate the input field
                    validationError=await employeeGoalsAndRatingInputValidation(args,'validateMonthYear');
                    // check whether there is no input validation error
                    if(Object.keys(validationError).length ===0){
                        // variable declarations
                        let rating=[];
                        let employeeCount=[];
                        let response={};
                        // iterate the loop till 10 since it is maximum rating by default
                        for (let i=1;i<=10;i++){
                            // push the ratings in a separate array
                            rating.push(i);
                            // push the employee count for each rating
                            let numberOfEmployee=await getEmployeeCountBasedOnRating(organizationDbConnection,args,i);
                            employeeCount.push(numberOfEmployee);
                        }
                        // form the output response
                        response.rating=rating;
                        response.employeeCount=employeeCount;
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return {errorCode:'',message:'Team employee ratings retrieved successfully.',teamRating:response};
                    }
                    else{
                        throw 'IVE0000';
                    }
                }
                else{
                    // throw if employee does not have access
                    throw ('_DB0100');
                }
            }catch(mainCatchError){
                console.log('Error in retrieveTeamRatings function main catch block',mainCatchError);
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                if (mainCatchError === 'IVE0000') {
                    errResult = commonLib.func.getError('', 'IVE0000');
                    // return response
                    throw new UserInputError(errResult.message, { validationError: validationError });
                } else {
                    errResult = commonLib.func.getError(mainCatchError, 'EPM0017');
                    // return response
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        }
    }
};
exports.resolvers = resolvers;

// function to get the number of employee's in each ratings
async function getEmployeeCountBasedOnRating(organizationDbConnection,args,inputRating){
    try{       
        // get the employee count for the input rating which are in published status         
        return(
            organizationDbConnection(ehrTables.performanceGoalAchievement)
            .count('Employee_Id as count')
            .where('Performance_Assessment_Month',args.month)
            .where('Performance_Assessment_Year',args.year)
            .where('Overall_Rating',inputRating)
            .where('Rating_Publish_Status', defaultValues.goalsAndAchievementGoalPublishStatus)
            .then(getEmployeeCount=>{
                return getEmployeeCount[0].count;
            })
            .catch(catchError => {
                console.log('Error in getEmployeeCountBasedOnRating function .catch block',catchError);
                return '';
            })                
        );
    }
    catch(error){
        console.log('Error in getEmployeeCountBasedOnRating function main catch block',error);
        return '';
    }
};
