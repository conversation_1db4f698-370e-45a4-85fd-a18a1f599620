//Require apollo-server-lambda to define lamda functions
var { ApolloServer, gql } = require('apollo-server-lambda');
//Require resolver
var { resolvers } = require('./settingsworesolver');
//Require fs to read schema from file
const fs = require('fs');
//Require schema.graphql
const typeDefs = gql(fs.readFileSync(__dirname.concat('/settingswoschema.graphql'), 'utf8'));
//Require common hrapp-corelib functions
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

module.exports.settingswoGraphql = (event, context, callback) => {
    // get customAuthorizerData from firebase authorize and  return it to resolver if exists.
    let idToken = event.requestContext.authorizer.idToken ? event.requestContext.authorizer.idToken : '';
    let refreshToken = event.requestContext.authorizer.refreshToken ? event.requestContext.authorizer.refreshToken : '';

    context.callbackWaitsForEmptyEventLoop = false; //to send the response immediately when callback executes

    // Create object for ApolloServer
    const server = new ApolloServer({
        typeDefs,
        resolvers,
        context: async ({ event }) => {
            let authDetails ={
                idToken: idToken,
                refreshToken: refreshToken
            };

            let contextData = await commonLib.func.getContextDataWithEmployeeId(event,authDetails,'wo');
            
            //return header to resolver function
            return {...contextData};
        }
    });
    const handler = server.createHandler({
        cors: {
            method: 'POST',
            allowHeaders: '*'
        }
    });
    
    function callbackFilter(error, output) {
        
        // We are appending the idToken and refreshToken in the response. While running this in local this is not returning the response
        // so here checked the stagename as local or not. If it is local then we will no append the token response. 
        // Otherwise token response will be append and response will be returned
        // If any doubts check this task #3794
        if (process.env.stageName !== 'local') {
            // parse the response data
            let responseData = JSON.parse(output.body);
            // push idToken and refresh token into an json object
            let identityToken = {
                idToken: idToken,
                refreshToken: refreshToken
            }
            // return the idToken and refreshTOken to UI
            responseData.identityToken = identityToken;
            output.body = JSON.stringify(responseData);
        }
        
        output.headers['Access-Control-Allow-Origin'] = '*';
        output.headers['Access-Control-Allow-Credentials'] = true;
        callback(error, output);
    }
    return handler(event, context, callbackFilter);
};
