// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex package
const knex = require('knex');
// require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require common constant files
const { formName,systemLogs } = require('../../../common/appconstants');
// require table alias
const { ehrTables } = require('../../../common/tablealias');
// require validation file
const commonValidation=require('../../../common/commonvalidation');

// variable declarations
let errResult = {};
let organizationDbConnection = '';
let inputValidationError;

// resolver definition
const resolvers = {
    Mutation:{
        // function to insert performance goals
        addGoalsToLibrary: async(parent, args, context, info) =>{
            try{
                console.log('Inside addGoalsToLibrary function');
                // get database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                let logInEmpId = context.Employee_Id;
                // check whether the employee has add access to goalsAndAchievement form or not & loggedIn employee should be either admin or manager
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.goalsAndAchievement, '', 'UI');
                // check whether loggedIn employee is manager/admin
                if ((Object.keys(checkRights).length >0 && checkRights.Role_Add===1) && (checkRights.Is_Manager===1 || checkRights.Employee_Role==='admin')) {
                    // validate the input fields
                    inputValidationError=await validateInputs(args.goalsList);
                    if(Object.keys(inputValidationError).length ===0){
                        // get the employee timezone based on location
                        let employeeTimeZone = await commonLib.func.getEmployeeTimeZone(logInEmpId, organizationDbConnection, 1);
                        // form the params to insert goals
                        let goalsData=args.goalsList.map(field=>({
                            Goal_Description: field.trim(), // remove the extra spaces in performance goal
                            Added_By: logInEmpId,
                            Added_On: employeeTimeZone
                        }));
                        //Get the goal description
                        let goalDescriptionArray = goalsData.map(goalDetails => goalDetails['Goal_Description']);
                        let existingGoalDescription = '';
                        return(
                        organizationDbConnection
                        .transaction(function(trx){
                            let goalsExistQuery = organizationDbConnection(ehrTables.performanceGoalsLibrary)
                            .select(organizationDbConnection.raw('GROUP_CONCAT(Goal_Description) as goalDescription'))
                            .whereIn('Goal_Description',goalDescriptionArray);
                            //If the logged in employee is manager, validate the goals library which is created by the login employee
                            if(checkRights.Is_Manager===1){
                                goalsExistQuery.where('Added_By',logInEmpId) 
                            }
                            //Validate the goal description already exist or not
                            return(
                            goalsExistQuery
                            .transacting(trx)
                            .then(goalDescriptionExistResponse=>{
                                if(goalDescriptionExistResponse.length>0 && goalDescriptionExistResponse[0].goalDescription){
                                    console.log('Goal description already exist.',goalDescriptionExistResponse);
                                    existingGoalDescription = goalDescriptionExistResponse[0].goalDescription;
                                    throw 'EPM0028';
                                }else{
                                    return(
                                    // function to insert performance goals
                                    organizationDbConnection(ehrTables.performanceGoalsLibrary)
                                    .insert(goalsData)
                                    .then(async(insertedRatings)=>{
                                        console.log('Goals inserted successfully',insertedRatings);
                                        // function to update system logs
                                        let systemLogParams = {
                                            action: systemLogs.roleAdd,
                                            userIp: context.User_Ip,
                                            employeeId: logInEmpId,
                                            formName: formName.performanceManagement,
                                            trackingColumn: 'Goals',
                                            organizationDbConnection: organizationDbConnection,
                                            uniqueId: ''
                                        };
                                        // call function to update system log activities
                                        await commonLib.func.createSystemLogActivities(systemLogParams);
                                        // destroy database connection
                                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                                        return {errorCode:'',message:'Performance goals inserted successfully.'};
                                    })
                                    )
                                }
                            })
                            )
                        })
                        .then(function (result) {
                            return result;
                        })
                        .catch(catchError => {
                            console.log('Error while inserting goals .catch block',catchError);
                            // destroy database connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            if(catchError === 'EPM0028' && existingGoalDescription){
                                let errorMessage = "Goal name '"+existingGoalDescription+"' already exists";
                                throw new ApolloError(errorMessage,'EPM0028');// return error response
                            }else{
                                errResult = commonLib.func.getError(catchError, 'EPM0103');
                                throw new ApolloError(errResult.message,errResult.code);// return error response
                            }
                        })
                        )
                    }
                    else{
                        throw 'IVE0000';
                    }
                }
                else{
                    throw '_DB0101';
                }
            }catch(mainCatchError){
                console.log('Error in addGoalsToLibrary function main catch block',mainCatchError);
                errResult = commonLib.func.getError(mainCatchError, 'EPM0005');
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                if (mainCatchError === 'IVE0000') {
                    errResult = commonLib.func.getError('', 'IVE0000');
                    // return response
                    throw new UserInputError(errResult.message, { validationError: inputValidationError });
                } else {
                    // return response
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        },
        //Function to update performance goals
        updateGoalsToLibrary: async(parent, args, context, info) =>{
            try{
                console.log('Inside updateGoalsToLibrary function.');
                //Get database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                let loginEmployeeId = context.Employee_Id;
                //Check whether the employee has update access to goalsAndAchievement form or not & loggedIn employee should be either admin or manager
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId , formName.goalsAndAchievement, '', 'UI');
                //Check whether loggedIn employee is manager/admin
                if ((Object.keys(checkRights).length >0 && checkRights.Role_Update===1) && (checkRights.Is_Manager===1 || checkRights.Employee_Role==='admin')) {
                    // validate the input fields
                    inputValidationError=await validateUpdateGoalsInputs(args);
                    if(Object.keys(inputValidationError).length ===0){
                        let goalDescription = args.goalDescription.trim();
                        let goalId = args.goalId;
                        //Get the employee timezone based on location
                        let loginEmployeeCurrentDateTime = await commonLib.func.getEmployeeTimeZone(loginEmployeeId, organizationDbConnection, 1);
                        return(
                        organizationDbConnection
                        .transaction(function(trx){
                            let goalsExistQuery = organizationDbConnection(ehrTables.performanceGoalsLibrary)
                                                .count('Goal_Id as goalDescriptionExistCount')
                                                .where('Goal_Description',goalDescription)
                                                .whereNot('Goal_Id',goalId);
                            //If the logged in employee is manager, validate the goals library which is created by the login employee
                            if(checkRights.Is_Manager===1){
                                goalsExistQuery.where('Added_By',logInEmpId) 
                            }
                            //Validate the goal description already exist or not
                            return(
                            goalsExistQuery
                            .transacting(trx)
                            .then(goalDescriptionExistResponse=>{
                                if(goalDescriptionExistResponse.length>0){
                                    if(goalDescriptionExistResponse[0].goalDescriptionExistCount === 0){
                                        //Validate the goal is associated with the employee or not
                                        return(
                                        organizationDbConnection(ehrTables.assessmentCycleGoalRatings)
                                        .count('Goal_Id as goalIdCount')
                                        .where('Goal_Id',goalId)
                                        .transacting(trx)
                                        .then(goalExistResponse=>{
                                            //If the goals are not associated with the performance record
                                            if(goalExistResponse.length === 0 || (goalExistResponse.length>0 
                                            && goalExistResponse[0].goalIdCount === 0)){
                                                let updateDetails = {
                                                    'Goal_Description': goalDescription,
                                                    'Last_Updated_On': loginEmployeeCurrentDateTime,
                                                    'Last_Updated_By': loginEmployeeId
                                                };
                                        
                                                //Update the goal details
                                                return(
                                                organizationDbConnection(ehrTables.performanceGoalsLibrary)
                                                .update(updateDetails)
                                                .where('Goal_Id',args.goalId)
                                                .transacting(trx)
                                                .then(async() =>{
                                                    //Function to update system logs.Ex: Update Performance Management Goals - 59
                                                    let systemLogParams = {
                                                        action: systemLogs.roleUpdate,
                                                        userIp: context.User_Ip,
                                                        employeeId: loginEmployeeId,
                                                        formName: formName.performanceManagement,
                                                        trackingColumn: 'Goals',
                                                        organizationDbConnection: organizationDbConnection,
                                                        uniqueId: args.goalId
                                                    };
                                                    //Call function to update system log activities
                                                    await commonLib.func.createSystemLogActivities(systemLogParams);
                                                    //Destroy database connection
                                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                    return {errorCode:'',message:'Performance goals updated successfully.'};
                                                })
                                                )
                                            }else{
                                                console.log('Goal is already associated with the employee.',args.goalId,goalExistResponse);
                                                throw('EPM0027');
                                            }
                                        })
                                        )
                                    }else{
                                        console.log('Goal description already exist.',args.goalId,goalDescriptionExistResponse);
                                        throw('EPM0028');
                                    }
                                }else{
                                    console.log('Goal does not exist.',goalDescriptionExistResponse);
                                    throw 'EPM0029';
                                }
                            })
                            )
                        })
                        .then(function (result) {
                            return result;
                        })
                        .catch(function (catchError) {
                            console.log('Error in updateGoalsToLibrary function .catch block',catchError);
                            //Destroy database connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            errResult = commonLib.func.getError(catchError, 'EPM0128');
                            throw new ApolloError(errResult.message,errResult.code);
                        })
                        );
                    }
                    else{
                        throw 'IVE0000';
                    }
                }
                else{
                    throw '_DB0102';
                }
            }catch(mainCatchError){
                console.log('Error in updateGoalsToLibrary function main catch block',mainCatchError);
                errResult = commonLib.func.getError(mainCatchError, 'EPM0025');
                //Destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                if (mainCatchError === 'IVE0000') {
                    console.log('Validation error in the updateGoalsToLibrary function.',inputValidationError);
                    errResult = commonLib.func.getError('', 'IVE0000');
                    //Return error response
                    throw new UserInputError(errResult.message, { validationError: inputValidationError });
                } else {
                    //Return error response
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        },
        //Function to delete performance goals
        deleteGoalsFromLibrary: async(parent, args, context, info) =>{
            // variable declarations
            inputValidationError={};
            try{
                console.log('Inside deleteGoalsFromLibrary function.');
                //Get database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                let loginEmployeeId = context.Employee_Id;
                //Check whether the employee has delete access to goalsAndAchievement form or not & loggedIn employee should be either admin or manager
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId , formName.goalsAndAchievement, '', 'UI');
                //Check whether loggedIn employee is manager/admin
                if ((Object.keys(checkRights).length >0 && checkRights.Role_Delete===1) && (checkRights.Is_Manager===1 || checkRights.Employee_Role==='admin')) {
                    if (!args.goalId) {
                        inputValidationError['IVE0146'] = commonLib.func.getError('', 'IVE0146').message;
                    }
                    if(Object.keys(inputValidationError).length ===0){
                        return(
                        organizationDbConnection
                        .transaction(function(trx){
                            //Validate the goal id associated with the employees or not
                            return(
                            organizationDbConnection(ehrTables.assessmentCycleGoalRatings)
                            .count('Goal_Id as goalIdCount')
                            .where('Goal_Id',args.goalId)
                            .transacting(trx)
                            .then(goalExistResponse=>{
                                //If the goals are not associated with the performance record
                                if(goalExistResponse.length === 0 || (goalExistResponse.length>0 
                                && goalExistResponse[0].goalIdCount === 0)){
                                    //Delete the goal details
                                    return(
                                    organizationDbConnection(ehrTables.performanceGoalsLibrary)
                                    .delete()
                                    .where('Goal_Id',args.goalId)
                                    .transacting(trx)
                                    .then(async(deleteGoalResponse) =>{
                                        if(deleteGoalResponse){
                                            //Function to update system logs. Ex: Delete Performance Management Goals - 59
                                            let systemLogParams = {
                                                action: systemLogs.roleDelete,
                                                userIp: context.User_Ip,
                                                employeeId: loginEmployeeId,
                                                formName: formName.performanceManagement,
                                                trackingColumn: 'Goals',
                                                organizationDbConnection: organizationDbConnection,
                                                uniqueId: args.goalId
                                            };
                                            //Call function to update system log activities
                                            await commonLib.func.createSystemLogActivities(systemLogParams);
                                            //Destroy database connection
                                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                                            return {errorCode:'',message:'Performance goals deleted successfully.'};
                                        }else{
                                            console.log('Goal is already deleted.',args.goalId,deleteGoalResponse);
                                            throw('EPM0029');
                                        }
                                    })
                                    )
                                }else{
                                    console.log('Goal is already associated with the employee.',args.goalId,goalExistResponse);
                                    throw('EPM0027');
                                }
                            })
                            )
                        })
                        .then(function (result) {
                            return result;
                        })
                        .catch(function (catchError) {
                            console.log('Error in deleteGoalsFromLibrary function .catch block',catchError);
                            //Destroy database connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            errResult = commonLib.func.getError(catchError, 'EPM0129');
                            throw new ApolloError(errResult.message,errResult.code);
                        })
                        );
                    }
                    else{
                        throw 'IVE0000';
                    }
                }
                else{
                    throw '_DB0103';
                }
            }catch(mainCatchError){
                console.log('Error in deleteGoalsFromLibrary function main catch block',mainCatchError);
                errResult = commonLib.func.getError(mainCatchError, 'EPM0026');
                //Destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                if (mainCatchError === 'IVE0000') {
                    console.log('Validation error in the deleteGoalsFromLibrary function.',inputValidationError);
                    errResult = commonLib.func.getError('', 'IVE0000');
                    //Return error response
                    throw new UserInputError(errResult.message, { validationError: inputValidationError });
                } else {
                    //Return error response
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        }
    }
};
exports.resolvers = resolvers;

// function to validate inputs
async function validateInputs(input){
    let validationError={};
    try{
        if(input.length>0){
            // validate all the inputs
            for(let i=0;i<input.length;i++)
            {
                let validate = commonValidation.checkLength(input[i],3,300);
                if (!validate) {
                    validationError['IVE0141'] = commonLib.func.getError('', 'IVE0141').message;
                }
            }
        }
        else{
            validationError['IVE0142'] = commonLib.func.getError('', 'IVE0142').message;
        }
        return validationError;
    }
    catch(error){
        console.log('Error in validateInputs function catch block',error);
        return validationError;
    }
}

// function to validate the update goals inputs
async function validateUpdateGoalsInputs(args){
    let validationError={};
    try{
        if (!args.goalId) {
            validationError['IVE0146'] = commonLib.func.getError('', 'IVE0146').message;
        }

        if (!commonValidation.checkLength(args.goalDescription,3,300)) {
            validationError['IVE0141'] = commonLib.func.getError('', 'IVE0141').message;
        }
        return validationError;
    }catch(error){
        console.log('Error in validateUpdateGoalsInputs function main catch block',error);
        return validationError;
    }
}
