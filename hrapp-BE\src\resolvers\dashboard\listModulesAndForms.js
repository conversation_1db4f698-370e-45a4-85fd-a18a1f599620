// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require table alias list
const tables = require('../../../common/tablealias');
// require common constant files
const constants = require('../../../common/appconstants');

// variable declaration
let ehrTables = tables.ehrTables;
let appManagerConnection,organizationDbConnection='';
let defaultValues = constants.defaultValues;
let integrationUrlConstant=defaultValues.integrationUrlConstant;
let reportUrlConstant=defaultValues.reportUrlConstant;
let departmentHierarchyUrlConstant=defaultValues.departmentHierarchyUrlConstant;
let hrReportsUrlConstant=defaultValues.hrReportsUrlConstant;

// resolver definition
const resolvers = {
    Query: {
        // function to list modules and forms based on employee access for providing menus in dashboard
        // Return access rights for all the forms and sub forms
        listModulesAndForms: async (parent, args, context, info) => {
            let errResult={};
            try{
                // Make database connection
                appManagerConnection = knex(context.connection.AppManagerDb);
                organizationDbConnection=knex(context.connection.OrganizationDb)
                
                let fetchModulesAndFormsListArgs={
                    orgCode: context.Org_Code,
                    loggedInEmployeeId: context.Employee_Id
                };

                //Language Modules and Forms
                let languageModulesAndForms = {}

                //If languageCode is provided then fetch the both modules and forms based on languageCode
                if (args.languageCode) {
                    //Get the column based on languageCode
                    const languageColumn = await organizationDbConnection(ehrTables.translatingLanguage)
                        .where('Language_Code', args.languageCode)
                        .select('Column_Name')
                        .first();

                    if (languageColumn) {
                        languageModulesAndForms = await getLanguageModulesAndForms(organizationDbConnection, languageColumn.Column_Name);
                        fetchModulesAndFormsListArgs.languageModulesAndForms = languageModulesAndForms;
                    }
                }

                let modulesAndFormDetails = await getModulesAndFormsList(organizationDbConnection,appManagerConnection,fetchModulesAndFormsListArgs);

                // destroy database connection
                appManagerConnection ? appManagerConnection.destroy() : null;
                organizationDbConnection ? organizationDbConnection.destroy():null;

                return { errorCode: '', message: 'Modules and forms listed successfully', modulesAndForms:modulesAndFormDetails};
            } catch (mainCatchError) {
                console.log('Error in listModuleAndForms function main catch block',mainCatchError);
                // destroy database connection
                appManagerConnection ? appManagerConnection.destroy() : null;
                organizationDbConnection ? organizationDbConnection.destroy():null;
                // get the code and message from common function based on returned error code
                errResult = commonLib.func.getError(mainCatchError, 'ERE0014');
                // return response
                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, modulesAndForms : {} }));
            }
        }
    }
};
exports.resolvers = resolvers;

async function getLanguageModulesAndForms(organizationDbConnection, languageColumn) {
    try {
        let languageModulesAndForms = {};

        let [languageModules, languageForms] = await Promise.all([
            organizationDbConnection(ehrTables.modules)
                .select(languageColumn + ' as Translated_Module_Name', 'Module_Id', 'Module_Name'),
            organizationDbConnection(ehrTables.ehrForms)
                .select(languageColumn + ' as Translated_Form_Name', 'Form_Id', 'Form_Name')
        ]);

        //Form them key value pair
        languageModules = languageModules.reduce((acc, module) => {
            acc[module.Module_Id] = {
                Module_Name: module.Module_Name,
                Translated_Module_Name: module.Translated_Module_Name
            }
            return acc;
        }, {})

        languageForms = languageForms.reduce((acc, form) => {
            acc[form.Form_Id] = {
                Form_Name: form.Form_Name,
                Translated_Form_Name: form.Translated_Form_Name
            }
            return acc;
        }, {})

        languageModulesAndForms = {
            modules: languageModules,
            forms: languageForms
        }
        return languageModulesAndForms;
    } catch (error) {
        console.log("Error in getLanguageModulesAndForms function catch block", error);
        throw error;
    }
}

// Function to push form list into resulted array
async function displayFormList(showFormInMenu,formId,isSubForm,resultFormName,resultCustomName,navigationUrl,moduleMenuList)
{
    try{
        // Check whether the input formId is main form and check showFormInMenu
        // if exist then we need to push the form details
        if(isSubForm===0 && showFormInMenu===1)
        {
            var displayMenus={
                'formId':formId,
                'formName':resultFormName,
                'customFormName':resultCustomName,
                'url':navigationUrl
            }

            moduleMenuList.push(displayMenus);
            // function to sort the result formarray based on formname in asc order
            moduleMenuList=await sortFormsList(moduleMenuList,'formName');
        }
        return moduleMenuList;
    }
    catch(error)
    {
        console.log("Error in displayFormList function catch block");
        return moduleMenuList;
    }
}

// function to sort the form list based on form name
function sortFormsList(array,key)
{
    return array.sort(function(a,b)
    {
        return((a[key]<b[key])?-1:((a[key]>b[key])?1:0));
    });
}

/**
 * Function to whether the employee has access to admin form or employee admin form or payroll admin form
 * and return the access count if access exist. Otherwise return 0.
 * @param {Object} organizationDbConnection - Organization Database connection object
 * @param {Object} appManagerConnection - App manager Database connection object
 * @param {Object} modulesAndFormsArgs - Input params
 * @param {String} modulesAndFormsArgs.orgCode - Organization code
 * @param {Number} modulesAndFormsArgs.loggedInEmployeeId - Logged in employee id
 * @returns {Object} - Returns an object with modules and forms list
 * @throws {Object} - Throws an error object if an error occured
*/
async function getModulesAndFormsList(organizationDbConnection,appManagerConnection,modulesAndFormsArgs){
    try{
        let formIdArray= [];
        var formDetails={};
        var formIdDetails= {};
        let navigationUrl="";
        const translatedModulesAndForms = modulesAndFormsArgs.languageModulesAndForms ? modulesAndFormsArgs.languageModulesAndForms : null;
        return(
            appManagerConnection
            .transaction(function(trx){
                return (
                    // Get forms,modules,custom form name based on orgcode from appmanager database
                    appManagerConnection
                    .select('HPF.Form_Id','F.Form_Name','F.Module_Id','M.Module_Name','F.Sub_Form')
                    .from(ehrTables.hrappRegisterUser + ' as HRU')
                    .innerJoin(ehrTables.billingRate + ' as BR', 'HRU.Billing_Id', 'BR.Billing_Id')
                    .innerJoin(ehrTables.hrappPlanForm + ' as HPF', 'BR.Plan_Id', 'HPF.Plan_Id')
                    .innerJoin(ehrTables.forms + ' as F','F.Form_Id', 'HPF.Form_Id')
                    // .leftJoin(ehrTables.customizationForms +' as CF',"CF.Form_Id", "F.Form_Id")
                    .innerJoin(ehrTables.modules + ' as M','M.Module_Id','F.Module_Id')
                    .where('HRU.Org_Code', modulesAndFormsArgs.orgCode)
                    .groupBy('HPF.Form_Id')
                    .transacting(trx)
                    .then(async (moduleFormsData) => {
                        // check whether data exist or not
                        if (moduleFormsData.length > 0) {
                            let customFormName = await appManagerConnection
                                .select('New_Form_Name','Form_Id')
                                .from(ehrTables.customizationForms)
                                .whereNotNull('New_Form_Name')
                                .where(function() {
                            
                                    this.where(function() {
                                        // Condition when Customization_Applicable_For is 'Specific Organization'
                                        this.where('Customization_Applicable_For', 'Specific Organization')
                                            .andWhere('Org_Code', modulesAndFormsArgs.orgCode)
                                            .andWhere('Enable', 1);
                                    });
                            
                                    this.orWhere(function() {
                                        // Condition when Customization_Applicable_For is 'All Organizations'
                                        this.where('Customization_Applicable_For', 'All Organization')
                                            .whereNotIn('Form_Id', function () {
                                                this.select('Form_Id').from(ehrTables.customizationForms).where('Org_Code', modulesAndFormsArgs.orgCode);
                                            })
                                            .andWhere('Enable', 1);
                                    });
                                })
                                .groupBy('Form_Id')
                                .transacting(trx)
                                .then(async (customFormName) => {
                                    return customFormName;
                                });
                            
                            // Form Form_Id array from the result
                            moduleFormsData.map((field => {
                                formIdArray.push(field.Form_Id);
                            }));
                            // invoke function to check access for the all the forms based on formId
                            let formAccessRights = await commonLib.func.checkMultipleFormAccessRights(modulesAndFormsArgs.loggedInEmployeeId,formIdArray, organizationDbConnection);
                            
                            // Check whether success response from formAccessRights function
                            if(Object.keys(formAccessRights).length>0)
                            {
                                let menuArray=[];
                                // output response formation
                                for(let i in formAccessRights)
                                {
                                    for(let j in moduleFormsData)
                                    {
                                        var resultFormId=moduleFormsData[j].Form_Id;
                                        var resultFormName=moduleFormsData[j].Form_Name.trim();
                                        
                                        var newFormName = customFormName.find(form => form.Form_Id === resultFormId);
                                        var resultCustomName= newFormName ? newFormName.New_Form_Name : resultFormName;

                                        //Replace if translated form name exist
                                        if (translatedModulesAndForms?.forms[resultFormId]?.Translated_Form_Name) {
                                            resultCustomName = translatedModulesAndForms?.forms[resultFormId]?.Translated_Form_Name;
                                        }

                                        (moduleFormsData[j].New_Form_Name)?(moduleFormsData[j].New_Form_Name).trim():resultFormName;
                                        var resultSubForm=moduleFormsData[j].Sub_Form;
                                        var resultFormKey=resultFormName.replace(/ +/g,'-').toLowerCase();
                                        var showFormInMenu=formAccessRights[i].showInMenu;

                                        // get only the forms which employee has access to that
                                        if(resultFormId===formAccessRights[i].Form_Id)
                                        {
                                            // variable declaration
                                            let isModuleExist =0;
                                            let moduleData={};
                                            // navigation url is different for some of the forms
                                            // Split the formId for url formation
                                            var integrationFormIdArray=[166,167,168];
                                            var reportFormIdArray=[63,64,65,66];

                                            // check whether the formId included in above cases if yes need to form the url
                                            if(integrationFormIdArray.includes(resultFormId))
                                            {
                                                navigationUrl=(resultSubForm===0)?moduleFormsData[j].Module_Name.toLowerCase().replace(/ +/g,'-')+'/'+ integrationUrlConstant +'/'+ resultFormKey:"";
                                            }
                                            else if(reportFormIdArray.includes(resultFormId))
                                            {
                                                if(resultFormId!==63)
                                                {
                                                    navigationUrl=(resultSubForm===0)?moduleFormsData[j].Module_Name.toLowerCase().replace(/ +/g,'-')+'/'+ reportUrlConstant +'/'+ 'view-'+resultFormKey+'/':"";
                                                }
                                                else{
                                                    navigationUrl=(resultSubForm===0)?moduleFormsData[j].Module_Name.toLowerCase().replace(/ +/g,'-')+'/'+ reportUrlConstant +'/'+hrReportsUrlConstant +'/' :"";
                                                }
                                            }
                                            else if(resultFormId===2)
                                            {
                                                // Department Hierarchy form
                                                navigationUrl=(resultSubForm===0)?moduleFormsData[j].Module_Name.toLowerCase().replace(/ +/g,'-')+'/'+ departmentHierarchyUrlConstant :"";
                                            }
                                            // trulead form
                                            else if(resultFormId===220){
                                                // navigationUrl=(resultSubForm===0)? modulesAndFormsArgs.orgCode+'.truleadai.com/trulead-redirection':'';
                                                navigationUrl=(resultSubForm===0)? 'demo.truleadai.com/trulead-redirection':'';
                                            }
                                            else
                                            {
                                                navigationUrl=(resultSubForm===0)?moduleFormsData[j].Module_Name.toLowerCase().replace(/ +/g,'-')+'/'+resultFormKey:"";
                                            }

                                            // formation of menu module list
                                            for(let id in menuArray)
                                            {
                                                // check whether the moduleId available in result array
                                                // if exist then push the forms associated with that module
                                                if(menuArray[id].moduleId===moduleFormsData[j].Module_Id)
                                                {    
                                                    isModuleExist=1;

                                                    // function to push the forms along with existing forms in that module
                                                    var menuList = await displayFormList(showFormInMenu,resultFormId,resultSubForm,resultFormName,resultCustomName,navigationUrl,menuArray[id].formList)                                                         
                                                    menuArray[id].formList=menuList;
                                                }
                                            }
                                            // if module does not exist in output array we need to push the module details
                                            if(isModuleExist===0)
                                            {
                                                // Formation of module data
                                                moduleData={
                                                    moduleName:moduleFormsData[j].Module_Name,
                                                    moduleId:moduleFormsData[j].Module_Id,
                                                    formList:[]
                                                }
                                                //Replace if translated module name exist
                                                if (translatedModulesAndForms?.modules[moduleFormsData[j]?.Module_Id]?.Translated_Module_Name) {
                                                    moduleData.translatedModuleName = translatedModulesAndForms?.modules[moduleFormsData[j]?.Module_Id]?.Translated_Module_Name;
                                                }else{
                                                    moduleData.translatedModuleName = moduleFormsData[j].Module_Name;
                                                }
                                                // function to push the forms along with moduleData
                                                var menuList= await displayFormList(showFormInMenu,resultFormId,resultSubForm,resultFormName,resultCustomName,navigationUrl,moduleData.formList);
                                                menuArray.push(moduleData);
                                            }
                                            // formation of form access json
                                            var formLevelAccess=
                                            {
                                                formId:resultFormId,
                                                formName:resultFormName,
                                                customFormName:resultCustomName,
                                                parentFormId:resultSubForm,
                                                accessRights:
                                                {
                                                    add:formAccessRights[i].Role_Add,
                                                    update:formAccessRights[i].Role_Update,
                                                    delete:formAccessRights[i].Role_Delete,
                                                    view:formAccessRights[i].Role_View,
                                                    optionalChoice:formAccessRights[i].Role_Optional_Choice,
                                                    hrGroup:formAccessRights[i].Role_Hr_Group,
                                                    payrollGroup:formAccessRights[i].Role_Payroll_Group,
                                                    admin:(formAccessRights[i].isAdmin)?'admin':'',
                                                    isManager: formAccessRights[i].Is_Manager,
                                                    isRecruiter: formAccessRights[i].Is_Recruiter ? formAccessRights[i].Is_Recruiter : 'No'
                                                }
                                            }
                                            formDetails[resultFormKey]=formLevelAccess;
                                            formIdDetails[resultFormId]=formLevelAccess;
                                        }
                                    }
                                }

                                // stringify the access list since we are having keys are formed dynamically
                                let modulesAndFormResponse={
                                    moduleList:menuArray,
                                    formAccessList: JSON.stringify(formDetails),
                                    formIdAccessList: JSON.stringify(formIdDetails)
                                };
                                return modulesAndFormResponse;// return success response
                            }
                            else{
                                // throw access denied message
                                console.log("User does not have access to forms or error in retrieving access rights");
                                throw '_DB0106';
                            }
                        }
                        else{
                            console.log("Modules and forms does not extsis");
                            throw '_DB0107';
                        }
                    })
                );
            })
            .then(function (result) {
                return result;
            })
            //catch db-connectivity errors
            .catch(function (catchErrror) {
                console.log('Error in retrieve modules and form details block', catchErrror);
                // errorcode list used in this function
                let errorCodeList=['_DB0106','_DB0107'];
                throw  errorCodeList.includes(catchErrror)?(catchErrror):'ERE0014';
            })
        );
    }catch(getModulesAndFormsListMainCatchError){
        console.log('Error in the getModulesAndFormsList() function main catch block.',getModulesAndFormsListMainCatchError);
        throw (getModulesAndFormsListMainCatchError);
    }
}