const { ApolloError} = require('apollo-server-lambda');
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');

module.exports.getResignationPayslipDetails = async (parent, args, context, info) => {
    console.log('Inside getResignationPayslipDetails function');
    let organizationDbConnection;
    let logInEmpId=args.employeeId;
    let orgCode = context.Org_Code;
    
    try{
        // get the organization database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);

        let getSalaryDetails = await commonLib.payroll.getLastPayslipMonth(organizationDbConnection, orgCode, logInEmpId);
        
        organizationDbConnection?organizationDbConnection.destroy():null;
        return {errorCode:"",message:"Payslip details retrieved successfully.",payslipDetails:getSalaryDetails};
    }
    catch(e)
    {
        organizationDbConnection?organizationDbConnection.destroy():null;
        console.log("Error in getResignationPayslipDetails() function main catch block.",e);
        let errResult = commonLib.func.getError(e,'PR0122');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
