// require table names
const { ehrTables } = require('../../../common/tablealias');
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require validation file
const { validLoginEmployeeIsEligibleApprover,updateOvertimeClaimApprovalStatus,getCompensatoryOffBalanceDetailsForOvertime,getEmployeeShiftAllowance,validateEmployeeClaimMonth,validateOvertimeWorkSchedule} = require('./overtimeCommonFunctions');
// require common constant files
const { formName,systemLogs,defaultValues } = require('../../../common/appconstants');
// require moment timezone
let moment = require('moment-timezone');
// Organization database connection
const knex = require('knex');

/**
 * Insert or update the employee compensatory off balance
 * @param {Object} orgDb - Organization Database Connection Object
 * @param {Object} updateCompensatoryOffBalanceInputs - Input params
 * @returns {String|Number} - Return error string when an error occured. Return the compensatory off balance id on success
 */
async function updateCompensatoryOff(orgDb,updateCompensatoryOffBalanceInputs){
    try{
        console.log('Inside the updateCompensatoryOff() function.',updateCompensatoryOffBalanceInputs);
        let insertUpdateCompOffBalanceResponse = await orgDb(ehrTables.compensatoryOffBalance)
        .select('Comp_Off_Balance_Id', 'Total_Hours', 'Total_Days', 'Remaining_Days','Source','Comp_Off_Overtime_Claim_Balance')
        .where('Worked_Date',updateCompensatoryOffBalanceInputs.regularFromDate)
        .where('Employee_Id',updateCompensatoryOffBalanceInputs.otEmployeeId)
        .then(async(compOffBalanceDetails) => {
            // if the compensatory off balance exist
            if(compOffBalanceDetails && compOffBalanceDetails.length > 0){
                compOffBalanceDetails = compOffBalanceDetails[0];
                /** If the old source is 'Overtime Claim' then the new source will also be 'Overtime Claim'. If the old source is
                 * 'Attendance'/'Attendance And Overtime Claim' then new source will also be 'Attendance And Overtime Claim'.
                 */
                let updateCompOffSource = (defaultValues.compOffOvertimeClaimSourceName === compOffBalanceDetails.Source) ? defaultValues.compOffOvertimeClaimSourceName : defaultValues.compOffAttendanceOvertimeClaimSourceName;
                /** The total hours should not be updated from here. The purpose of total hours is to present the attendance eligible hours. It will updated
                 * while adding or updating the compensatory off balance from the attendance form. It will be added while inserting the compensatory-off
                 * balance from the additional wage claim form. Form the JSON to update the below inputs,
                 */
                let updateDetails = {
                    Remaining_Days: compOffBalanceDetails.Remaining_Days + updateCompensatoryOffBalanceInputs.newTotalDays,
                    Total_Days:compOffBalanceDetails.Total_Days + updateCompensatoryOffBalanceInputs.newTotalDays,
                    Source: updateCompOffSource,
                    Comp_Off_Overtime_Claim_Balance: compOffBalanceDetails.Comp_Off_Overtime_Claim_Balance + updateCompensatoryOffBalanceInputs.newTotalDays
                }
                console.log('Compensatory off balance updateDetails:',updateDetails);
                // update the compensatory off balance
                let updateCompOffBalanceResponse = await orgDb(ehrTables.compensatoryOffBalance)
                .update(updateDetails)
                .where('Worked_Date',updateCompensatoryOffBalanceInputs.regularFromDate)
                .where('Employee_Id',updateCompensatoryOffBalanceInputs.otEmployeeId)
                .where('Comp_Off_Balance_Id',compOffBalanceDetails.Comp_Off_Balance_Id)
                .then(() => {
                    console.log("Compensatory off-balance updated for the unique id,",compOffBalanceDetails.Comp_Off_Balance_Id,' for the employee id ',updateCompensatoryOffBalanceInputs.otEmployeeId);
                    return compOffBalanceDetails.Comp_Off_Balance_Id;
                })
                .catch(updateCatchError => {
                    console.log('Error occured while updating the compensatory off balance in the updateCompensatoryOff() function .catch block. ',updateCatchError,' for the employee id: ',updateCompensatoryOffBalanceInputs.otEmployeeId);
                    return 'error';
                });
                console.log('Compensatory off balance update final response:',updateCompOffBalanceResponse);
                return updateCompOffBalanceResponse;
            }else{
                let compOffBalanceAttendanceRemainingTotalHours = null;
                let expiryDate = '';
                if(updateCompensatoryOffBalanceInputs.compOffExpiryType=='Fixed Days')
                {
                    //add the compensatory off expiry days to the regular start date
                    expiryDate = moment(updateCompensatoryOffBalanceInputs.regularFromDate, "YYYY-MM-DD").add(updateCompensatoryOffBalanceInputs.compOffExpiryDays, 'days').format('YYYY-MM-DD');
                }
                else if(updateCompensatoryOffBalanceInputs.compOffExpiryType=='Same Payroll Month')
                {
                    let regularFromDateMoment = moment(updateCompensatoryOffBalanceInputs.regularFromDate);
                    let salaryMonthYear = regularFromDateMoment.format('MM')+','+regularFromDateMoment.format('YYYY');
                    let {Last_SalaryDate} = await commonLib.func.retrieveSalaryDates(updateCompensatoryOffBalanceInputs.orgCode,salaryMonthYear,0,updateCompensatoryOffBalanceInputs.regularFromDate,process.env.domainName,process.env.webAddress);
                    expiryDate = Last_SalaryDate; 
                } else if(updateCompensatoryOffBalanceInputs.compOffExpiryType=='Calendar Year') {
                    expiryDate = await calculateCompOffBalanceExpiryDate(updateCompensatoryOffBalanceInputs.orgCode,orgDb,updateCompensatoryOffBalanceInputs.regularFromDate,12);
                } else if(updateCompensatoryOffBalanceInputs.compOffExpiryType=='Fiscal Year') {
                    let orgDetails= await commonLib.func.getOrgDetails(updateCompensatoryOffBalanceInputs.orgCode,orgDb,0);
                    if(orgDetails && Object.keys(orgDetails).length > 0){
                        //Get the fiscal end month
                        let fiscalEndMonth = parseInt(orgDetails.Fiscal_StartMonth)-1;
                        //If the fiscal start month is jan then fiscal end month should be dec
                        if (fiscalEndMonth == 0) {
                            fiscalEndMonth = 12;
                        }
                        expiryDate = await calculateCompOffBalanceExpiryDate(updateCompensatoryOffBalanceInputs.orgCode,orgDb,updateCompensatoryOffBalanceInputs.regularFromDate,fiscalEndMonth);
                    }else{
                        console.log('Org details not found in the updateCompensatoryOff() function',orgDetails);
                        return 'error';
                    }
                }
                // add 1 minute to the regularto date-time
                let newRegularToNextMinute = (moment(updateCompensatoryOffBalanceInputs.regularTo, 'YYYY-MM-DD hh:mm:ss').add(1, 'minute')).format('YYYY-MM-DD hh:mm:ss');
                
                /** Get the maximum punch out date-time for the approved attendance record if the attendance records punch out date-time
                 * is greater then regular to date-time and the consideration to date-time.
                 */
                let getAttendanceMaxPunchOutDateTime = await orgDb(ehrTables.empAttendance)
                .select(orgDb.raw("MAX(CONCAT_WS(' ',PunchOut_Date,PunchOut_Time)) as punchOutDateTime"))
                .whereIn('Attendance_Id', updateCompensatoryOffBalanceInputs.approvedAttendanceIds)
                .where(qb => {
                    qb.whereBetween(orgDb.raw('CONCAT(PunchIn_Date, " ", PunchIn_Time)'), [newRegularToNextMinute, updateCompensatoryOffBalanceInputs.considerationTo])
                    qb.orWhereBetween(orgDb.raw('CONCAT(PunchOut_Date, " ", PunchOut_Time)'), [newRegularToNextMinute, updateCompensatoryOffBalanceInputs.considerationTo])
                });
                // get the attendance max punch out date-time
                let attendancePunchOutDateTime = (getAttendanceMaxPunchOutDateTime && getAttendanceMaxPunchOutDateTime.length > 0) ? getAttendanceMaxPunchOutDateTime[0].punchOutDateTime : '';

                if(attendancePunchOutDateTime){
                    /**
                     * Calculate total hours from new regular to date-time to attendance punch out date-time and update in the total hours.
                     * So while presenting the compensatory off balance the total hours(attendance eligible hours) will be presented from the 
                     * punch in date-time to regular to date-time and the remaining time which we are calculating here will be added and presented.
                     */
                    let compOffTotalHoursStartTime = moment(newRegularToNextMinute, 'YYYY-MM-DD hh:mm:ss');
                    let compOffTotalHoursEndTime = moment(attendancePunchOutDateTime, 'YYYY-MM-DD hh:mm:ss');
                    // Get difference between 2 dates in seconds
                    let CompOfftimeDiff = compOffTotalHoursEndTime.diff(compOffTotalHoursStartTime, 'seconds');
                    // convert timeDiff seconds to hours
                    CompOfftimeDiff = (CompOfftimeDiff / 3600).toFixed(2);
                    compOffBalanceAttendanceRemainingTotalHours = CompOfftimeDiff;
                    console.log('Compensatory off balance total hours from the regular-to date-time(+1minute) ',newRegularToNextMinute,' till the regular date - attendance max punch out date-time ',attendancePunchOutDateTime,' for the employee id, ', updateCompensatoryOffBalanceInputs.otEmployeeId,' is ',compOffBalanceAttendanceRemainingTotalHours);
                }else{
                    console.log('Attendance punch out date-time is less than or equal to the regular to date-time. So the compensatory off balance total hours from the regular-to date-time(+1minute) ',newRegularToNextMinute,' till the regular date - attendance max punch out date-time ',attendancePunchOutDateTime,' for the employee id, ', updateCompensatoryOffBalanceInputs.otEmployeeId,' is ',0);
                    compOffBalanceAttendanceRemainingTotalHours = null;
                }
                // insert inputs
                let insertDetails = {
                    Employee_Id: updateCompensatoryOffBalanceInputs.otEmployeeId,
                    Worked_Date: updateCompensatoryOffBalanceInputs.regularFromDate,
                    Expiry_Date: expiryDate,
                    Total_Hours: compOffBalanceAttendanceRemainingTotalHours,
                    Remaining_Hours: updateCompensatoryOffBalanceInputs.totalHours,
                    Total_Days: updateCompensatoryOffBalanceInputs.newTotalDays,
                    Remaining_Days: updateCompensatoryOffBalanceInputs.newTotalDays,
                    Source: defaultValues.compOffOvertimeClaimSourceName,
                    Comp_Off_Overtime_Claim_Balance: updateCompensatoryOffBalanceInputs.newTotalDays
                };
                
                // insert the compensatory off balance
                let insertCompOffBalanceResponse = await orgDb(ehrTables.compensatoryOffBalance)
                .insert(insertDetails)
                .then((insertResponse) => {
                    console.log("Compensatory off-balance insert response. ",insertResponse,' for the employee id ',updateCompensatoryOffBalanceInputs.otEmployeeId);
                    return insertResponse[0];
                })
                .catch(insertCatchError => {
                    console.log('Error occured while inserting the compensatory off balance in the updateCompensatoryOff() function .catch block. ',insertCatchError,' for the employee id: ',updateCompensatoryOffBalanceInputs.otEmployeeId);
                    return 'error';
                });
                console.log('Compensatory off balance insert final response:',insertCompOffBalanceResponse);
                return insertCompOffBalanceResponse;
            }
        })
        .catch(compOffBalanceDetailsError =>{
            console.log('Error occured while retrieving the compensatory off balance in the updateCompensatoryOff() function .catch block.', compOffBalanceDetailsError,' for the employee id ',updateCompensatoryOffBalanceInputs.otEmployeeId);
            return 'error';
        });
        return insertUpdateCompOffBalanceResponse;
    }catch(updateCompensatoryOffMainCatchError){
        console.log('Error in the updateCompensatoryOff() function main catch block.',updateCompensatoryOffMainCatchError);
        return 'error';
    }
}

/**
 * Insert the employee shift allowance in the shift allowance table.
 * Update the employee shift allowance in the shift allowance table and insert the shift allowance history 
 * when the old and new shift allowance amount varied.
 * @param {Object} updateShiftAllowanceInputs - input params
 * @param {Number} updateShiftAllowanceInputs.otEmployeeId - overtime claim employee id
 * @param {Date} updateShiftAllowanceInputs.regularFromDate - regular from date from the work schedule on which the overtime claim duration falls
 * @param {Number} updateShiftAllowanceInputs.workScheduleId - Selected work schedule id to claim shift allowance for the overtime claim
 * @param {Array} updateShiftAllowanceInputs.shiftMode - manual | both
 * @param {Number} updateShiftAllowanceInputs.loginEmpId - overtime claim login employee id
 * @param {Number} updateShiftAllowanceInputs.loginEmpTimeZoneCurrentDateTime - current date time based on the login employee timezone
 * @param {Object} orgDb - Organization Database connection object
 * @returns {String|Number} - Return error string when an error occured. Return the request id on success
 */
async function updateEmployeeShiftAllowance(orgDb,updateShiftAllowanceInputs){
    try{
        let insertUpdateShiftAllowanceResponse = await orgDb(ehrTables.empShift)
        .select('Request_Id','No_of_Days','Shift_Amount','Payroll_Mid','Approval_Status')
        .where('Employee_Id',updateShiftAllowanceInputs.otEmployeeId)
        .where('ShiftType_Id',updateShiftAllowanceInputs.workScheduleId)
        .where('Start_Date',updateShiftAllowanceInputs.regularFromDate)
        .whereNotIn('Approval_Status',['Rejected'])
        .then(async(shiftAllowanceDetails) => {
            // if the shift allowance exist for the regular from date
            if(shiftAllowanceDetails && shiftAllowanceDetails.length > 0){
                shiftAllowanceDetails = shiftAllowanceDetails[0];
                if(shiftAllowanceDetails.Approval_Status != 'Paid'){
                    let calculateShiftAllowanceInputs = {
                        employeeId: updateShiftAllowanceInputs.otEmployeeId,
                        workScheduleId: updateShiftAllowanceInputs.workScheduleId,
                        shiftMode: updateShiftAllowanceInputs.shiftMode,
                        source: 'updateemployeeshiftallowance'
                    };
                    let shiftAllowance = await getEmployeeShiftAllowance(orgDb,calculateShiftAllowanceInputs);
                    // if the employee shift allowance is greater than zero.
                    if(shiftAllowance > 0){
                        let newTotalDays = shiftAllowanceDetails.No_of_Days + 1;

                        // update inputs
                        let shiftAllowanceUpdateDetails = {
                            Lock_Flag: 0,
                            No_of_Days: newTotalDays,
                            Shift_Amount: shiftAllowanceDetails.Shift_Amount + shiftAllowance,
                            Payroll_Mid: !(shiftAllowanceDetails.Payroll_Mid) ? updateShiftAllowanceInputs.loginEmpId:shiftAllowanceDetails.Payroll_Mid,
                            Approval_Status: 'Complete',
                            Approved_By: updateShiftAllowanceInputs.loginEmpId,
                            Approved_On: updateShiftAllowanceInputs.loginEmpTimeZoneCurrentDateTime
                        };
                        
                        // update the employee shift allowance
                        return (orgDb
                        .transaction(function (trx) {
                            return (
                            orgDb(ehrTables.empShift)
                            .update(shiftAllowanceUpdateDetails)
                            .where('Request_Id',shiftAllowanceDetails.Request_Id)
                            .where('Employee_Id',updateShiftAllowanceInputs.otEmployeeId)
                            .where('ShiftType_Id',updateShiftAllowanceInputs.workScheduleId)
                            .where('Start_Date',updateShiftAllowanceInputs.regularFromDate)
                            .transacting(trx)
                            .then(async() => {
                                
                                /** If the old and new shift allowance amount differs */
                                if(shiftAllowanceDetails.Shift_Amount != shiftAllowance){
                                    let insertAuditShiftAmountDetails = {
                                        Request_Id: shiftAllowanceDetails.Request_Id, 
                                        Old_ShiftAmount: shiftAllowanceDetails.Shift_Amount,
                                        New_ShiftAmount: shiftAllowance,
                                        Modified_By: updateShiftAllowanceInputs.loginEmpId, 
                                        Modified_On: updateShiftAllowanceInputs.loginEmpTimeZoneCurrentDateTime
                                    }
                                    
                                    return(
                                    orgDb(ehrTables.auditEmpshift)
                                    .insert(insertAuditShiftAmountDetails)
                                    .transacting(trx)
                                    .then((insertAuditShiftAllowanceResponse) => {
                                        return shiftAllowanceDetails.Request_Id;
                                    })
                                    )
                                }else{
                                    return shiftAllowanceDetails.Request_Id;
                                }
                            })
                            )
                        })
                        .then(async(shiftAllowanceResponseRequestId) => {
                            return shiftAllowanceResponseRequestId;
                        })
                        .catch(updateShiftAllowanceCatchError => {
                            console.log('Error occured while updating the shift allowance in the updateEmployeeShiftAllowance() function .catch block. ',updateShiftAllowanceCatchError,' for the employee id: ',updateShiftAllowanceInputs.otEmployeeId);
                            return 'error';
                        })
                        );
                    }else{
                        console.log('Shift allowance cannot be updated as the shift allowance is zero for the employee id: ',updateShiftAllowanceInputs.otEmployeeId);
                        return 'error';
                    }
                }else{
                    console.log('Shift allowance cannot be updated as the shift allowance is already paid for the regular date for the employee id: ',updateShiftAllowanceInputs.otEmployeeId);
                    return 'error';
                }
            }else{
                let calculateShiftAllowanceInputs = {
                    employeeId: updateShiftAllowanceInputs.otEmployeeId,
                    workScheduleId: updateShiftAllowanceInputs.workScheduleId,
                    shiftMode: updateShiftAllowanceInputs.shiftMode,
                    source: 'updateemployeeshiftallowance'
                };
                let shiftAllowance = await getEmployeeShiftAllowance(orgDb,calculateShiftAllowanceInputs);
                
                // if the employee shift allowance is greater than zero
                if(shiftAllowance > 0){
                    let maxRequestId = await getShiftAllowanceMaxRequestId(orgDb);
                    // add 1 to the max request id and assign it is as new request id
                    let newRequestId = maxRequestId + 1;
                    // shift allowance insert inputs
                    let shiftAllowanceInsertDetails = {
                        Request_Id : newRequestId,
                        Employee_Id: updateShiftAllowanceInputs.otEmployeeId,
                        ShiftType_Id: updateShiftAllowanceInputs.workScheduleId,
                        Start_Date: updateShiftAllowanceInputs.regularFromDate,
                        End_Date: updateShiftAllowanceInputs.regularFromDate,
                        Approval_Status: 'Complete',
                        Approver_Id: updateShiftAllowanceInputs.loginEmpId,
                        Payroll_Mid: updateShiftAllowanceInputs.loginEmpId,
                        Submission_Date: updateShiftAllowanceInputs.regularFrom,
                        No_Of_Days: 1,
                        Shift_Amount: shiftAllowance,
                        Added_By:updateShiftAllowanceInputs.loginEmpId,
                        Added_On: updateShiftAllowanceInputs.loginEmpTimeZoneCurrentDateTime,
                        Approved_By:updateShiftAllowanceInputs.loginEmpId,
                        Approved_On: updateShiftAllowanceInputs.loginEmpTimeZoneCurrentDateTime
                    };
                    
                    // insert the shift allowance
                    let insertShiftAllowanceResponse = await orgDb(ehrTables.empShift)
                    .insert(shiftAllowanceInsertDetails)
                    .then(() => {
                        console.log('Employee shift allowance inserted for the employee id, ',updateShiftAllowanceInputs.otEmployeeId);
                        return newRequestId;
                    })
                    .catch(insertShiftAllowanceCatchError => {
                        console.log('Error occured while inserting the employee shift allowance in the updateEmployeeShiftAllowance() function .catch block. ',insertShiftAllowanceCatchError,' for the employee id: ',updateShiftAllowanceInputs.otEmployeeId);
                        return 'error';
                    });
                    
                    return insertShiftAllowanceResponse;
                }else{
                    console.log('Shift allowance cannot be inserted as the shift allowance is zero for the employee id: ',updateShiftAllowanceInputs.otEmployeeId);
                    return 'error';
                }
            }
        })
        .catch(shiftAllowanceDetailsError =>{
            console.log('Error occured while retrieving the employee shift allowance in the updateEmployeeShiftAllowance() function .catch block.', shiftAllowanceDetailsError,' for the employee id: ',updateShiftAllowanceInputs.otEmployeeId);
            return 'error';
        });
        console.log('Final response inside the update shift allowance function',insertUpdateShiftAllowanceResponse);
        return insertUpdateShiftAllowanceResponse;
    }catch(updateEmployeeShiftAllowanceMainCatchError){
        console.log('Error in the updateEmployeeShiftAllowance() function main catch block.',updateEmployeeShiftAllowanceMainCatchError);
        return 'error';
    }
}

/**
 * Get the maximum value of the column 'Request_Id' from the emp_shift table.
 * @param {Object} orgDb - Organization Database connection object
 * @returns {Number} - Return the maximum request id
 * @throws {Object} - Throw an error object when an error occured while fetching the maximum value of the request id from the table.
 */
async function getShiftAllowanceMaxRequestId(orgDb){
    return(
    await orgDb(ehrTables.empShift)
    .max('Request_Id as maxRequestId')
    .then((shiftAllowanceMaxIdDetails) => {
        return (shiftAllowanceMaxIdDetails && shiftAllowanceMaxIdDetails.length>0) ? shiftAllowanceMaxIdDetails[0].maxRequestId : 0;

    }).catch(shiftAllowancMaxRequestIdCatchError => {
        console.log('Error in the getShiftAllowanceMaxRequestId() function .catch block',shiftAllowancMaxRequestIdCatchError);
        throw shiftAllowancMaxRequestIdCatchError;
    })
    );
}

//Function to calculate comp off expiry type for fiscal year, calendar year
async function calculateCompOffBalanceExpiryDate(orgCode,orgDb,startDate,salaryMonth) {
    try{
        let salaryYear = moment(startDate).format('YYYY');
        let {Last_SalaryDate} = await commonLib.func.getSalaryDay(orgCode,orgDb,salaryMonth,salaryYear);
        console.log("Inside calculateCompOffBalanceExpiryDate function, Last_SalaryDate",Last_SalaryDate)
        if (moment(startDate) <= moment(Last_SalaryDate)) {
            return Last_SalaryDate;
        } else {
            salaryYear += 1;
            let salaryDateDetails = await commonLib.func.getSalaryDay(orgCode,orgDb,salaryMonth,salaryYear);
            console.log("Inside calculateCompOffBalanceExpiryDate function, salaryDateDetails",salaryDateDetails);
            return salaryDateDetails.Last_SalaryDate;
        }
    }catch(expiryCatchError){
        console.log('Error in the calculateCompOffBalanceExpiryDate() function main catch block.',expiryCatchError);
        throw expiryCatchError;
    }
}
    
// resolver function to insert or update the overtime-compensatory off balance and overtime - shift allowance
module.exports.updateOvertimeOtherClaims = async (parent, args, context, info) => {
    console.log("Inside updateOvertimeOtherClaims() function.");
    let orgDb = '';
    let { otDetail } = args;
    let { overtimeClaimId } = otDetail;
    let updateotStatusResponse = 0;
    let additionalWageClaimErrorCode = '';
    try{
        let { loginEmpTimeZoneCurrentDateTime, newApprovalStatus, isLoggedInEmpAdmin, overtimeSettings, isCompOffOrShiftAllowanceEnabled, oneApprovalRecordComment,employeeClaimedMonthStartDate } = args;
        let { otEmployeeId,otStartDateTime,otEndDateTime,totalHours,workScheduleId,oldApprovalStatus,lastApprovedBy } = otDetail;

        let loginEmpId = context.Employee_Id;
        let userIp = context.User_Ip;
        orgDb = knex(context.connection.OrganizationDb);

        let validateLoginEmployeeIsApproverArgs = {
            isLoggedInEmpAdmin:isLoggedInEmpAdmin,
            newApprovalStatus: newApprovalStatus,
            lastApprovedBy: lastApprovedBy,
            otEmployeeId: otEmployeeId,
            loginEmpId: loginEmpId,
            oldApprovalStatus: oldApprovalStatus//approval status associated with the record
        };
        let updateOvertimeStatusRecord = await validLoginEmployeeIsEligibleApprover(validateLoginEmployeeIsApproverArgs,orgDb);
        
        if(parseInt(updateOvertimeStatusRecord) === 1){
            let otStatusApprovalInputs = { 
                overtimeClaimId: overtimeClaimId,
                newApprovalStatus: newApprovalStatus,
                approvalComment: oneApprovalRecordComment,
                loginEmpTimeZoneCurrentDateTime: loginEmpTimeZoneCurrentDateTime,
                loginEmpId: loginEmpId,
                userIp:userIp,
                formName: formName.overtime,
                roleUpdate: systemLogs.roleUpdate
            };
            /** If the compensatory off or shift allowance is not enabled */
            if(parseInt(isCompOffOrShiftAllowanceEnabled) === 0){
                /** Call the function to update the additional wage claim status and log it */
                updateotStatusResponse = await updateOvertimeClaimApprovalStatus(orgDb,otStatusApprovalInputs);
                
            }else{
                let overtimeClaimMonthPayslipExist = await validateEmployeeClaimMonth(orgDb,otEmployeeId,employeeClaimedMonthStartDate);

                /** If the payslip is generated for the employee for the additional wage claim processing month */
                if(overtimeClaimMonthPayslipExist === 1){
                    additionalWageClaimErrorCode = 'OT0137';
                    updateotStatusResponse=0;
                }else{
                    // flag to allow the user to claim overtime from "shift - consideration start date time" when the shift falls on weekoff or holiday
                    let allowRegularHoursOvertimeForWeekoffHoliday = overtimeSettings.Allow_Regular_Hours_Overtime_For_Weekoff_holiday;
                    let validateWorkScheduleInputs = {
                        employeeId: otEmployeeId,
                        otStartTime: otStartDateTime,
                        allowRegularHoursOvertimeForWeekoffHoliday:allowRegularHoursOvertimeForWeekoffHoliday
                    }
                    let validateOTWorkscheduleResponse = await validateOvertimeWorkSchedule(orgDb,validateWorkScheduleInputs);
                    /** If the overtime start date-time is valid */
                    if(!validateOTWorkscheduleResponse.error){
                        let currentWorkScheduleDetails = validateOTWorkscheduleResponse.currentWorkScheduleDetails;
                        if(currentWorkScheduleDetails && Object.keys(currentWorkScheduleDetails).length > 0){
                            // get workSchedule details
                            let { regularFrom, regularTo, considerationFrom, considerationTo,errorCode } = currentWorkScheduleDetails;
                            
                            // Check regularFrom, regularTo, considerationFrom, considerationTo is not empty and the error code does not exist
                            if (regularTo && regularFrom && considerationFrom && considerationTo && !errorCode){
                                let workScheduleDetailsBeforeFormat = validateOTWorkscheduleResponse.workScheduleDetailsBeforeFormat;
                                /**
                                 * manual - employee has to manually apply for shift allowance
                                 * both - refers to manual and auto. auto refers to applying shift allowance for the employee actual regular shift
                                */
                                let shiftMode = ['manual','both'];

                                let regularFromDate = moment(regularFrom).format("YYYY-MM-DD");// convert date time to date format.

                                /** The overtime claim start date may fall on next day in case of overlapping shift. The attendance count will be 
                                 * fetched based on the consideration start and end time.So get attendance count by sending the regular from date 
                                 * and current work schedule details and validate the attendance is approved or not for the given work schedule details. */
                                let getAttendanceDetails  = await commonLib.employees.getAttendance(regularFromDate, otEmployeeId, orgDb, 'get-approved-status-attendance-id', '',workScheduleDetailsBeforeFormat);
                                // If an error returned we need to consider attendance is not approved
                                let approvedAttendanceIds = (getAttendanceDetails.errorCode) ? [] : getAttendanceDetails.attendanceDetails;
                                
                                // if the attendance is not approved
                                if(approvedAttendanceIds.length <= 0){
                                    console.log('Attendance is not approved. Response from the getAttendance() is ',getAttendanceDetails,' for the overtimeClaimId.',overtimeClaimId);
                                    additionalWageClaimErrorCode = (getAttendanceDetails.errorCode) ? (getAttendanceDetails.errorCode): 'OT0011';
                                    updateotStatusResponse = 0;
                                }else{
                                    let calculateCompensatoryOffBalance = 0;
                                    let isValidatedShiftAllowanceApplicable = 1;
                                    let regularShiftWorkScheduleId = validateOTWorkscheduleResponse.currentWorkScheduleDetails.workScheduleId;

                                    /** Validate overtime end date-time is less than the regular to date-time when the regular hours-overtime claim is applicable for week off and holiday 
                                     * and when the overtime is claimed on weekoff or holiday. Because for this duration the shift allowance is not applicable as it will be automatically
                                     * handled during payslip generation.*/
                                    if (allowRegularHoursOvertimeForWeekoffHoliday === 1 && !(validateOTWorkscheduleResponse.isWorkingDay) && 
                                    otStartDateTime >= considerationFrom && otEndDateTime <= regularTo){
                                        //Shift allowance is not applicable as the overtime is claimed for the weekoff or holiday on or before the regular to date-time
                                        isValidatedShiftAllowanceApplicable = 0;
                                    }
                                    
                                    /** Compensatory off-balance eligiblity will be calculated and balance will updated for the regular working hours in the 
                                     * attendance form during approval. In the overtime claim form the compensatory off balance has to be calculated 
                                     * after regular to date-time till the overtime end date-time. So validate it before calculating the compensatory off balance. */

                                    /** If the overtime start date time is greater than or equal to 'regular to' date-time then the
                                     *  compensatory balance can be calculated from otStartDateTime to otEndDateTime */
                                    if(otStartDateTime >= regularTo){
                                        calculateCompensatoryOffBalance = 1;
                                    }else{
                                        /** If the overtime end date-time is less than the 'regular to' date time */

                                        /** If overtime end date-time is less than the 'regular to' date-time then the compensatory balance should not 
                                         * be calculated here as the regular hours compensatory off balance will be calculated in the attendance 
                                         * form during approval. */
                                        if (otEndDateTime < regularTo){
                                            console.log('Employee compensatory off-balance will be updated from the attendance form. So it will not be calculated and updated from the additional wage claim form for the employee id, ',otEmployeeId,' as the overtime start date-time and the overtime end date-time is less than the regular to date-time.');                                
                                            calculateCompensatoryOffBalance = 0;
                                        }else{
                                            /** If overtime end date-time is greater than or equal to the 'regular to' date-time then the compensatory balance can 
                                             * be calculated from the 'regular to' date-time to the overtime end date-time. */
                                            calculateCompensatoryOffBalance = 1;
                                        }
                                    }
                                    /** If the compensatory off balance or the shift allowance can be calculated and updated here from the additional wage claim form */
                                    if(calculateCompensatoryOffBalance === 1){
                                        let updateOtApprovalStatus = 1;
                                        let newOtStartDateTime = regularTo;// replace the ot start time to 'regular to' date time

                                        //compensatory off balance calculation inputs
                                        let calculateCompensatoryOffBalanceInputs = {
                                            employeeId: otEmployeeId,
                                            otStartTime: newOtStartDateTime,
                                            otEndTime : otEndDateTime,
                                            workScheduleDetail: currentWorkScheduleDetails,
                                            action: 'updateovertimecompoffbalance',
                                            regularFromDate: regularFromDate,
                                            fetchEmployeeWorkingDaySpecialWageConfig: 1
                                        };
                                        //calculate the compensatory off balance for the overtime hours
                                        let compensatoryOffBalanceResponse = await getCompensatoryOffBalanceDetailsForOvertime(orgDb,calculateCompensatoryOffBalanceInputs);
                                        console.log('Compensatory off eligiblility calculation inputs:',calculateCompensatoryOffBalanceInputs,'and the response from the getCompensatoryOffBalanceDetailsForOvertime() function for the  employee id, ',otEmployeeId,' is, ',compensatoryOffBalanceResponse);

                                        let compensatoryOffBalance = compensatoryOffBalanceResponse.compensatoryOffBalance;
                                        let compOffExpiryDays = compensatoryOffBalanceResponse.employeeSpecialWagesConfiguration.Comp_Off_Expiry_Days;
                                        let compOffExpiryType = compensatoryOffBalanceResponse.employeeSpecialWagesConfiguration.Comp_Off_Expiry_Type;
                                        isEmployeeEligibleForShiftAllowance = compensatoryOffBalanceResponse.isEmployeeEligibleForShiftAllowance;

                                        // if the comp off is applicable for overtime and if the compensatory off balance is greater than zero
                                        if(overtimeSettings.Comp_Off_Applicable_For_Overtime && compensatoryOffBalance > 0){
                                            let updateCompensatoryOffBalanceInputs = {
                                                otEmployeeId: otEmployeeId,
                                                newTotalDays : compensatoryOffBalance,
                                                regularFromDate: regularFromDate,
                                                regularTo: regularTo,
                                                compOffExpiryType: compOffExpiryType,
                                                compOffExpiryDays: compOffExpiryDays,
                                                approvedAttendanceIds: approvedAttendanceIds,
                                                considerationTo:considerationTo,
                                                orgCode: context.Org_Code
                                            };
                                            // call the function to update the compensatory off balance for the employee
                                            let updateCompensatoryOffResponse = await updateCompensatoryOff(orgDb,updateCompensatoryOffBalanceInputs);
                                            
                                            if(updateCompensatoryOffResponse !== 'error'){
                                                // form inputs to update system log. Update Additional Wage Claim  - Compensatory Off Balance - `UID of compensatory off balance record`
                                                let updateCompOffBalanceSystemLogParams = {
                                                    action: systemLogs.roleUpdate,
                                                    userIp: userIp,
                                                    employeeId: loginEmpId,
                                                    formName: formName.overtime,
                                                    trackingColumn: ' - '+formName.compensatoryOffBalance,
                                                    organizationDbConnection: orgDb,
                                                    uniqueId: updateCompensatoryOffResponse//uId of the compensatory off balance record
                                                };

                                                // call function createSystemLogActivities() to update system log activities
                                                await commonLib.func.createSystemLogActivities(updateCompOffBalanceSystemLogParams);
                                            }else{
                                                console.log('Compensatory off balance not updated for the input,',updateCompensatoryOffBalanceInputs,' and workschedule details, ',currentWorkScheduleDetails);
                                                updateOtApprovalStatus = 0;
                                            }
                                        }else{
                                            console.log('Compensatory off flag value in Overtime settings is',overtimeSettings.Comp_Off_Applicable_For_Overtime,'.Employee compensatory off-balance is not updated in the table as the compensatory off-balance is zero.');
                                        }
                                        
                                        /**
                                         * Validate the shift allowance is applicable for overtime and also validate whether compensatory off balance is 
                                         * updated or not when the compensatory off is applicable. If compensatory off balance is not updated we do not need to
                                         * update the shift allowance
                                         */
                                        if(overtimeSettings.Shift_Allowance_Applicable_For_Overtime && workScheduleId && updateOtApprovalStatus){
                                            //if the employee shift allowance is eligible based on the settings
                                            if(isValidatedShiftAllowanceApplicable === 1 && isEmployeeEligibleForShiftAllowance > 0 && workScheduleId!= regularShiftWorkScheduleId){
                                                let updateEmployeeShiftAllowanceInputs = {
                                                    otEmployeeId: otEmployeeId,
                                                    regularFrom:regularFrom,
                                                    regularFromDate: regularFromDate,
                                                    workScheduleId: workScheduleId,
                                                    shiftMode: shiftMode,
                                                    loginEmpId:loginEmpId,
                                                    loginEmpTimeZoneCurrentDateTime:loginEmpTimeZoneCurrentDateTime
                                                };

                                                // call the function to update the shift allowance for the employee
                                                let updateShiftAllowanceResponse = await updateEmployeeShiftAllowance(orgDb,updateEmployeeShiftAllowanceInputs);
                                                
                                                if(updateShiftAllowanceResponse !== 'error'){
                                                    // form inputs to update system log. Ex: Update Additional Wage Claim - Shift allowance for the employee(s)' 
                                                    let updateShiftAllowanceSystemLogParams = {
                                                        action: systemLogs.roleUpdate,
                                                        userIp: userIp,
                                                        employeeId: loginEmpId,
                                                        formName: formName.overtime,
                                                        trackingColumn: ' - '+formName.shiftAllowance,
                                                        organizationDbConnection: orgDb,
                                                        uniqueId: updateShiftAllowanceResponse
                                                    };

                                                    // call function createSystemLogActivities() to update system log activities
                                                    await commonLib.func.createSystemLogActivities(updateShiftAllowanceSystemLogParams);
                                                }else{
                                                    updateOtApprovalStatus = 0;
                                                    console.log('Shift allowance not updated for the input,',updateEmployeeShiftAllowanceInputs,' and workschedule details, ',currentWorkScheduleDetails);
                                                }
                                            }else{
                                                console.log('Employee shift allowance is not updated in the table as the shift allowance is not eligible for the overtimeClaimId, ',overtimeClaimId,' for the work schedule',currentWorkScheduleDetails,' and the shift allowance work schedule:',workScheduleId);
                                            }
                                        }else{
                                            console.log('Employee shift allowance in Overtime settings is,',overtimeSettings.Shift_Allowance_Applicable_For_Overtime,'. and the Work schedule id is,',workScheduleId,'.Employee shift allowance is not updated in the table as the shift allowance is not applicable for the overtime hours or the work schedule id is zero for the employee id, ',otEmployeeId,' or the compensatory off balance for this employee id is not udpated and the response is,.',updateOtApprovalStatus);
                                        }

                                        /** When either compensatory off balance or shift allowance is updated then ot approval status will be updated. */
                                        if(updateOtApprovalStatus === 1){
                                            updateotStatusResponse = await updateOvertimeClaimApprovalStatus(orgDb,otStatusApprovalInputs);
                                            additionalWageClaimErrorCode = '';
                                            updateotStatusResponse=1;
                                            console.log('Response from the updateOvertimeClaimApprovalStatus() function,',updateotStatusResponse);
                                        }else{
                                            console.log('OT Approval status is not updated as either compensatory off or shift allowance is not inserted or updated for the overtime claim id.',overtimeClaimId);
                                            additionalWageClaimErrorCode = 'OT0013';
                                            updateotStatusResponse=0;
                                        }
                                    }else{
                                        console.log('Employee compensatory off-balance or the shift allowance is not updated in the table as the pre-requisite condition, "calculateCompensatoryOffBalance" response is zero for the overtimeClaimId, ',overtimeClaimId);

                                        let updateotStatusResponse = await updateOvertimeClaimApprovalStatus(orgDb,otStatusApprovalInputs);
                                        additionalWageClaimErrorCode = '';
                                        updateotStatusResponse=1;
                                        console.log('Response from the updateOvertimeClaimApprovalStatus() function in the else block.',updateotStatusResponse);
                                    }
                                }
                            }
                            else{
                                console.log('Error returned from the current work schedule response. ', currentWorkScheduleDetails,' for the overtimeClaimId ',overtimeClaimId,'.So employee compensatory off-balance or the shift allowance is not updated.');
                                additionalWageClaimErrorCode = errorCode ? errorCode: 'OT0011';
                                updateotStatusResponse=0;
                            }
                        }else{
                            console.log('Empty currentWorkScheduleDetails response. ', currentWorkScheduleDetails,' for the overtimeClaimId ',overtimeClaimId,'.So employee compensatory off-balance or the shift allowance is not updated.');
                            additionalWageClaimErrorCode = 'OT0011';
                            updateotStatusResponse=0;
                        }
                    }else{
                        console.log('Response from the validateOvertimeWorkSchedule() function. ',validateOTWorkscheduleResponse,' for the overtime claim id, ',overtimeClaimId);
                        /** If an error occured during overtime work schedule validation or overtime start date-time is not valid throw an error returned from the function. */
                        additionalWageClaimErrorCode = validateOTWorkscheduleResponse.error;
                        updateotStatusResponse=0;
                    }
                }
            }
        }else{
            console.log('Empty response from the validLoginEmployeeIsEligibleApprover() function, ',updateOvertimeStatusRecord,'.Login employee id is not eligible approver to complete the overtime claim record id, ',overtimeClaimId)
            additionalWageClaimErrorCode = '_DB0108';
            updateotStatusResponse=0;
        }
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
        return { errorCode: additionalWageClaimErrorCode, overtimeClaimId: overtimeClaimId, additionalWageClaimUpdated: updateotStatusResponse};
    }catch(updateOvertimeOtherClaimsMainCatchError){
        console.log('Error in the updateOvertimeOtherClaims() function main catch block.', updateOvertimeOtherClaimsMainCatchError);
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
        return { errorCode: 'OT0135', overtimeClaimId: overtimeClaimId, additionalWageClaimUpdated: updateotStatusResponse };
    }
};