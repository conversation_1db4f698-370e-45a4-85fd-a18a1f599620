'use strict';
// require knex for database connection
const knex = require('knex');
// require moment package
const moment = require('moment-timezone');
// require table alias
const { appManagerTables } = require('../../../../common/tablealias');
// require commonLib notification
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// variable declarations
let appManagerDbConnection='';
let response;
let fieldName;
let inputData;

// function to check whether reminder notification exist
module.exports.checkReminderNotificationExist  = async(event, context) =>{
    try{
        console.log('Inside checkReminderNotificationExist function',event);
        // make database connection
        appManagerDbConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
        appManagerDbConnection=knex(appManagerDbConnection.AppManagerDb);
        // form inputs
        inputData = event;
        // based on process define the field name since same process used for goal and rating
        fieldName=(inputData.process==='goal')?'Goal_Settings_Notification_Day':'Rating_Notification_Day';
        // get the notification enabled instance list
        let orgCodeList=await getNotificationEnabledInstances(appManagerDbConnection,fieldName);
        // check whether record or not
        if(orgCodeList.length>0)
        {
            response={
                nextStep:'Step2',
                input:{'process':inputData.process,'orgCodeList':orgCodeList},
                message:'Record exist process the next step.'
            }
        }
        else{
            response={
                nextStep:'End',
                message:'No records found so quit the process.'
            }
        }
        // destroy database connection
        appManagerDbConnection ? appManagerDbConnection.destroy() : null;
        // return response
        return response;
    }
    catch(mainCatchError){
        console.log('Error in checkReminderNotificationExist function main catch block.', mainCatchError);
        appManagerDbConnection ? appManagerDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(mainCatchError, 'EPM0125');
        console.log('Error from step1:',errResult.code+"-"+errResult.message)
        response={
            nextStep:'End',
            message:'Error from step1 so quit the process.'
        }
        return response;
    }
};

// function to get notification enabled instances
async function getNotificationEnabledInstances(appManagerDbConnection){
    try{
        console.log('Inside getNotificationEnabledInstances function');
        // By default we consider indian timezone. Get the current day
        let getCurrentDay= moment().tz('Asia/Kolkata').format('D');
        return(
            // get the orgcode for the notification enabled instance and notification day same as current date
            appManagerDbConnection(appManagerTables.pmsEmailEventsMaster+' as PE')
            .select('PE.Org_Code','HRU.Data_Region')
            .leftJoin(appManagerTables.hrappRegisteruser+' as HRU','PE.Org_Code','HRU.Org_Code')
            .where('PE.Enable_Email_Notification','Yes')
            .where(fieldName,getCurrentDay)
            .then(getOrgCodeList=>{
                return getOrgCodeList;
            })
            .catch(catchError => {
                console.log('Error in getNotificationEnabledInstances function .catch block',catchError);
                throw 'EPM0125';
            })                
        );
    }
    catch(catchError){
        console.log('Error in getNotificationEnabledInstances function main catch block.', catchError);
        throw 'EPM0125';
    }
};
