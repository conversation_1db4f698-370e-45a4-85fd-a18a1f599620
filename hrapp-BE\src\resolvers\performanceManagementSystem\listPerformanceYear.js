// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../../common/appconstants');
// require common function
const { getPerformanceMonthYear } = require('../../../common/performanceManagementCommonFunction');
// require moment package
const moment = require('moment-timezone');

// list the previous and the current performance year in "my goals and achievement" tab and in the employee login - goals and achievement tab
module.exports.listPerformanceYear = async (parent, args, context, info) => {
    console.log("Inside listPerformanceYear() function.");
    let organizationDbConnection;
    let errResult;
    try{
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const orgCode = context.Org_Code;

        // Check Goals and achievement form access rights.
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, context.Employee_Id, formName.goalsAndAchievement, '', 'UI');
        
        // If the login employee is having view access for the 'Goals and achievement' form
        // common for employee,manager and admin view
        if ((Object.keys(checkRights).length >0 && checkRights.Role_View===1)) {
            // get the performance month and year details
            let getPerformanceMonthYearResponse = await getPerformanceMonthYear(orgCode,organizationDbConnection,'getstartendmonthandyear');

            if(getPerformanceMonthYearResponse.length > 0){
                getPerformanceMonthYearResponse = getPerformanceMonthYearResponse[0];

                let response = [];

                // form previous year response
                //Example response: Apr 2019
                let previousYearPerformanceStartMonthYear = moment.monthsShort((getPerformanceMonthYearResponse.startMonth) - 1)+" "+(getPerformanceMonthYearResponse.currentStartYear-1);
                //Example response: Mar 2020
                let previousYearPerformanceEndMonthYear = moment.monthsShort((getPerformanceMonthYearResponse.endMonth) - 1)+" "+(getPerformanceMonthYearResponse.currentEndYear-1);
                //Example response: Apr 2019 - Mar 2020
                let previousPerformanceYear = previousYearPerformanceStartMonthYear+' - '+previousYearPerformanceEndMonthYear;                
                response.push(previousPerformanceYear);

                // form current year response
                //Example response: Apr 2020
                let currentYearPerformanceStartMonthYear = moment.monthsShort((getPerformanceMonthYearResponse.startMonth) - 1)+" "+(getPerformanceMonthYearResponse.currentStartYear);
                //Example response: Mar 2021
                let currentYearPerformanceEndMonthYear = moment.monthsShort((getPerformanceMonthYearResponse.endMonth) - 1)+" "+(getPerformanceMonthYearResponse.currentEndYear);
                //Example response: Apr 2020 - Mar 2021
                let currentPerformanceYear = currentYearPerformanceStartMonthYear+' - '+currentYearPerformanceEndMonthYear;
                response.push(currentPerformanceYear);

                let finalResponse = (response.length > 0) ? JSON.stringify(response) : '';
                
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                return {errorCode:'',message:'Performance year retrieved successfully.',performanceYear:finalResponse};
            }else{
                console.log('Empty response is returned from the getPerformanceMonthYear() function.',getPerformanceMonthYearResponse);
                throw 'EPM0119';// throw error occurred while retrieving the performance year.
            }
        }else{
            throw '_DB0100';// throw employee does not have view access
        }
    }catch(listPerformanceYearMainCatchErr) {
        console.log('Error in the listPerformanceYear() function main catch block. ',listPerformanceYearMainCatchErr);
        // destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(listPerformanceYearMainCatchErr, 'EPM0016');
        // return response
        throw new ApolloError(errResult.message,errResult.code);
    }
};