// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require table alias file
const tables = require('../../../common/tablealias');
// require common constant files
const constants = require('../../../common/appconstants');

// resolver definition
const resolvers = {
    Query: {
        // Function to get announcements based on current date
        listAnnouncements: async (parent, args, context, info) => {
            console.log("Inside list announcements functions");
            // variable declarations
            let organizationDb = '';
            let ehrTables = tables.ehrTables;
            let announcementCount =0;
            try{  
                let loginEmployeeId = context.Employee_Id;
                // get current date 
                let date=new Date();
                let currentDate= date.getFullYear() + '-'+ (date.getMonth()+1) + '-'+ date.getDate()

                // get the organization database connection
                organizationDb = knex(context.connection.OrganizationDb);

                // get the custom group coverage - announcement ids for the login employee
                let announcementIds = await commonLib.func.getCustomGroupParentId(organizationDb,loginEmployeeId,constants.formIds.announcement);

                let getAnnouncementDetailQuery = organizationDb(ehrTables.announcements)
                .select('Announcement_Id as announcementId','Title as title','Announcement_Type as announcementType',
                'Announcement_Text as announcementText','Flash_Content as flashContent','Embed_Url as embedUrl')
                .where('Start_Date','<=',currentDate)
                .where('End_Date','>=',currentDate);

                //If the login employee is associated with the announcement form custom group
                if(announcementIds.length > 0){
                    getAnnouncementDetailQuery = getAnnouncementDetailQuery
                    .where(qb => {
                        qb.where('Coverage', 'ORGANIZATION')
                        qb.orWhere((qb1)=>{
                            qb1.orWhere('Coverage', 'CUSTOMGROUP')
                            qb1.whereIn('Announcement_Id', announcementIds)
                        });
                    });
                }else{
                    getAnnouncementDetailQuery = getAnnouncementDetailQuery.where('Coverage', 'ORGANIZATION');
                }

                // get the announcement details based on current date and the announcement coverage
                return(
                    getAnnouncementDetailQuery.then((getDetails) => {
                        // return the announcement count 
                        announcementCount=getDetails.length;
                        // destroy database connection
                        organizationDb ? organizationDb.destroy() : null;
                        return { errorCode: '', message: 'Announcements retrieved successfully', announcementDetails:(getDetails.length>0)?getDetails:[],announcementCount:announcementCount};
                    })
                    .catch(function (err) {
                        console.log('Error in retrieve announcement details',err);
                        throw 'DB0012';
                    })
                );
            }
            catch(mainCatchError)
            {
                console.log('Error in listAnnouncements function catch block',mainCatchError);
                // destroy database connection
                organizationDb ? organizationDb.destroy() : null;
                // get the code and message from common function based on returned error code
                let errResult = commonLib.func.getError(mainCatchError, 'DB0012');
                // return error response
                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, announcementDetails : [] , announcementCount: announcementCount}));
            }
        },
        // function to list dashboard notification
        listNotificationsInDashboard: async (parent, args, context, info) => {
            let organizationDb = '';
            let outputData = {};
            try {
                const startTime = Date.now();
                console.log('[TIMING] Function start');

                // Database connection
                organizationDb = knex(context.connection.OrganizationDb);
                const employeeId = context.Employee_Id;
                let isAdmin = 0;
                console.log(`[TIMING] DB connection: ${Date.now() - startTime}ms`);

                // Check admin rights
                const rightsStart = Date.now();
                const rights = await commonLib.func.checkEmployeeAccessRights(organizationDb, employeeId, constants.formName.admin, '', 'UI');
                if (Object.keys(rights).length > 0) {
                    if (rights.Role_Update === 1 || rights.Employee_Role.toLowerCase() === 'admin') {
                        isAdmin = 1;
                    }
                }
                console.log(`[TIMING] Admin rights check: ${Date.now() - rightsStart}ms`);

                // Get workflow settings
                const workflowStart = Date.now();
                const [reimbursementWorkflowSettings, leaveWorkflowSettings] = await Promise.all([
                    getReimbursementWorkflowSettings(organizationDb),
                    getLeaveWorkflowSettings(organizationDb)
                ]);
                console.log(`[TIMING] Workflow settings: ${Date.now() - workflowStart}ms`);

                // Build form IDs array
                const taUserFormIds = [
                    constants.formIds.compensatoryOffNew,
                    constants.formIds.projectSettings,
                    constants.formIds.recruitmentRequest || 291,
                    constants.formIds.newPosition || 290,
                    constants.formIds.employeeTravelNew,
                    constants.formIds.resignation,
                    constants.formIds.workFromHome || 244,
                    constants.formIds.workDuringWeekOff || 245,
                    constants.formIds.workDuringHoliday || 246,
                    constants.formIds.shortTimeOffNew,
                    constants.formIds.lopRecovery || 253,
                    constants.formIds.jobPost || 15,
                    constants.formIds.onDutyPreApprovals || 301,
                    constants.formIds.empSalaryRevision || 360,
                    constants.formIds.teamSummary || 243,
                    constants.formIds.myProfile || 18
                ];

                if (leaveWorkflowSettings.toLowerCase() === "yes") {
                    taUserFormIds.push(constants.formIds.leaves);
                }
                if (reimbursementWorkflowSettings.toLowerCase() === "yes") {
                    taUserFormIds.push(constants.formIds.reimbursementNew);
                }

                // Execute taUserTask query
                const taUserQueryStart = Date.now();
                let taUserFormQuery = organizationDb(tables.ehrTables.taUserTask + ' as TUT')
                    .select('TUT.form_id')
                    .countDistinct('TUT.Process_Instance_Id as count')
                    .whereIn('TUT.form_id', taUserFormIds)
                    .whereNotNull('TUT.form_id')
                    .groupBy('TUT.form_id');

                if (args.myApprovals === false) {
                    taUserFormQuery = taUserFormQuery.where('TUT.assignee', '!=', 0);
                } else {
                    taUserFormQuery = taUserFormQuery.where('TUT.assignee', employeeId);
                }

                const taUserFormResults = await taUserFormQuery;
                console.log(`[TIMING] TaUser query: ${Date.now() - taUserQueryStart}ms`);

                // Process taUser results
                const processStart = Date.now();
                taUserFormResults.forEach(result => {
                    const formId = parseInt(result.form_id);
                    const count = parseInt(result.count);

                    if (count > 0) {
                        if (formId === constants.formIds.leaves) {
                            outputData['leaves'] = count;
                        } else if (formId === constants.formIds.compensatoryOffNew) {
                            outputData['compensatoryOff'] = count;
                        } else if (formId === constants.formIds.reimbursementNew) {
                            outputData['reimbursement'] = count;
                        } else if (formId === constants.formIds.projectSettings) {
                            outputData['timesheet'] = count;
                        } else if (formId === (constants.formIds.recruitmentRequest || 268)) {
                            outputData['recruitmentRequest'] = count;
                        } else if (formId === (constants.formIds.newPosition || 290)) {
                            outputData['newPosition'] = count;
                        } else if (formId === constants.formIds.employeeTravelNew) {
                            outputData['travel'] = count;
                        } else if (formId === constants.formIds.resignation) {
                            outputData['resignation'] = count;
                        } else if (formId === (constants.formIds.workFromHome || 244)) {
                            outputData['workFromHome'] = count;
                        } else if (formId === (constants.formIds.workDuringWeekOff || 245)) {
                            outputData['workDuringWeekOff'] = count;
                        } else if (formId === (constants.formIds.workDuringHoliday || 246)) {
                            outputData['workDuringHoliday'] = count;
                        } else if (formId === constants.formIds.shortTimeOffNew) {
                            outputData['shortTimeOff'] = count;
                        } else if (formId === (constants.formIds.lopRecovery || 253)) {
                            outputData['lopRecovery'] = count;
                        } else if (formId === (constants.formIds.jobPost || 15)) {
                            outputData['jobPost'] = count;
                        } else if (formId === (constants.formIds.onDutyPreApprovals || 301)) {
                            outputData['onDutyPreApprovals'] = count;
                        } else if (formId === (constants.formIds.empSalaryRevision || 360)) {
                            outputData['empSalaryRevision'] = count;
                        } else if (formId === (constants.formIds.teamSummary || 243)) {
                            outputData['teamSummary'] = count;
                        } else if (formId === (constants.formIds.myProfile || 18)) {
                            outputData['myProfile'] = count;
                        }
                    }
                });
                console.log(`[TIMING] Process taUser results: ${Date.now() - processStart}ms`);

                // Execute all notification queries
                const notificationStart = Date.now();
                const allPromises = [
                    commonLib.employees.listNotificationCount(employeeId, organizationDb, constants.formIds.attendance),
                    commonLib.employees.listNotificationCount(employeeId, organizationDb, constants.formIds.commission),
                    commonLib.employees.listNotificationCount(employeeId, organizationDb, constants.formIds.advanceSalary),
                    commonLib.employees.listNotificationCount(employeeId, organizationDb, constants.formIds.transfer),
                    commonLib.employees.listNotificationCount(employeeId, organizationDb, constants.formIds.deferredLoan),
                    commonLib.employees.listNotificationCount(employeeId, organizationDb, constants.formIds.deductions),
                    commonLib.employees.listNotificationCount(employeeId, organizationDb, constants.formIds.taxDeclaration),
                    commonLib.employees.listNotificationCount(employeeId, organizationDb, constants.formIds.bonus),
                    commonLib.employees.listNotificationCount(employeeId, organizationDb, constants.formIds.loan),
                    commonLib.employees.listNotificationCount(employeeId, organizationDb, constants.formIds.shiftAllowance),
                    commonLib.employees.listNotificationCount(employeeId, organizationDb, constants.formIds.hraDeclarations),
                    commonLib.employees.listNotificationCount(employeeId, organizationDb, constants.formIds.taxDeclaration, 'uploaded-files', isAdmin, context.Org_Code),
                    commonLib.employees.listNotificationCount(employeeId, organizationDb, constants.formIds.incomeUnderSection24, 'is-approval-notification', isAdmin, context.Org_Code),
                    commonLib.employees.listNotificationCount(employeeId, organizationDb, constants.formIds.incomeUnderSection24, 'upload-files', isAdmin, context.Org_Code),
                    commonLib.employees.checkFinancialClosure(organizationDb, context.Org_Code),
                    commonLib.employees.getAppliedJobcandidates(employeeId, organizationDb)
                ];

                // Add conditional queries
                if (reimbursementWorkflowSettings === "No") {
                    allPromises.push(commonLib.employees.listNotificationCount(employeeId, organizationDb, constants.formIds.reimbursement));
                }
                if (leaveWorkflowSettings && leaveWorkflowSettings.toLowerCase() === "no") {
                    allPromises.push(commonLib.employees.listNotificationCount(employeeId, organizationDb, constants.formIds.leaves));
                }

                const allResults = await Promise.all(allPromises);
                console.log(`[TIMING] All notification queries: ${Date.now() - notificationStart}ms`);

                // Extract results
                const extractStart = Date.now();
                const [
                    attendanceCount,
                    commissionCount,
                    advanceSalaryCount,
                    transferCount,
                    deferredLoanCount,
                    deductionCount,
                    taxDeclarationCount,
                    bonusCount,
                    loanCount,
                    shiftCount,
                    hraDeclarationCount,
                    taxDeclarationUploadCount,
                    housePropertyRecordCount,
                    housePropertyRecordUploadCount,
                    financialClosureData,
                    candidatesCount
                ] = allResults;

                // Handle conditional results
                let reimbursementCount = 0;
                let leavesCount = 0;
                let conditionalIndex = 16;

                if (reimbursementWorkflowSettings === "No") {
                    reimbursementCount = allResults[conditionalIndex++];
                }
                if (leaveWorkflowSettings && leaveWorkflowSettings.toLowerCase() === "no") {
                    leavesCount = allResults[conditionalIndex++];
                }

                // Populate outputData
                if (attendanceCount > 0) outputData['attendance'] = attendanceCount;
                if (commissionCount > 0) outputData['commission'] = commissionCount;
                if (advanceSalaryCount > 0) outputData['advanceSalary'] = advanceSalaryCount;
                if (transferCount > 0) outputData['transfer'] = transferCount;
                if (deferredLoanCount > 0) outputData['deferredLoan'] = deferredLoanCount;
                if (deductionCount > 0) outputData['deduction'] = deductionCount;
                if (taxDeclarationCount > 0) outputData['taxDeclaration'] = taxDeclarationCount;
                if (bonusCount > 0) outputData['bonus'] = bonusCount;
                if (loanCount > 0) outputData['loan'] = loanCount;
                if (shiftCount > 0) outputData['shiftAllowance'] = shiftCount;
                if (hraDeclarationCount > 0) outputData['hraDeclaration'] = hraDeclarationCount;
                if (reimbursementCount > 0) outputData['reimbursement'] = reimbursementCount;
                if (leavesCount > 0) outputData['leaves'] = leavesCount;
                if (taxDeclarationUploadCount > 0) outputData['taxDeclarationUpload'] = taxDeclarationUploadCount;
                if (housePropertyRecordCount > 0) outputData['housePropertyRecord'] = housePropertyRecordCount;
                if (housePropertyRecordUploadCount > 0) outputData['housePropertyUpload'] = housePropertyRecordUploadCount;
                if (candidatesCount > 0) outputData['recruitment'] = candidatesCount;
                if (financialClosureData && isAdmin && financialClosureData > 0) outputData['financialClosure'] = financialClosureData;

                console.log(`[TIMING] Extract and populate: ${Date.now() - extractStart}ms`);

                // Validate leave workflow settings
                if (!leaveWorkflowSettings) {
                    throw("CHR0001");
                }

                organizationDb ? organizationDb.destroy() : null;
                console.log(`[TIMING] Total function time: ${Date.now() - startTime}ms`);

                return {
                    errorCode: '',
                    message: 'Notifications retrieved successfully.',
                    notificationList: (Object.keys(outputData).length > 0) ? JSON.stringify(outputData) : '',
                    notificationCount: Object.keys(outputData).length,
                    leaveWorkflowEnabled: leaveWorkflowSettings,
                    reimbursementWorkflowEnabled: reimbursementWorkflowSettings
                };
            }
            catch (mainCatchError) {
                console.log('Error in listNotificationsInDashboard function catch block', mainCatchError);
                organizationDb ? organizationDb.destroy() : null;
                const errResult = commonLib.func.getError(mainCatchError, 'DB0020');
                throw new Error(JSON.stringify({
                    errorCode: errResult.code,
                    message: errResult.message,
                    notificationList: '',
                    notificationCount: 0,
                    leaveWorkflowEnabled: 'No'
                }));
            }
        }
    }
}

async function getLeaveWorkflowSettings(organizationDbConnection){
    const functionStart = Date.now();
    console.log("Inside getLeaveWorkflowSettings function");
    try{
        let ehrTables = tables.ehrTables;
        const queryStart = Date.now();
        const result = await organizationDbConnection(ehrTables.leaveSettings)
                .select('Enable_Workflow')
                .then((enableWorkflow) => {
                    console.log(`[TIMING] getLeaveWorkflowSettings query: ${Date.now() - queryStart}ms`);
                    //return the response
                    return (enableWorkflow.length>0)?enableWorkflow[0].Enable_Workflow:'No';
                })
                // catch the errors
                .catch(function (err) {
                    console.log('Error in getLeaveWorkflowSettings .catch block',err);
                    return false;
                });

        console.log(`[TIMING] getLeaveWorkflowSettings total: ${Date.now() - functionStart}ms`);
        return result;

    }catch(getLeaveWorkflowSettingsError){
        console.log("Error in getLeaveWorkflowSettings catch block.",getLeaveWorkflowSettingsError);
        return false;
    }
}

async function getReimbursementWorkflowSettings(organizationDbConnection){
    const functionStart = Date.now();
    console.log("Inside getReimbursementWorkflowSettings function");
    try{
        let ehrTables = tables.ehrTables;
        const queryStart = Date.now();
        const result = await organizationDbConnection(ehrTables.reimbursementSettings)
                .select('Enable_Workflow')
                .then((enableWorkflow) => {
                    console.log(`[TIMING] getReimbursementWorkflowSettings query: ${Date.now() - queryStart}ms`);
                    //return the response
                    return (enableWorkflow.length>0)?enableWorkflow[0].Enable_Workflow:'No';
                })
                // catch the errors
                .catch(function (err) {
                    console.log('Error in getReimbursementWorkflowSettings .catch block',err);
                    return 'No';
                });

        console.log(`[TIMING] getReimbursementWorkflowSettings total: ${Date.now() - functionStart}ms`);
        return result;

    }catch(getReimbursementWorkflowSettingsError){
        console.log("Error in getReimbursementWorkflowSettings catch block.",getReimbursementWorkflowSettingsError);
        return 'No';
    }
}

exports.resolvers = resolvers;
