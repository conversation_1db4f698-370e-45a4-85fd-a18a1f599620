// require table alias
const { ehrTables } = require('../../../common/tablealias');

// get UTC time from unix timestamp
module.exports.getUTCTime = async(timeInSeconds)=>{
    return new Promise((resolve,reject)=>{
        try {
            // Create a new Date object based on the timestamp
            // multiplied by 1000 so that the argument is in milliseconds, not seconds.
            let date = timeInSeconds * 1000;
            date = new Date(date);
            let utcTime = date.toISOString().replace(/T/, ' ').replace(/\..+/, '');
            // return response to resolver
            resolve(utcTime);
        } catch (getUTCTimeMainCatchError) {
            console.log('Error in getUTCTime() function catch block', getUTCTimeMainCatchError);
            reject(getUTCTimeMainCatchError);
        }
    })    
};
// get my team activity total worked hours and percentage for a single day and week
module.exports.getMyTeamActivities = async (minDate, maxDate, weekMinDate, weekMaxDate, employeeIdArray, organizationDbConnection)=>{
    // My team members activity
    let myTeamStatistics = {
        todayDuration:  0,
        todayPercentage:0,
        weekDuration:  0,
        weekPercentage: 0,
        productiveActivityDuration: 0
    };
    try{
        // get total employee worked hours (Today)
        let todayDuration = await organizationDbConnection(ehrTables.empActivityDetails).sum('Total_Activity_Duration as duration')
            .andWhere('Activity_Start_Date_Time', '>=', minDate)
            .andWhere('Activity_End_Date_Time', '<=', maxDate)
            .whereIn('Employee_Id', employeeIdArray)
            .then((res) => {
                return res[0] ? res[0]['duration'] : null;
            })

        // get total employee activity percentage(Today)
        let todayPercentage = await organizationDbConnection(ehrTables.empActivityDetails).avg('Activity_Percentage as percentage')
            .andWhere('Activity_Start_Date_Time', '>=', minDate)
            .andWhere('Activity_End_Date_Time', '<=', maxDate)
            .whereIn('Employee_Id', employeeIdArray)
            .then((res) => {
                return res[0] ? res[0]['percentage'] : null;
            })

        // get total employee worked hours (Whole week)
        let weekDuration = await organizationDbConnection(ehrTables.empActivityDetails).sum('Total_Activity_Duration as duration')
            .andWhere('Activity_Start_Date_Time', '>=', weekMinDate)
            .andWhere('Activity_End_Date_Time', '<=', weekMaxDate)
            .whereIn('Employee_Id', employeeIdArray)
            .then((res) => {
                return res[0] ? res[0]['duration'] : null;
            })

        // get total employee activity percentage (Whole week)
        let weekPercentage = await organizationDbConnection(ehrTables.empActivityDetails).avg('Activity_Percentage as percentage')
            .andWhere('Activity_Start_Date_Time', '>=', weekMinDate)
            .andWhere('Activity_End_Date_Time', '<=', weekMaxDate)
            .whereIn('Employee_Id', employeeIdArray)
            .then((res) => {
                return res[0] ? res[0]['percentage'] : null;
            })
        // get employee productive activity percentage for the chosen date
        let productiveActivityDuration = await getProductiveActivityDuration(minDate, maxDate, employeeIdArray, organizationDbConnection);

        // My team members activity
        myTeamStatistics = {
            todayDuration: todayDuration ? await getTimeInHoursMinutesSeconds(parseInt(todayDuration)) : '00:00:00',
            todayPercentage: todayPercentage ? Math.floor(todayPercentage) : 0,
            weekDuration: weekDuration ? await getTimeInHoursMinutesSeconds(parseInt(weekDuration)) : '00:00:00',
            weekPercentage: weekPercentage ? Math.floor(weekPercentage): 0,
            productiveActivityDuration: productiveActivityDuration ? await getTimeInHoursMinutesSeconds(parseInt(productiveActivityDuration)) : '00:00:00'
        };
        // return response to reslover
        return myTeamStatistics;        
    } catch (getMyTeamActivitiesError){
        console.log('Error in getMyTeamActivities() function catch block', getMyTeamActivitiesError);
        // return response to reslover
        return myTeamStatistics;
    }
}
// get my activity total worked hours and percentage for a single day and week
module.exports.getMyActivities = async (employeeId,minDate, maxDate, weekMinDate, weekMaxDate, organizationDbConnection) =>{
    // variable declaration
    let todayDuration = todayPercentage = weekDuration = weekPercentage = 0;
    try {
        // get total worked hours of the employee (Today)
        todayDuration = await organizationDbConnection(ehrTables.empActivityDetails).sum('Total_Activity_Duration as duration')
            .where('Employee_Id', employeeId)
            .andWhere('Activity_Start_Date_Time', '>=', minDate)
            .andWhere('Activity_End_Date_Time', '<=', maxDate)
            .then((res) => {
                return res[0] ? res[0]['duration'] : null;
            })
        // get activity percentage of the employee (Today)
        todayPercentage = await organizationDbConnection(ehrTables.empActivityDetails).avg('Activity_Percentage as percentage')
            .where('Employee_Id', employeeId)
            .andWhere('Activity_Start_Date_Time', '>=', minDate)
            .andWhere('Activity_End_Date_Time', '<=', maxDate)
            .then((res) => {
                return res[0] ? res[0]['percentage'] : null;
            })
        // get total worked hours of the employee (Whole week)
        weekDuration = await organizationDbConnection(ehrTables.empActivityDetails).sum('Total_Activity_Duration as duration')
            .where('Employee_Id', employeeId)
            .andWhere('Activity_Start_Date_Time', '>=', weekMinDate)
            .andWhere('Activity_End_Date_Time', '<=', weekMaxDate)
            .then((res) => {
                return res[0] ? res[0]['duration'] : null;
            })
        // get activity percentage of the employee (Whole week)
        weekPercentage = await organizationDbConnection(ehrTables.empActivityDetails).avg('Activity_Percentage as percentage')
            .where('Employee_Id', employeeId)
            .andWhere('Activity_Start_Date_Time', '>=', weekMinDate)
            .andWhere('Activity_End_Date_Time', '<=', weekMaxDate)
            .then((res) => {
                return res[0] ? res[0]['percentage'] : null;
            })

        // return response to resolver
        return {todayDuration, todayPercentage, weekDuration, weekPercentage};

    } catch (getMyActivitiesError){
        console.log('Error in getMyActivities() function main catch block.', getMyActivitiesError);
        // return response to resolver
        return { todayDuration, todayPercentage, weekDuration, weekPercentage };
    }
}
// function to email notification through AWS SES
module.exports.sendEmailNotifications = async (notificationParams)=>{
    try {
        //  require aws-sdk 
        const AWS = require("aws-sdk");
        // create object for aws SES
        const ses = new AWS.SES({ region: process.env.sesTemplatesRegion });
        //  call function sendTemplatedEmailto send email
        let response = await ses.sendTemplatedEmail(notificationParams).promise();
        console.log('sendEmailNotifications response:', response); //Console Required
        // return response
        return true;
    } catch (sendEmailNotificationsError) {
        console.log('error while in sendEmailNotifications() function catch block', sendEmailNotificationsError);
        // return response
        return false;
    }
};
// get employee email
module.exports.getEmployeeEmail = async(employeeId,organizationDbConnection)=>{
    return new Promise((resolve,reject)=>{
        return(
            organizationDbConnection(ehrTables.empJob)
            .select("Employee_Id", "Emp_Email")
            .from(ehrTables.empJob)
            .whereIn("Employee_Id", employeeId)
            .then((response) => {
                resolve(response);
            })
            .catch(getEmployeeEmailError=>{
                console.log('Error in getEmployeeEmail() function .catch block', getEmployeeEmailError);
                reject(getEmployeeEmailError);
            })
        )
    })
}

// get hours:minutes:seconds from seconds
async function getTimeInHoursMinutesSeconds(seconds){
    let totalSeconds = seconds;
    return new Promise((resolve,reject)=>{
        try {
            // get hours from totalSeconds
            let hours = parseInt(totalSeconds / (60 * 60));
            // get remaining seconds from totalSeconds
            let reminingSeconds = (totalSeconds % (60 * 60));
            // get minutes from reminingSeconds
            let minutes = parseInt(reminingSeconds / 60);
            // get seconds from reminingSeconds
            let seconds = (reminingSeconds % 60);
            // from time example: 09:40:59
            let time = ((parseInt(hours) > 9 ) ? parseInt(hours) : '0' + parseInt(hours))  + ':' + ((parseInt(minutes) > 9) ? (parseInt(minutes)) : ('0' + parseInt(minutes))) + ':' + ((seconds > 9) ? seconds : '0' + seconds);
            // return response to resolver function
            resolve(time);
           } catch (getTimeInHoursMinutesSecondsError) {
            console.log('Error in getTimeInHoursMinutesSeconds() function main catch block', getTimeInHoursMinutesSecondsError);
            // return response to resolver function
            reject(getTimeInHoursMinutesSecondsError);
        }
    })
}

// get productive activity duration
async function getProductiveActivityDuration(minDate, maxDate, employeeIdArray, organizationDbConnection){
    // get employee productive activity percentage for the chosen date
    let productiveActivityDuration = await organizationDbConnection(ehrTables.empActivityDetails).sum('Productive_Activity_Duration as productiveActivityDuration')
        .whereIn('Employee_Id', employeeIdArray)
        .andWhere('Activity_Start_Date_Time', '>=', minDate)
        .andWhere('Activity_End_Date_Time', '<=', maxDate)
        .then((res) => {
            return res[0] ? (res[0]['productiveActivityDuration'] ? res[0]['productiveActivityDuration'] : 0) : 0;
        }).catch(getProductiveActivityDurationError=>{
            console.log('Error in getProductiveActivityDuration() .catch block.', getProductiveActivityDurationError);
            return 0;
        })
    // return response back
    return productiveActivityDuration;

}
// form UTC minimum time and maximum time for given date
function formMinMaxTimeByZone(date, offSet) {
    // global variable declration
    let response = {
        minTime:"",
        maxTime:""
    };
    try{

        let minTime = new Date(date);

        minTime.setMinutes(minTime.getMinutes() - offSet);

        let maxTime = new Date(minTime.getTime());

        maxTime.setDate(maxTime.getDate() + 1);

        minTime = minTime.toISOString().substring(0, 19).replace('T', ' '); // 2020-06-03T13:00:00.000Z, 2020-06-04T13:00:00.000Z

        maxTime = maxTime.toISOString().substring(0, 19).replace('T', ' ');

        return { // 2020-06-03 13:00:00, 2020-06-04 13:00:00
            minTime,
            maxTime
        }
    }catch(formMinMaxTimeByZoneError){
        console.log('Error in formMinMaxTimeByZone() function catch block', formMinMaxTimeByZoneError);
        return response;
    }
}

//to get the week start date and end date with time for the given date
function getWeekMinMaxTime(givenDate) {
    // global variable declaration
    let response = {
        weekMinTime:"",
        weekMaxTime:""
    };
    try{
        // require moment-timezone
        const moment = require('moment-timezone');

        // get the week min and max date of the chosen date.
        // that is if i choose July-02-2020 then week min date will be June-28-2020 and week max date will be Jult-04-2020
        let weekMinTime = moment(givenDate).startOf('week').format('YYYY-MM-DD');
        let weekMaxTime = moment(givenDate).endOf('week').format('YYYY-MM-DD');

        // return response
        return {
            weekMinTime,
            weekMaxTime
        }
    }catch(getWeekMinMaxTimeError){
        console.log('Error in getWeekMinMaxTimeError() catch block.', getWeekMinMaxTimeError);
        // return response
        return response
    }
}
// get UTC time from unix timestamp
module.exports.formSlotStartEndDate = async (timeInSeconds) => {
    return new Promise((resolve, reject) => {
        try {
            // Create a new Date object based on the timestamp
            // multiplied by 1000 so that the argument is in milliseconds, not seconds.
            let date = timeInSeconds * 1000;
            date = new Date(date);
            let utcTime = date.toISOString().replace(/T/, ' ').replace(/\..+/, '');
            let [curDate, curTime] = utcTime.toString().split(' ');
            let [h, m, s] = curTime.split(':');
            let minTime, maxTime = '';
            if (m < 10) {
                minTime = curDate + ' ' + h + ':' + '00:00';
                maxTime = curDate + ' ' + h + ':' + '10:59';
            } else if (m < 20) {
                minTime = curDate + ' ' + h + ':' + '10:00';
                maxTime = curDate + ' ' + h + ':' + '20:59';
            } else if (m < 30) {
                minTime = curDate + ' ' + h + ':' + '20:00';
                maxTime = curDate + ' ' + h + ':' + '30:59';
            } else if (m < 40) {
                minTime = curDate + ' ' + h + ':' + '30:00';
                maxTime = curDate + ' ' + h + ':' + '40:59';
            } else if (m < 50) {
                minTime = curDate + ' ' + h + ':' + '40:00';
                maxTime = curDate + ' ' + h + ':' + '50:59';
            } else {
                minTime = curDate + ' ' + h + ':' + '50:00';
                maxTime = curDate + ' ' + (parseInt(h) + 1) + ':' + '00:59';
            }
            // return response to resolver
            resolve({ minTime, maxTime });
        } catch (formSlotStartEndDateMainCatchError) {
            console.log('Error in formSlotStartEndDate() function catch block', formSlotStartEndDateMainCatchError);
            reject(formSlotStartEndDateMainCatchError);
        }
    })
};
exports.getTimeInHoursMinutesSeconds = getTimeInHoursMinutesSeconds;
exports.getProductiveActivityDuration = getProductiveActivityDuration;
exports.formMinMaxTimeByZone = formMinMaxTimeByZone;
exports.getWeekMinMaxTime = getWeekMinMaxTime;
    