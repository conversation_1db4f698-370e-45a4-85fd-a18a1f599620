// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex package
const knex = require('knex');
// require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require common constant files
const { formName,systemLogs } = require('../../../common/appconstants');
// require table alias
const { ehrTables,appManagerTables } = require('../../../common/tablealias');
// require validation file
const performanceManagementValidation = require('../../../common/performanceManagementValidation');

// variable declarations
let errResult = {};
let organizationDbConnection = '';
let appManagerDbConnection='';
let validationError;

// resolver definition
const resolvers = {
    Mutation:{
        // function to update performance settings/ratings
        updatePerformanceSettings: async(parent, args, context, info) =>{
            try{
                console.log('Inside updatePerformanceSettings function');
                // get database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                let logInEmpId = context.Employee_Id;
                // check whether the employee has admin access and edit access to performance management form or not
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId, formName.performanceManagement, '', 'UI');
                if (Object.keys(checkRights).length >0 && checkRights.Employee_Role.toLowerCase() === 'admin' && checkRights.Role_Update===1) {
                    // get the employee timezone based on location
                    let employeeTimeZone = await commonLib.func.getEmployeeTimeZone(logInEmpId, organizationDbConnection, 1);
                    // check whether performance settings inputs available or not
                    if(args.performanceSettings && (Object.keys(args.performanceSettings).length > 0)){
                        // validate the input fields
                        validationError=await performanceManagementValidation.inputValidation(args.performanceSettings);
                        if(Object.keys(validationError).length ===0){
                            appManagerDbConnection = knex(context.connection.AppManagerDb);
                            // update the notification settings in app manager table
                            let updateNotificationSettings=await updateAppManagerSettings(appManagerDbConnection,context.Org_Code,args.performanceSettings,logInEmpId,employeeTimeZone);
                            // if settings updated successfully then update the same in organization database
                            if(updateNotificationSettings==='success'){
                                // function to update performance settings in organization database
                                let updateSettings=await updatePerformanceSettings(organizationDbConnection,args.performanceSettings,employeeTimeZone,logInEmpId);
                                // On successful update of performance settings check whether input contains ratings array is exist then update the same.
                                if(updateSettings==='success'){
                                    // if exist then update the rating details
                                    if(args.ratings && (args.ratings.length>0)){
                                        // function to update ratings and its description
                                        let updateDetails=await updateRatings(organizationDbConnection,args.ratings,logInEmpId,employeeTimeZone);
                                        if(updateDetails!=='success'){
                                            console.log('Performance settings updated but error while updating rating details.');
                                            throw 'EPM0004';
                                        }
                                    }
                                }
                                else{
                                    console.log('Performance settings updated in app manager but error while updating it in organization table')
                                    throw 'EPM0102';
                                }
                            }
                            else{
                                throw 'EPM0102'; // Error while updating settings in app manager table
                            }
                        }
                        else{
                            throw 'IVE0000';
                        }    
                    }
                    else{
                        if(args.ratings && (args.ratings.length>0)){
                            // function to update ratings and its description
                            let updateDetails=await updateRatings(organizationDbConnection,args.ratings,logInEmpId,employeeTimeZone);
                            if(updateDetails!=='success'){
                                throw 'EPM0003';
                            }
                        }
                    }
                    // function to update system logs
                    let systemLogParams = {
                        action: systemLogs.roleUpdate,
                        userIp: context.User_Ip,
                        employeeId: logInEmpId,
                        formName: formName.performanceManagement,
                        trackingColumn: 'settings',
                        organizationDbConnection: organizationDbConnection,
                        uniqueId: ''
                    };
                    // call function to update system log activities
                    await commonLib.func.createSystemLogActivities(systemLogParams);
                    // destroy the database connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    appManagerDbConnection ? appManagerDbConnection.destroy() : null;    
                    return { errorCode:'',message:'Performance management settings updated successfully.'};
                }
                else{
                    // throw if employee does not edit have access
                    throw ('_DB0102');
                }
            }
            catch(mainCatchError){
                console.log('Error in updatePerformanceSettings function main catch block',mainCatchError);
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                if (mainCatchError === 'IVE0000') {
                    errResult = commonLib.func.getError('', 'IVE0000');
                    // return response
                    throw new UserInputError(errResult.message, { validationError: validationError });
                } else {
                    errResult = commonLib.func.getError(mainCatchError, 'EPM0002');
                    // return response
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        }
    }
};

exports.resolvers = resolvers;

// function to insert/update the settings in app manager table
async function updateAppManagerSettings(appManagerDbConnection,orgCode,settingDetails,logInEmpId,employeeTimeZone){
    try{
        return(
            // check whether event details exist for the orgcode or not
            appManagerDbConnection(appManagerTables.pmsEmailEventsMaster)
            .pluck('Id')
            .where('Org_Code',orgCode)
            .then(getDetails =>{
                let inputParams=[{
                    Org_Code:orgCode,
                    Goal_Settings_Notification_Day:settingDetails.GoalSettings_ReminderOne,
                    Rating_Notification_Day:settingDetails.RatingUpdate_ReminderOne,
                    Enable_Email_Notification:settingDetails.Enable_Email_Notification,
                    Updated_On:employeeTimeZone,
                    Updated_By:logInEmpId
                },
                {
                    Org_Code:orgCode,
                    Goal_Settings_Notification_Day:settingDetails.GoalSettings_ReminderTwo,
                    Rating_Notification_Day:settingDetails.RatingUpdate_ReminderTwo,
                    Enable_Email_Notification:settingDetails.Enable_Email_Notification,
                    Updated_On:employeeTimeZone,
                    Updated_By:logInEmpId
                }];

                // if record exist then update the settings
                if(getDetails.length>0){
                    return (
                    appManagerDbConnection
                    .transaction(function (trx) {
                        return(
                            appManagerDbConnection(appManagerTables.pmsEmailEventsMaster)
                            .delete()
                            .whereIn('Id',getDetails)
                            .transacting(trx)
                            .then(() =>{
                                return(
                                    appManagerDbConnection(appManagerTables.pmsEmailEventsMaster)
                                    .insert(inputParams)
                                    .transacting(trx)
                                    .then(insertSettingsResponse =>{
                                        console.log('Settings deleted and inserted in appmanager database successfully.',insertSettingsResponse);
                                        return 'success';
                                    })
                                );
                            })
                        )
                    })
                    .then((result) => {
                        return result;
                    })
                    .catch(function (deleteInsertCatchError) {
                        console.log('Error while updating settings in appmanager database.',deleteInsertCatchError);
                        return 'error';
                    })
                    );
                }
                else{
                    return(
                        appManagerDbConnection(appManagerTables.pmsEmailEventsMaster)
                        .insert(inputParams)
                        .then(insertSettings =>{
                            console.log('Settings inserted in appmanager database successfully.',insertSettings);
                            return 'success';
                        })
                        .catch(function (catchError) {
                            console.log('Error while inserting settings in appmanager database.',catchError);
                            return 'error';
                        })
                    );
                }
            })
            .catch(function (updateError) {
                console.log('Error while updateAppManagerSettings .catch block',updateError);
                return 'error';
            })
        );
    }
    catch(error){
        console.log('Error in updateAppManagerSettings function  main catch block',error);
        return 'error';
    }
};

// function to update performance settings in organization database
async function updatePerformanceSettings(organizationDbConnection,settingDetails,employeeTimeZone,logInEmpId){
    try{
        // form update params
        let updateParams={
            Performance_Management_Mode:settingDetails.Performance_Management_Mode,
            Maximum_Rating:settingDetails.Maximum_Rating,
            GoalSettings_ReminderOne:settingDetails.GoalSettings_ReminderOne,
            GoalSettings_ReminderTwo:settingDetails.GoalSettings_ReminderTwo,
            RatingUpdate_ReminderOne:settingDetails.RatingUpdate_ReminderOne,
            RatingUpdate_ReminderTwo:settingDetails.RatingUpdate_ReminderTwo,
            Goal_Email_Subject:settingDetails.Goal_Email_Subject,
            Goal_Email_Content:settingDetails.Goal_Email_Content,
            Rating_Email_Subject:settingDetails.Rating_Email_Subject,
            Rating_Email_Content:settingDetails.Rating_Email_Content,
            Enable_Email_Notification:settingDetails.Enable_Email_Notification,
            Present_Only_Team_Members_To_Managers: settingDetails.Present_Only_Team_Members_To_Managers,
            Updated_On:employeeTimeZone,
            Updated_By:logInEmpId
        };
        // update performance settings
        return(
            organizationDbConnection(ehrTables.performanceManagementSettings)
            .update(updateParams)
            .then(updateSettings =>{
                console.log('Performance settings details updated successfully.',updateSettings);
                return 'success';
            })
            .catch(function (catchError) {
                console.log('Error in updatePerformanceSettings function .catch block',catchError);
                return 'error';
            })
        );
    }
    catch(error){
        console.log('Error in updatePerformanceSettings function main catch block',error);
        return 'error';
    }
};

// function to update the rating description in ratings table
async function updateRatings(organizationDbConnection,ratings,logInEmpId,employeeTimeZone){
    try{
        return(
            organizationDbConnection
            .transaction(function(trx){
                return(
                    // truncate the values in ratings table and insert the input data
                    organizationDbConnection(ehrTables.performanceRatingDescription)
                    .truncate()
                    .transacting(trx)
                    .then(data =>{
                       let performanceRatingsData = ratings.map(ratings => ({
                            Rating_Id: ratings.Rating_Id,
                            Rating_Description: ratings.Rating_Description,
                            Updated_On: employeeTimeZone,
                            Updated_By: logInEmpId
                        }));
                        // insert the rating details
                        return(        
                            organizationDbConnection(ehrTables.performanceRatingDescription)
                            .insert(performanceRatingsData)
                            .transacting(trx)
                            .then(insertedRatings=>{
                                console.log('Ratings inserted successfully',insertedRatings);
                                return 'success';
                            })
                        )
                    })
                )
            })
            .catch(function (catchError) {
                console.log('Error in updateRatings function .catch block',catchError);
                return 'error';
            })
        );
    }
    catch(error){
        console.log('Error in updateRatings function main catch block',error);
        return 'error';
    }
};
