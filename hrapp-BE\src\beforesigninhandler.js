var { ApolloServer, gql } = require('apollo-server-lambda');
var { resolvers } = require('./beforesigninresolver');
const fs = require('fs');
const typeDefs = gql(fs.readFileSync(__dirname.concat('/beforesigninschema.graphql'), 'utf8'));
// require common hrapp-corelib functions
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// Create object for ApolloServer
const server = new ApolloServer({
    typeDefs,
    resolvers,
    context: async ({ event }) => {
        
        let contextData = await commonLib.func.getContextDataWithoutEmployeeId(event,1,'ro');
        //return header to resolver function
        return {...contextData};
    }
});
const handler = server.createHandler({
    cors: {
        method: 'POST',
        allowHeaders: '*'
    }
});
module.exports.beforesigninhandler = (event, context, callback) => {
    context.callbackWaitsForEmptyEventLoop = false; //to send the response immediately when callback executes
    function callbackFilter(error, output) {
        output.headers['Access-Control-Allow-Origin'] = '*';
        output.headers['Access-Control-Allow-Credentials'] = true;
        callback(error, output);
    }
    return handler(event, context, callbackFilter);
};