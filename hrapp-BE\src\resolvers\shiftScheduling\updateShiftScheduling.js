// require table names
const { ehrTables } = require('../../../common/tablealias');
// require moment-timezone
const moment = require('moment-timezone');
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require common function
const { getShiftDateExistEmpId,validateWeekOffApplicableOnHoliday } = require('./weekOffCommonFunction');

// Return Employee Ids if the employee has date of joining after shift start date OR resgination date before shift start date and end date
async function checkWithJoinAndResignationDate(orgDb, employeeId, shiftStartDate, shiftEndDate) {
    let joinDateErr = [];
    let resignationDateErr = [];

    try {

        return orgDb.select("Employee_Id")
            .from(ehrTables.empJob)
            .where('Date_Of_Join', '>', shiftStartDate)
            .whereIn('Employee_Id', employeeId)
            .then((joinErr) => {
                for (let arr of joinErr) {
                    joinDateErr.push(arr.Employee_Id)
                }

                return orgDb.select("Employee_Id")
                    .from(ehrTables.resignation)
                    .where((qb) => {
                        qb.where('Resignation_Date', '<', shiftStartDate).orWhere('Resignation_Date', '<', shiftEndDate)
                    })
                    .whereIn('Employee_Id', employeeId)
                    .whereIn('Approval_Status', ['Approved', 'Applied', 'Incomplete'])
                    .then((resignErr) => {
                        for (let arr of resignErr) {
                            resignationDateErr.push(arr.Employee_Id)
                        }

                        return [...joinDateErr, ...resignationDateErr];
                    })
            })
    }
    catch (err) {
        console.log("Error in checkWithJoinAndResignationDate ", err)
        return [];
    }
}

// Get employees name by their Employee Id
async function getEmpNameById(orgDb, empIds) {
    let empNames = [];
    try {
        return orgDb.select(orgDb.raw("CONCAT_WS(' ',Emp_First_Name,Emp_Middle_Name, Emp_Last_Name) as Employee_Name"))
            .from(ehrTables.empPersonal)
            .whereIn('Employee_Id', empIds)
            .then((names) => {
                for (let arr of names) {
                    empNames.push(arr.Employee_Name)
                }
                return empNames;
            })
    }
    catch (err) {
        console.log("Error in getEmpNameById ", err)
        return empNames;
    }
}
/**
 * Function to get the employee new shift type - work schedule details
 * @param {JSON} orgDb - Organization database connection object
 * @param {Number} employeeId - Employee Id
 * @param {Date} shiftStartDate - Shift date
 * @param {Number} newshiftTypeId - New Shift type id
 * @param {Number} overlapShiftEnabled - 0 | 1 - Overlapping shift schedule flag 
 * @returns {JSON} - If the workschedule details retreived return the workschedule JSON and empty errorcode. 
 * Otherwise return the empty workschedule JSON and the error code.
 */
async function getNewShiftTypeWorkSchedule(orgDb, employeeId, shiftStartDate, newshiftTypeId, overlapShiftEnabled) {
    let newShiftTypeWorkScheduleJson = {};
    let newShiftTypeWorkScheduleErrorCode = '';
    try {
        /** sent the employee id as null in order to get the organization level work schedule details for the new shift type id. */
        let newShiftTypeWorkSchedule = await commonLib.employees.getGraceTimeDetails(null, newshiftTypeId, shiftStartDate, orgDb);

        //if the workschedule details exist
        if (Object.keys(newShiftTypeWorkSchedule).length > 0 && newShiftTypeWorkSchedule.regularFrom && newShiftTypeWorkSchedule.regularTo
            && newShiftTypeWorkSchedule.considerationFrom && newShiftTypeWorkSchedule.considerationTo) {
            // Get workschedule level for the employee
            let employeeWorkScheduleLevel = await commonLib.employees.getEmployeeTypeWorkSchedule(orgDb, employeeId);
            /** If overlapping shift schedule is enabled and if the employee work schedule type is Shift Roster then 
             * find the consideration end date-time for the new shift type id for the shift date.*/
            if (parseInt(overlapShiftEnabled) === 1 && employeeWorkScheduleLevel === 'Shift Roster' && employeeId) {
                // Find next day date by using start date
                let nextDate = moment(shiftStartDate, "YYYY-MM-DD").add(1, 'days').format('YYYY-MM-DD');

                // get the next date workschedule details for the employee id
                let newShiftTypeNextWorkSchedule = await commonLib.employees.getShiftDetails(nextDate, employeeId, null, orgDb);
                // get the new shift type id - actual workschedule details along with the new consideration end date-time for the employee id for the shift date
                let newShiftTypeOverlappingWorkSchedule = await commonLib.employees.getshiftDateOverlappingBasedWorkschedule(newShiftTypeNextWorkSchedule, nextDate, newShiftTypeWorkSchedule);
                //if the overlapping based workschedule details exist
                if (Object.keys(newShiftTypeOverlappingWorkSchedule).length > 0 && newShiftTypeOverlappingWorkSchedule.regularFrom
                    && newShiftTypeOverlappingWorkSchedule.regularTo && newShiftTypeOverlappingWorkSchedule.considerationFrom
                    && newShiftTypeOverlappingWorkSchedule.considerationTo) {
                    newShiftTypeWorkScheduleJson = newShiftTypeOverlappingWorkSchedule;
                    newShiftTypeWorkScheduleErrorCode = '';
                } else {
                    newShiftTypeWorkScheduleJson = {};
                    newShiftTypeWorkScheduleErrorCode = (newShiftTypeOverlappingWorkSchedule.errorCode) ? newShiftTypeOverlappingWorkSchedule.errorCode : 'SS0103';
                }
            } else {
                newShiftTypeWorkScheduleJson = newShiftTypeWorkSchedule;
                newShiftTypeWorkScheduleErrorCode = '';
            }
            return { errorCode: newShiftTypeWorkScheduleErrorCode, newShiftTypeWorkSchedule: newShiftTypeWorkScheduleJson };
        } else {
            return { errorCode: 'SS0129', newShiftTypeWorkSchedule: newShiftTypeWorkScheduleJson }
        }
    } catch (newShiftTypeWorkScheduleCatchError) {
        console.log('Error in the getNewShiftTypeWorkSchedule() main catch block.', newShiftTypeWorkScheduleCatchError);
        return { errorCode: 'SS0130', newShiftTypeWorkSchedule: newShiftTypeWorkScheduleJson };
    }
}


/**
 * Function to return the success/error response while adding the shift for the employee(s)
 * @param {JSON} responseMessageArgs - JSON with multiple categories - invalid employee ids and invalid employee names
 * @returns {String}
 */
async function formErrorResponseMessage(responseMessageArgs) {
    try {
        let { responseMessage, invalidDOJEmpIds, invalidDOJEmpNames, shiftExistsEmpIds, shiftExistsEmpNames, shiftOverlapEmpIds, shiftOverlapEmpNames } = responseMessageArgs;
        let { isAttendanceLeaveCompOffExistEmployeeIds, attendanceCompOffLeaveExistEmpNames, newShiftDetailsNotRetrievedEmployeeIds, newShiftDetailsNotRetrievedEmpNames, attendanceNotRetrievedEmployeeIds, attendanceNotRetrievedEmpNames, weekOffNotApplicableEmployeeIds, weekOffNotApplicableEmpNames } = responseMessageArgs;
        responseMessage = responseMessage;
        let previousInvalidEmpExist = 0;
        if (invalidDOJEmpIds[0]) {
            previousInvalidEmpExist = 1;
            responseMessage += invalidDOJEmpNames + " as shift date exist before their DOJ or after their resignation";
        }

        if (shiftExistsEmpIds[0]) {
            if (previousInvalidEmpExist) {
                responseMessage += ' and for ';
            }
            previousInvalidEmpExist = 1;
            responseMessage += shiftExistsEmpNames + " as the shift already scheduled for the chosen date";
        } 
        if (shiftOverlapEmpIds[0]) {
            if (previousInvalidEmpExist) {
                responseMessage += ' and for ';
            }
            previousInvalidEmpExist = 1;
            responseMessage += shiftOverlapEmpNames + " as the shift overlapped for the chosen date";
        }

        if (isAttendanceLeaveCompOffExistEmployeeIds[0]) {
            if (previousInvalidEmpExist) {
                responseMessage += ' and for ';
            }
            previousInvalidEmpExist = 1;
            responseMessage += attendanceCompOffLeaveExistEmpNames + " as the attendance or compensatory off or leave exist for the chosen date";
        }

        if (newShiftDetailsNotRetrievedEmployeeIds[0]) {
            if (previousInvalidEmpExist) {
                responseMessage += ' and for ';
            }
            previousInvalidEmpExist = 1;
            responseMessage += newShiftDetailsNotRetrievedEmpNames + " as an error occurred while validating shift details for the chosen date";
        }

        if (attendanceNotRetrievedEmployeeIds[0]) {
            if (previousInvalidEmpExist) {
                responseMessage += ' and for ';
            }
            previousInvalidEmpExist = 1;
            responseMessage += attendanceNotRetrievedEmpNames + " as an error occurred while validating attendance for the chosen date.";
        }
        if (weekOffNotApplicableEmployeeIds[0]) {
            if (previousInvalidEmpExist) {
                responseMessage += ' and for ';
            }
            previousInvalidEmpExist = 1;
            responseMessage += weekOffNotApplicableEmpNames + " as week off is not applicable on holidays.";
        }
        return responseMessage;
    } catch (formErrorResponseMessageCatchError) {
        console.log('Error in the formErrorResponseMessage() function main catch block.', formErrorResponseMessageCatchError);
        return formErrorResponseMessageCatchError;
    }
}


async function formShiftRotationScheduleWithDates(orgDb, shiftRotationId, shiftStartDateInput) {
    try {
        let shiftRotationDetails = await orgDb(ehrTables.shiftRotation + " as SR")
            .select('SR.*', 'SRS.*')
            .innerJoin(ehrTables.shiftRotationSchedule + " as SRS", 'SRS.Rotation_Id', 'SR.Rotation_Id')
            .where('SR.Rotation_Id', shiftRotationId)
            .orderBy('SRS.Rotation_Level', 'asc');

        if (shiftRotationDetails && shiftRotationDetails.length) {

            let shiftStartDate = moment(shiftStartDateInput);

            let result = formShiftRotation(shiftRotationDetails, shiftStartDate);
            if (result && result.shiftRotationScheduleWithDates && result.shiftRotationScheduleWithDates.length) {

                let endDate = result.endDate

                let repeatCount = shiftRotationDetails[0].Repeat_Count

                if (repeatCount > 0) {
                    // let repeatData = []
                    for (let i = 1; i <= repeatCount; i++) {
                        let loopResult = formShiftRotation(shiftRotationDetails, endDate);
                        result.shiftRotationScheduleWithDates = result.shiftRotationScheduleWithDates.concat(loopResult.shiftRotationScheduleWithDates);
                        endDate = loopResult.endDate
                    }
                }

                return { 'shiftRotationScheduleWithDates': result.shiftRotationScheduleWithDates, 'shiftEndDate': endDate }
            }

        }
        throw 'SS0105'
    }
    catch (err) {
        console.log('Error in formShiftRotationScheduleWithDates', err)
        throw err
    }
}

function formShiftRotation(shiftRotationDetails, shiftStartDate) {
    try {
        let lastShiftEndDate = null
        let lastEndDate = null;
        let shiftRotationScheduleWithDates = shiftRotationDetails.map(el => {
            let applicablePeriod = el.Applicable_Period;

            // Convert weeks to days if Period_Unit is 'week'
            if (el.Period_Unit === 'week') {
                applicablePeriod *= 7;
            }

            // Calculate the Shift_End_Date based on start date and total duration
            let shiftEndDate = moment(shiftStartDate).add(applicablePeriod - 1, 'days');

            // Update the last end date if it's later than the current
            if (!lastEndDate || shiftEndDate.isAfter(lastEndDate)) {
                lastEndDate = moment(shiftEndDate);
            }

            let shiftData = {
                ShiftType_Id: el.ShiftType_Id,
                Enable_Roster_Leave: el.Enable_Roster_Leave,
                LeaveType_Id: el.LeaveType_Id,
                Shift_Start_Date: shiftStartDate.format('YYYY-MM-DD'),
                Shift_End_Date: shiftEndDate.format('YYYY-MM-DD'),
            };

            shiftStartDate = moment(shiftEndDate).add(1, 'days');
            lastShiftEndDate = shiftStartDate
            return shiftData;
        });
        return { "shiftRotationScheduleWithDates": shiftRotationScheduleWithDates, 'endDate': lastShiftEndDate }
    } catch (err) {
        console.log('Error in formShiftRotation', err)
        throw err
    }
}

function formWeekOffDates(shiftStartDate, shiftEndDate, weekOffDay = 0, plusDays = 0) {
    try {
        shiftStartDate = moment(shiftStartDate).subtract(1, 'day')
        shiftEndDate = moment(shiftEndDate)

        //Form Weekdays
        let weekdays = [], weekOffArray = [];
        //weekOffDay - 6, plusDays - 2 -> [6,7,8]
        for (let i = 0; i <= plusDays; i++) {
            weekdays.push(weekOffDay + i); // Use % 7 to wrap around if the days exceed 6
        }

        let maximumWeekOffDates = null;
        while (shiftStartDate && shiftStartDate.isBefore(shiftEndDate)) {
            weekdays.forEach((day, index) => {
                const weekOffDate = shiftStartDate.clone().add(day, 'days');
                if (weekOffDate.isSameOrBefore(shiftEndDate)) {
                    weekOffArray.push(weekOffDate.format('YYYY-MM-DD'));
                }
                // Reset shiftStartDate after processing the last element
                maximumWeekOffDates = weekOffDate;
            });
            shiftStartDate = maximumWeekOffDates;
        }

        return weekOffArray;
    }
    catch (err) {
        console.log('Error in formWeekOffDates', err)
        throw err
    }
}

module.exports.updateShiftScheduling = async (parent, args, context, info) => {
    console.log('Inside updateShiftScheduling() function');
    const { ApolloError } = require('apollo-server-lambda');
    // require common constant files
    const { formName, roles, systemLogs } = require('../../../common/appconstants');
    // Organization database connection
    const knex = require('knex');

    const orgDb = knex(context.connection.OrganizationDb);
    try {
        let action;
        let role;
        if (args.shiftSchedulingId) {
            //Edit
            action = "Edit";
            role = roles.roleUpdate;
        } else {
            //Add
            action = "Add";
            role = roles.roleAdd;
        }
        // Check form view access rights
        let loginId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(orgDb, loginId, formName.shiftScheduling, role);

        if (checkRights === true) {
            let employeeId = args.employeeId;
            let weekOff = (args.weekOff === 1) ? args.weekOff : 0;
            weekOff = parseInt(weekOff);
            let shiftTypeId = args.shiftTypeId;
            let shiftStartDate = args.shiftStartDate;
            let shiftEndDate = args.shiftEndDate;
            let shiftRotationId = args.rotationId;
            let shiftRotationScheduleWithDates = null;

            // Get Roster Settings
            let rosterSettings = await orgDb(ehrTables.rosterSettings).select('Dynamic_Week_Off').first()


            if (shiftTypeId && !shiftRotationId) {
                // get shift type            
                let shiftTypeExist = await orgDb(ehrTables.empShiftType)
                    .where('Shift_Type_Id', shiftTypeId)
                    .where('Status', 'Active');
                // check shift type exist or not
                if (!shiftTypeExist[0]) {
                    let errResult = commonLib.func.getError('', 'SS0134');
                    return {
                        success: false,
                        errorCode: errResult.code,
                        message: errResult.message
                    }
                }
            }

            if (shiftRotationId && !args.shiftSchedulingId) {
                const result = await formShiftRotationScheduleWithDates(orgDb, shiftRotationId, shiftStartDate)
                if (result) {
                    shiftEndDate = moment(result.shiftEndDate).subtract(1, 'days').format('YYYY-MM-DD')
                    shiftRotationScheduleWithDates = result.shiftRotationScheduleWithDates
                    //Validations
                    //Date of Join and Resignation
                    let invalidDOJEmpIds = await checkWithJoinAndResignationDate(orgDb, employeeId, shiftStartDate, shiftEndDate)
                    if (invalidDOJEmpIds && invalidDOJEmpIds.length) {
                        let invalidDOJEmpNames = await getEmpNameById(orgDb, invalidDOJEmpIds)
                        return {
                            success: false,
                            errorCode: "SS0128",
                            message: "Unable to add the Shift for " + invalidDOJEmpNames.toString() + " as shift date exist before their DOJ or after their resignation"
                        }
                    }

                    //Already Exists
                    let { shiftExistsEmpIds } = await getShiftDateExistEmpId(orgDb, employeeId, shiftStartDate, shiftEndDate, null, args.shiftSchedulingId);
                    if (shiftExistsEmpIds && shiftExistsEmpIds.length) {
                        let shiftExistsEmpNames = await getEmpNameById(orgDb, shiftExistsEmpIds)
                        return {
                            success: false,
                            errorCode: "SS0129",
                            message: "Unable to add the Shift for " + shiftExistsEmpNames.toString() + " as the shift already scheduled for the chosen date"
                        }
                    }

                    //Form the data
                    let shiftSchedulingData = [];
                    let leaveData = []
                    let weekOffDates = []
                    let empTimeZone = await commonLib.func.getEmployeeTimeZone(loginId, orgDb, 1);

                    //Form Week Off Dates
                    if (rosterSettings.Dynamic_Week_Off && args.weekOffDays) {
                        weekOffDates = formWeekOffDates(shiftStartDate, shiftEndDate, args.weekOffDays, args.plusDays);
                    }

                    //Form the data
                    employeeId.forEach(id => {
                        shiftRotationScheduleWithDates.forEach(el => {
                            let startDate = moment(el.Shift_Start_Date);
                            let endDate = moment(el.Shift_End_Date);

                            while (startDate <= endDate) {
                                let weekOff = 0;
                                if (weekOffDates.length && rosterSettings.Dynamic_Week_Off && weekOffDates.includes(startDate.format('YYYY-MM-DD'))) {
                                    weekOff = 1;
                                }
                                shiftSchedulingData.push({
                                    Employee_Id: id,
                                    Shift_Type_Id: el.ShiftType_Id,
                                    Rotation_Id: shiftRotationId,
                                    Shift_Start_Date: startDate.format('YYYY-MM-DD'), // Format as YYYY-MM-DD
                                    Shift_End_Date: startDate.format('YYYY-MM-DD'), // Format as YYYY-MM-DD
                                    Approver_Id: 1,
                                    Week_Off: weekOff,
                                    Approval_Status: 'approved',
                                    Added_On: empTimeZone,
                                    Added_By: loginId,
                                    Lock_Flag: 0
                                });

                                // Increment the date by one day
                                startDate.add(1, 'days');
                            }
                        });
                        if (shiftRotationScheduleWithDates[0].Enable_Roster_Leave === 'Yes') {
                            leaveData.push({
                                LeaveType_Id: shiftRotationScheduleWithDates[0].LeaveType_Id,
                                Employee_Id: id,
                                Eligible_Days: 0,
                                Leaves_Taken: 0,
                                Leave_Balance: 0,
                                No_Of_Days: 0,
                                Last_CO_Balance: 0,
                                Accumulation_Lapsed_Days: 0,
                                CO_Year: moment(shiftStartDate).year(),
                                LE_Year: moment(shiftStartDate).year(),
                                Leave_Closure_Start_Date: moment(shiftStartDate).format('YYYY-MM-DD'),
                                Leave_Closure_End_Date: moment(shiftEndDate).format('YYYY-MM-DD')
                            })
                        }
                    });

                    //Insert the data
                    await orgDb.transaction(async (trx) => {
                        if (leaveData && leaveData.length) {
                            await Promise.all([
                                //Insert the shift mapping data
                                orgDb(ehrTables.shiftEmpMapping)
                                    .insert(shiftSchedulingData)
                                    .transacting(trx),

                                //Insert the leave data
                                orgDb(ehrTables.empEligibleLeave)
                                    .insert(leaveData)
                                    .transacting(trx)
                            ])
                        } else {
                            await orgDb(ehrTables.shiftEmpMapping)
                                .insert(shiftSchedulingData)
                                .transacting(trx)
                        }

                    })
                        .catch((err) => {
                            console.log('Error while adding the shift based on rotation in the updateShiftScheduling() function', err);
                            let errResult = commonLib.func.getError('', 'SS0003');
                            throw new ApolloError(errResult.message, errResult.code);
                        })

                    // Call step Function
                    await commonLib.func.triggerLambda(process.env.updateRosterLeaveBatch,'RequestResponse', null);

                    let systemLogParams = {
                        action: systemLogs.roleAdd,
                        userIp: context.User_Ip,
                        employeeId: loginId,
                        formName: formName.shiftScheduling,
                        trackingColumn: '',
                        organizationDbConnection: orgDb,
                        uniqueId: shiftRotationId
                    };

                    // call function createSystemLogActivities() to update system log activities
                    await commonLib.func.createSystemLogActivities(systemLogParams);

                    return {
                        success: true,
                        errorCode: null,
                        message: "Shift added successfully based on shift rotation."
                    }
                } else {
                    throw 'SS0105'
                }

            } else {

                let invalidDOJEmpIds = await checkWithJoinAndResignationDate(orgDb, employeeId, shiftStartDate, shiftEndDate)

                let invalidDOJEmpNames = await getEmpNameById(orgDb, invalidDOJEmpIds)

                let validEmpIds = employeeId.filter(x => !invalidDOJEmpIds.includes(x));

                let shiftExistsEmpNames = '';
                let shiftOverlapEmpNames = '';

                // Call function getEmployeeTimeZone() to get current date and time based on employee time zone 
                // Send employeeId ,dbConnection and another flag as 1(This flag is to get the time zone date with time)
                let empTimeZone = await commonLib.func.getEmployeeTimeZone(loginId, orgDb, 1);

                if (action == "Edit") {
                    // Edit shift scheduling
                    if (validEmpIds[0]) {
                        // get shift details
                        let shiftDetails = await orgDb(ehrTables.shiftEmpMapping)
                            .select('Shift_Type_Id', 'Week_Off')
                            .where('Shift_Schedule_Id', args.shiftSchedulingId)
                            .then(response => {
                                return response
                            })
                            .catch(shiftDetailsError => {
                                console.log('Error while getting the shift details in updateShiftScheduling() function.', shiftDetailsError);
                                return [];
                            });
                        if (shiftDetails.length > 0) {
                            let isnewShiftTypeIdAttendanceExists = 0;
                            let newShiftWorkScheduleDetails = {};

                            // check attendance exists for the old shift type id
                            let getOldShiftAttendance = await commonLib.employees.getAttendance(shiftStartDate, employeeId, orgDb);
                            if (getOldShiftAttendance.attendanceDetails.errorCode) {
                                console.log('Response form the getAttendance() function,', getOldShiftAttendance, ' for the shift schedule id:', args.shiftSchedulingId);
                                let errResult = commonLib.func.getError('', getOldShiftAttendance.attendanceDetails.errorCode);
                                return {
                                    success: false,
                                    errorCode: errResult.code,
                                    message: errResult.message
                                }
                            } else {
                                let isOldShiftTypeIdAttendanceExists = getOldShiftAttendance.attendanceDetails;
                                // get Overlap_Shift_Schedule flag
                                let overlapShiftEnabled = await orgDb(ehrTables.rosterSettings).select('Overlap_Shift_Schedule').first();
                                overlapShiftEnabled = overlapShiftEnabled.Overlap_Shift_Schedule ? overlapShiftEnabled.Overlap_Shift_Schedule : 0;

                                /** If the overlapping shift enabled and if the old shift type id and new shift type id differs */
                                if (shiftTypeId !== shiftDetails[0].Shift_Type_Id) {
                                    let newShiftWorkScheduleResponse = await getNewShiftTypeWorkSchedule(orgDb, employeeId, shiftStartDate, shiftTypeId, overlapShiftEnabled);
                                    newShiftWorkScheduleDetails = newShiftWorkScheduleResponse.newShiftTypeWorkSchedule;

                                    //if the new shift work schedule is not fetched
                                    if (newShiftWorkScheduleResponse.errorCode) {
                                        let errResult = commonLib.func.getError('', newShiftWorkScheduleResponse.errorCode);
                                        return {
                                            success: false,
                                            errorCode: errResult.code,
                                            message: errResult.message
                                        }
                                    } else {
                                        // check attendance exists for the new shift type id
                                        let getNewShiftAttendance = await commonLib.employees.getAttendance(shiftStartDate, employeeId, orgDb, 'get-count', '', newShiftWorkScheduleDetails);
                                        isnewShiftTypeIdAttendanceExists = getNewShiftAttendance.attendanceDetails;
                                        if (getNewShiftAttendance.errorCode) {
                                            console.log('Response form the getAttendance() function,', getNewShiftAttendance, ' for the shift schedule id:', args.shiftSchedulingId, ' and for the new shift type id:', shiftTypeId, ' and for the workschedule, ', newShiftWorkScheduleResponse);
                                            let errResult = commonLib.func.getError('', getNewShiftAttendance.attendanceDetails.errorCode);
                                            return {
                                                success: false,
                                                errorCode: errResult.code,
                                                message: errResult.message
                                            }
                                        }
                                    }
                                }

                                // Check leave already exist for the shift date
                                let isLeaveExists = await commonLib.employees.getLeaves(employeeId, shiftStartDate, shiftStartDate, 'update-shift', orgDb);

                                // Check compensatory_off exist for the shift date
                                let isCompensatoryOffExists = await commonLib.employees.getCompensatoryOff(employeeId, shiftStartDate, shiftStartDate, 'update-shift', orgDb);

                                /** Return the error response when the shift type id or week off changed. */
                                if ((isOldShiftTypeIdAttendanceExists || isLeaveExists || isCompensatoryOffExists) && ((parseInt(shiftDetails[0].Shift_Type_Id) !== parseInt(shiftTypeId))
                                    || (parseInt(shiftDetails[0].Week_Off) !== weekOff))) {
                                    console.log('Shift type or week off is changed. But attendance or leave or compensatory-off exist for the employee id');
                                    let errResult = commonLib.func.getError('', 'SS0149');
                                    return {
                                        success: false,
                                        errorCode: errResult.code,
                                        message: errResult.message
                                    }
                                } else {
                                    //Validate the shift type exist and if the new shift type overlap with the previous day or next day shift type
                                    let { shiftExistsEmpIds, shiftOverlapEmpIds } = await getShiftDateExistEmpId(orgDb, validEmpIds, shiftStartDate, shiftEndDate, shiftTypeId, args.shiftSchedulingId);

                                    shiftExistsEmpNames = await getEmpNameById(orgDb, shiftExistsEmpIds);

                                    shiftOverlapEmpNames = await getEmpNameById(orgDb, shiftOverlapEmpIds);

                                    if (shiftExistsEmpIds[0]) {
                                        errResult = commonLib.func.getError('', 'SS0102');
                                        return {
                                            success: false,
                                            errorCode: errResult.code,
                                            message: errResult.message
                                        };
                                    } else if (shiftOverlapEmpIds[0]) {
                                        errResult = commonLib.func.getError('', 'SS0103');
                                        return {
                                            success: false,
                                            errorCode: errResult.code,
                                            message: errResult.message
                                        };
                                    } else {
                                        if (isnewShiftTypeIdAttendanceExists) {
                                            console.log('New shift-type attendance exist and new shift work schedule details,', newShiftWorkScheduleDetails);
                                            let newShiftResponseConsiderationFrom = newShiftWorkScheduleDetails.considerationFrom ? newShiftWorkScheduleDetails.considerationFrom.toISOString().substring(0, 19).replace('T', ' ') : null; // Response '2020-08-31 10:20:59'
                                            let newShiftResponseConsiderationTo = newShiftWorkScheduleDetails.considerationTo ? newShiftWorkScheduleDetails.considerationTo.toISOString().substring(0, 19).replace('T', ' ') : null;
                                            return {
                                                success: false,
                                                errorCode: 'SS0132',
                                                message: 'Unable to change the shift-type as the attendance exist in the new shift-type consideration date-time duration from ' + newShiftResponseConsiderationFrom + ' to ' + newShiftResponseConsiderationTo
                                            }
                                        }
                                        else {
                                            /** If week off is selected for the shift date and shift type id*/
                                            if (weekOff === 1) {
                                                let validateHolidayOverrideeResponse = await validateWeekOffApplicableOnHoliday(orgDb, employeeId, shiftTypeId, shiftStartDate);
                                                isWeekOffValueAllowedToUpdate = validateHolidayOverrideeResponse.isWeekOffApplicable;
                                            } else {
                                                isWeekOffValueAllowedToUpdate = 1;
                                            }

                                            if (isWeekOffValueAllowedToUpdate === 1) {
                                                // update shift details
                                                let updateResponse = await orgDb(ehrTables.shiftEmpMapping)
                                                    .update({
                                                        Employee_Id: employeeId[0],
                                                        Shift_Type_Id: shiftTypeId,
                                                        Shift_Start_Date: shiftStartDate,
                                                        Shift_End_Date: shiftEndDate,
                                                        Week_Off: weekOff,
                                                        Updated_On: empTimeZone,
                                                        Updated_By: loginId
                                                    })
                                                    .where("Shift_Schedule_Id", args.shiftSchedulingId)
                                                    .then(async (updated) => {
                                                        if (updated) {
                                                            // form inputs to update system log activities commonly
                                                            let systemLogParams = {
                                                                action: systemLogs.roleUpdate,
                                                                userIp: context.User_Ip,
                                                                employeeId: loginId,
                                                                formName: formName.shiftScheduling,
                                                                trackingColumn: '',
                                                                organizationDbConnection: orgDb,
                                                                uniqueId: args.shiftSchedulingId
                                                            };

                                                            // call function createSystemLogActivities() to update system log activities
                                                            await commonLib.func.createSystemLogActivities(systemLogParams);

                                                            // return success message based on the inputs params
                                                            if (parseInt(shiftDetails[0].Shift_Type_Id) !== parseInt(shiftTypeId) && parseInt(shiftDetails[0].Week_Off) !== weekOff) {
                                                                return {
                                                                    success: true,
                                                                    errorCode: null,
                                                                    message: "Shift and Week off updated successfully"
                                                                }
                                                            } else if (parseInt(shiftDetails[0].Week_Off) !== weekOff) {
                                                                return {
                                                                    success: true,
                                                                    errorCode: null,
                                                                    message: "Week off updated successfully"
                                                                }
                                                            } else {
                                                                return {
                                                                    success: true,
                                                                    errorCode: null,
                                                                    message: "Shift updated successfully"
                                                                }
                                                            }
                                                        } else {
                                                            let errResult = commonLib.func.getError('', 'SS0110');
                                                            return {
                                                                success: false,
                                                                errorCode: errResult.code,
                                                                message: errResult.message
                                                            }
                                                        }
                                                    })
                                                    .catch((updateShiftCatchErr) => {
                                                        console.log('Error while updating the shift in the updateShiftScheduling() function .catch block', updateShiftCatchErr);
                                                        let errResult = commonLib.func.getError('', 'SS0003');
                                                        throw new ApolloError(errResult.message, errResult.code);
                                                    });
                                                // return response back to UI
                                                return updateResponse;
                                            } else {
                                                console.log('Week off is not allowed to apply for the shift schedule id:', args.shiftSchedulingId);
                                                let errResult = commonLib.func.getError('', 'SS0147');
                                                return { success: false, code: errResult.code, message: errResult.message };
                                            }
                                        }
                                    }
                                }
                            }
                        } else {

                            let errResult = commonLib.func.getError('', 'SS0145');
                            return {
                                success: false,
                                errorCode: errResult.code,
                                message: errResult.message
                            }
                        }
                    } else {
                        let errResult;
                        if (invalidDOJEmpIds[0]) {
                            errResult = commonLib.func.getError('', 'SS0101');
                        } else {
                            errResult = commonLib.func.getError('', 'SS0111');
                        }
                        return {
                            success: false,
                            errorCode: errResult.code,
                            message: errResult.message
                        }
                    }
                } else {
                    // Add shift scheduling
                    if (validEmpIds[0]) {
                        //Validate the shift type exist and if the new shift type overlap with the previous day or next day shift type
                        let { shiftExistsEmpIds, shiftOverlapEmpIds, shiftExistsEmployeeDetails } = await getShiftDateExistEmpId(orgDb, validEmpIds, shiftStartDate, shiftEndDate, shiftTypeId, args.shiftSchedulingId);

                        let groupShiftExistEmployeeDetails = (shiftExistsEmployeeDetails?.length) ? await commonLib.func.organizeData(shiftExistsEmployeeDetails, 'Employee_Id', 'Shift_Start_Date') : {};

                        shiftExistsEmpNames = await getEmpNameById(orgDb, shiftExistsEmpIds);

                        shiftOverlapEmpNames = await getEmpNameById(orgDb, shiftOverlapEmpIds);

                        if (shiftOverlapEmpIds[0]) {
                            errResult = commonLib.func.getError('', 'SS0103');
                            return {
                                success: false,
                                errorCode: errResult.code,
                                message: errResult.message
                            };
                        } else {
                            // Check shift start and end date difference
                            var startDate = moment(shiftStartDate, 'YYYY-MM-DD');
                            var endDate = moment(shiftEndDate, 'YYYY-MM-DD');

                            // Get difference between 2 dates in days
                            let dateDiff = endDate.diff(startDate, 'days');

                            // variable declaration
                            let shiftData = [];
                            let shiftDataJson = {};
                            let isAttendanceLeaveCompOffExistEmployeeIds = [];
                            let newShiftDetailsNotRetrievedEmployeeIds = [];
                            let attendanceNotRetrievedEmployeeIds = [];
                            let weekOffNotApplicableEmployeeIds = [];

                            // get Overlap_Shift_Schedule flag
                            let overlapShiftEnabled = await orgDb(ehrTables.rosterSettings).select('Overlap_Shift_Schedule').first();
                            overlapShiftEnabled = overlapShiftEnabled.Overlap_Shift_Schedule ? overlapShiftEnabled.Overlap_Shift_Schedule : 0;

                            // Check dateDiff. If its 0 then there is no range between start and end date
                            if (dateDiff === 0) {
                                // iterate the validEmpIds array
                                for (let empId = 0; empId <= validEmpIds.length - 1; empId++) {
                                    let isAttendanceExist = 0;
                                    let isLeaveExists = 0;
                                    let isCompensatoryOffExists = 0;
                                    let getNewShiftAttendance = {};
                                    shiftDataJson = {};

                                    let currentEmpIdShiftDateKey = `${validEmpIds[empId]}|${shiftStartDate}`;
                                    let employeeShiftOnDate = groupShiftExistEmployeeDetails[currentEmpIdShiftDateKey]?.[0] ?? {};

                                    if (Object.keys(employeeShiftOnDate).length > 0) {
                                       console.log('Shift exist for the employee for the shift start date',currentEmpIdShiftDateKey,employeeShiftOnDate)
                                    }else{
                                        let newShiftWorkScheduleResponse = await getNewShiftTypeWorkSchedule(orgDb, validEmpIds[empId], shiftStartDate, shiftTypeId, overlapShiftEnabled);
                                        let newShiftWorkScheduleDetails = newShiftWorkScheduleResponse.newShiftTypeWorkSchedule;

                                        //if the new shift work schedule is not fetched
                                        if (newShiftWorkScheduleResponse.errorCode) {
                                            newShiftDetailsNotRetrievedEmployeeIds.push(validEmpIds[empId]);
                                        } else {
                                            // check attendance exists for the new shift type id
                                            getNewShiftAttendance = await commonLib.employees.getAttendance(shiftStartDate, validEmpIds[empId], orgDb, 'get-count', '', newShiftWorkScheduleDetails);

                                            // If an error occured while getting the attendance details for the shift date then consider attendance exist as 1
                                            if (getNewShiftAttendance.errorCode) {
                                                attendanceNotRetrievedEmployeeIds.push(validEmpIds[empId]);
                                            } else {
                                                isAttendanceExist = getNewShiftAttendance.attendanceDetails;

                                                // Check leave already exist for this date
                                                isLeaveExists = await commonLib.employees.getLeaves(validEmpIds[empId], shiftStartDate, shiftStartDate, 'update-shift', orgDb);
                                                // check compensatory_off exist for this date
                                                isCompensatoryOffExists = await commonLib.employees.getCompensatoryOff(validEmpIds[empId], shiftStartDate, shiftStartDate, 'update-shift', orgDb);

                                                /** Return the error response when the attendance or leave or compensatory off exist. */
                                                if (isLeaveExists || isCompensatoryOffExists || isAttendanceExist) {
                                                    console.log('Attendance or leave or compensatory-off exist for the above shift details.');
                                                    console.log('Add shift scheduling when the date difference is 0. EmployeeId:', validEmpIds[empId], 'shift start date: ', shiftStartDate, ' shift type id: ', shiftTypeId, 'isLeaveExists: ', isLeaveExists, 'isCompensatoryOffExists: ', isCompensatoryOffExists, ' isAttendanceExist: ', isAttendanceExist);
                                                    isAttendanceLeaveCompOffExistEmployeeIds.push(validEmpIds[empId]);
                                                } else {
                                                    /** If week off is selected for the shift date and shift type id*/
                                                    if (args.weekOff && args.weekOff === 1) {
                                                        let validateHolidayOverrideResponse = await validateWeekOffApplicableOnHoliday(orgDb, validEmpIds[empId], shiftTypeId, shiftStartDate);
                                                        isWeekOffValueAllowedToUpdate = validateHolidayOverrideResponse.isWeekOffApplicable;
                                                    } else {
                                                        isWeekOffValueAllowedToUpdate = 1;
                                                    }

                                                    if (isWeekOffValueAllowedToUpdate === 1) {
                                                        /** This if block will be used in 2 places
                                                         * - Assgn shift through form - Week off param  may/may not be come in the input so check if exist or not. If yes use that variable otherwise set as 0
                                                         * - Assign shift through calendar - Week off param  may/may not be come in the input so check if exist or not. If yes use that variable otherwise set as 0
                                                        */
                                                        shiftDataJson = {
                                                            Employee_Id: validEmpIds[empId],
                                                            Shift_Type_Id: shiftTypeId,
                                                            Shift_Start_Date: shiftStartDate,
                                                            Shift_End_Date: shiftEndDate,
                                                            Week_Off: args.weekOff ? args.weekOff : 0, // Week off asignment will come 2 places. Update through Bulk week off import and calendar
                                                            Approver_Id: 1,
                                                            Approval_Status: "approved",
                                                            Added_On: empTimeZone,
                                                            Added_By: loginId,
                                                            Lock_Flag: 0
                                                        };
                                                        shiftData.push(shiftDataJson);
                                                    }else {
                                                        console.log('Week off is not applicable', validEmpIds[empId], shiftStartDate);
                                                        weekOffNotApplicableEmployeeIds.push(validEmpIds[empId]);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                // if dateDiff is greater than 0 then there is range between start and end date
                            } else if (dateDiff > 0) {
                                // iterate the validEmpIds array
                                for (let empId = 0; empId <= validEmpIds.length - 1; empId++) {

                                    // iterate the date difference and form the shift data to insert records in table
                                    for (let day = 0; day <= dateDiff; day++) {
                                        let newShiftStartDate = moment(shiftStartDate, "YYYY-MM-DD").add(day, 'days').format('YYYY-MM-DD');
                                        // variable declration
                                        let shiftDataJson = {};
                                        let getNewShiftAttendance = {};
                                        let isAttendanceExist = 0;
                                        let isLeaveExists = 0;
                                        let isCompensatoryOffExists = 0;

                                        let currentEmpIdShiftDateKey = `${validEmpIds[empId]}|${newShiftStartDate}`;
                                        let employeeShiftOnDate = groupShiftExistEmployeeDetails[currentEmpIdShiftDateKey]?.[0] ?? {};
                                        if (Object.keys(employeeShiftOnDate).length > 0) {
                                           console.log('Shift exist for the employee for the shift start date in dateDiff greater than zero block',currentEmpIdShiftDateKey, employeeShiftOnDate)
                                        }else{
                                            let newShiftWorkScheduleResponse = await getNewShiftTypeWorkSchedule(orgDb, validEmpIds[empId], newShiftStartDate, shiftTypeId, overlapShiftEnabled);
                                            let newShiftWorkScheduleDetails = newShiftWorkScheduleResponse.newShiftTypeWorkSchedule;

                                            //if the new shift work schedule is not fetched
                                            if (newShiftWorkScheduleResponse.errorCode) {
                                                newShiftDetailsNotRetrievedEmployeeIds.push(validEmpIds[empId]);
                                            } else {
                                                // check attendance exists for the new shift type id
                                                getNewShiftAttendance = await commonLib.employees.getAttendance(newShiftStartDate, validEmpIds[empId], orgDb, 'get-count', '', newShiftWorkScheduleDetails);
                                                // If an error occured while getting the attendance details for the shift date then consider the attendance exist as 1
                                                if (getNewShiftAttendance.errorCode) {
                                                    attendanceNotRetrievedEmployeeIds.push(validEmpIds[empId]);
                                                } else {
                                                    isAttendanceExist = getNewShiftAttendance.attendanceDetails;

                                                    // Check leave already exist for this date
                                                    isLeaveExists = await commonLib.employees.getLeaves(validEmpIds[empId], newShiftStartDate, newShiftStartDate, 'update-shift', orgDb);
                                                    // check compensatory_off exist for this
                                                    isCompensatoryOffExists = await commonLib.employees.getCompensatoryOff(validEmpIds[empId], newShiftStartDate, newShiftStartDate, 'update-shift', orgDb);

                                                    /** Return the error response when the attendance or leave or compensatory off exist. */
                                                    if (isLeaveExists || isCompensatoryOffExists || isAttendanceExist) {
                                                        console.log('Attendance or leave or compensatory-off exist for the above shift details.');
                                                        console.log('Add shift scheduling when the date difference is greater than 0. EmployeeId:', validEmpIds[empId], 'shift start date: ', newShiftStartDate, ' shift type id: ', shiftTypeId, ' isLeaveExists:', isLeaveExists, ' isCompensatoryOffExists:', isCompensatoryOffExists, ' isAttendanceExist: ', isAttendanceExist);
                                                        console.log('Response form the getAttendance() function', getNewShiftAttendance, ' and the work schedule details, ', newShiftWorkScheduleResponse);

                                                        isAttendanceLeaveCompOffExistEmployeeIds.push(validEmpIds[empId]);
                                                    } else {
                                                        /** If week off is selected for the shift date and shift type id*/
                                                        if (args.weekOff && args.weekOff === 1) {
                                                            let validateHolidayOverrideResponse = await validateWeekOffApplicableOnHoliday(orgDb, validEmpIds[empId], shiftTypeId, newShiftStartDate);
                                                            isWeekOffValueAllowedToUpdate = validateHolidayOverrideResponse.isWeekOffApplicable;
                                                        } else {
                                                            isWeekOffValueAllowedToUpdate = 1;
                                                        }

                                                        if (isWeekOffValueAllowedToUpdate === 1) {
                                                            /** Form data to insert records in shift mapping table. This else block will be used in 2 places.
                                                             * - Shift bulk import - Week off param will not be come in the input so set default 0
                                                             * - Assign shift through form - Week off param  will not be come in the input so set default 0
                                                            */
                                                            shiftDataJson = {
                                                                Employee_Id: validEmpIds[empId],
                                                                Shift_Type_Id: shiftTypeId,
                                                                Shift_Start_Date: newShiftStartDate,
                                                                Shift_End_Date: newShiftStartDate,
                                                                Week_Off: 0, // By default 0
                                                                Approver_Id: 1,
                                                                Approval_Status: "approved",
                                                                Added_On: empTimeZone,
                                                                Added_By: loginId,
                                                                Lock_Flag: 0
                                                            };

                                                            // push shiftDataJson into shiftData array to insert in table
                                                            shiftData.push(shiftDataJson);
                                                        }else {
                                                            console.log('Week off is not applicable in datediff greater than zero block', validEmpIds[empId], shiftTypeId, newShiftStartDate);
                                                            weekOffNotApplicableEmployeeIds.push(validEmpIds[empId]);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            // get the employee name for whom the attendance or leave or comp off exist
                            let attendanceCompOffLeaveExistEmpNames = await getEmpNameById(orgDb, isAttendanceLeaveCompOffExistEmployeeIds);
                            // get the employee name for whom the new shift work schedule detail is empty
                            let newShiftDetailsNotRetrievedEmpNames = await getEmpNameById(orgDb, newShiftDetailsNotRetrievedEmployeeIds);
                            // get the employee name for whom the week off is not applicable
                            let weekOffNotApplicableEmpNames = await getEmpNameById(orgDb, weekOffNotApplicableEmployeeIds);
                            // get the employee name for whom the attendance is not retrieved
                            let attendanceNotRetrievedEmpNames = await getEmpNameById(orgDb, attendanceNotRetrievedEmployeeIds)
                            if (shiftData.length > 0) {
                                // Query to insert records in shift_emp_mapping table
                                let shiftAssignmentResponse = await orgDb(ehrTables.shiftEmpMapping)
                                    .insert(shiftData)
                                    .onConflict(['Employee_Id', 'Shift_Start_Date', 'Shift_End_Date'])
                                    .ignore()
                                    .then(async (insertedIds) => {
                                        if (insertedIds) {
                                            //If the shift is not added then 0 is returned in the insertedIds array. So remove the duplicate zeros
                                            insertedIds = [...new Set(insertedIds)];
                                            //Find the 0 id index
                                            let zeroIndex = insertedIds.indexOf(0);
                                            //If exist remove the zero from the array
                                            if (zeroIndex !== -1) {
                                                insertedIds.splice(zeroIndex, 1);
                                            }
                                            //If the records are inserted
                                            if (insertedIds && insertedIds.length > 0) {
                                                // form inputs to update system log activities commonly
                                                let systemLogParams = {
                                                    action: systemLogs.roleAdd,
                                                    userIp: context.User_Ip,
                                                    employeeId: loginId,
                                                    formName: formName.shiftScheduling,
                                                    trackingColumn: '',
                                                    organizationDbConnection: orgDb,
                                                    uniqueId: insertedIds[0]
                                                };

                                                if (!invalidDOJEmpIds[0] && !shiftExistsEmpIds[0] && !shiftOverlapEmpIds[0] && !isAttendanceLeaveCompOffExistEmployeeIds[0]
                                                    && !newShiftDetailsNotRetrievedEmployeeIds[0] && !weekOffNotApplicableEmployeeIds[0]) {
                                                    // call function createSystemLogActivities() to update system log activities
                                                    !args.source ? await commonLib.func.createSystemLogActivities(systemLogParams) : null;
                                                    // check inputs params based on that return success response to UI
                                                    if (args.weekOff === 1) {
                                                        return {
                                                            success: true,
                                                            errorCode: null,
                                                            message: "Shift and week off scheduled successfully"
                                                        }
                                                    } else {
                                                        return {
                                                            success: true,
                                                            errorCode: null,
                                                            message: "Shift scheduled successfully"
                                                        }
                                                    }
                                                } else {
                                                    // call function createSystemLogActivities() to update system log activities
                                                    !args.source ? await commonLib.func.createSystemLogActivities(systemLogParams) : null;

                                                    let responseMessageParams = {
                                                        responseMessage: 'Shift added successfully for employees partially. Unable to add for ',
                                                        invalidDOJEmpIds: invalidDOJEmpIds,
                                                        invalidDOJEmpNames: invalidDOJEmpNames,
                                                        shiftExistsEmpIds: shiftExistsEmpIds,
                                                        shiftExistsEmpNames: shiftExistsEmpNames,
                                                        shiftOverlapEmpIds: shiftOverlapEmpIds,
                                                        shiftOverlapEmpNames: shiftOverlapEmpNames,
                                                        isAttendanceLeaveCompOffExistEmployeeIds: isAttendanceLeaveCompOffExistEmployeeIds,
                                                        attendanceCompOffLeaveExistEmpNames: attendanceCompOffLeaveExistEmpNames,
                                                        newShiftDetailsNotRetrievedEmployeeIds: newShiftDetailsNotRetrievedEmployeeIds,
                                                        newShiftDetailsNotRetrievedEmpNames: newShiftDetailsNotRetrievedEmpNames,
                                                        attendanceNotRetrievedEmployeeIds: attendanceNotRetrievedEmployeeIds,
                                                        attendanceNotRetrievedEmpNames: attendanceNotRetrievedEmpNames,
                                                        weekOffNotApplicableEmployeeIds: weekOffNotApplicableEmployeeIds,
                                                        weekOffNotApplicableEmpNames: weekOffNotApplicableEmpNames
                                                    };

                                                    let addShiftResponseMessage = await formErrorResponseMessage(responseMessageParams);//Form the response message

                                                    return {
                                                        success: false,
                                                        errorCode: "SS0127",
                                                        message: addShiftResponseMessage
                                                    }
                                                }
                                            } else {
                                                return {
                                                    success: true,
                                                    errorCode: "",
                                                    message: "Shift already scheduled."
                                                }
                                            }
                                        } else {
                                            return {
                                                success: true,
                                                errorCode: "",
                                                message: "Shift already scheduled."
                                            }
                                        }
                                    })
                                    .catch((err) => {
                                        console.log('Error while adding the shift in updateShiftScheduling() function .catch block', err);
                                        let errResult = commonLib.func.getError('', 'SS0001');
                                        throw new ApolloError(errResult.message, errResult.code);
                                    });

                                // return Response
                                return shiftAssignmentResponse;
                            } else {
                                console.log('No record found to add the shift scheduling.shiftData: ', shiftData);

                                let responseMessageParams = {
                                    responseMessage: 'Unable to add the Shift for ',
                                    invalidDOJEmpIds: invalidDOJEmpIds,
                                    invalidDOJEmpNames: invalidDOJEmpNames,
                                    shiftExistsEmpIds: shiftExistsEmpIds,
                                    shiftExistsEmpNames: shiftExistsEmpNames,
                                    shiftOverlapEmpIds: shiftOverlapEmpIds,
                                    shiftOverlapEmpNames: shiftOverlapEmpNames,
                                    isAttendanceLeaveCompOffExistEmployeeIds: isAttendanceLeaveCompOffExistEmployeeIds,
                                    attendanceNotRetrievedEmployeeIds: attendanceNotRetrievedEmployeeIds,
                                    attendanceCompOffLeaveExistEmpNames: attendanceCompOffLeaveExistEmpNames,
                                    newShiftDetailsNotRetrievedEmployeeIds: newShiftDetailsNotRetrievedEmployeeIds,
                                    newShiftDetailsNotRetrievedEmpNames: newShiftDetailsNotRetrievedEmpNames,
                                    weekOffNotApplicableEmployeeIds: weekOffNotApplicableEmployeeIds,
                                    weekOffNotApplicableEmpNames: weekOffNotApplicableEmpNames
                                };

                                let addShiftResponseMessage = await formErrorResponseMessage(responseMessageParams);//Form the response message
                                return {
                                    success: false,
                                    errorCode: "SS0128",
                                    message: addShiftResponseMessage
                                }
                            }
                        }
                    } else {
                        let errResult;
                        if (invalidDOJEmpIds[0]) {
                            errResult = commonLib.func.getError('', 'SS0101');
                        } else {
                            errResult = commonLib.func.getError('', 'SS0108');
                        }
                        return {
                            success: false,
                            errorCode: errResult.code,
                            message: errResult.message
                        }
                    }
                }
            }
        } else if (checkRights === false) {
            let errResult;
            if (action == "Edit") {
                errResult = commonLib.func.getError('', '_DB0102');
            } else {
                errResult = commonLib.func.getError('', '_DB0101');
            }
            return {
                success: false,
                errorCode: errResult.code,
                message: errResult.message
            }
        } else {
            console.log("Error while checking rights in updateShiftScheduling", checkRights);
            let errResult = commonLib.func.getError('', 'SS0109');
            return {
                success: false,
                errorCode: errResult.code,
                message: errResult.message
            }
        }
    }
    catch (err) {
        console.log('Error in updateShiftScheduling function main catch block', err);
        let errResult = commonLib.func.getError('', 'SS0117');
        throw new ApolloError(errResult.message, errResult.code)
    } finally {
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
    }
}