// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName,systemLogs,defaultValues,awsSesTemplates } = require('../../../common/appconstants');
// require table alias
const { ehrTables } = require('../../../common/tablealias');
// require common function
const { ratingsPublishableEmpPerformanceDetails,getEmpPerformanceAvgRating,getPerformanceMonthYear,sendEmailToEmployees } = require('../../../common/performanceManagementCommonFunction');
// require pms validation function
const {employeeGoalsAndRatingInputValidation} = require('../../../common/performanceManagementValidation');

/**
 * Function to update the rating details for the performance ids.
*/
async function updateRatingDetails(orgCode,organizationDbConnection,empPerformanceDetails,loginEmpId,loginEmpCurrentDateTime,monthsYear){
    let performanceEmpId = empPerformanceDetails.Employee_Id;
    let performanceId = empPerformanceDetails.Performance_Assessment_Id;
    try{
        // call the function to get the average yearly rating for the employee
        let averageYearlyRating = await getEmpPerformanceAvgRating(orgCode,organizationDbConnection,performanceEmpId,monthsYear);

        // form the update ratings details
        let updateDetails = {
            'Rating_Publish_Status': defaultValues.goalsAndAchievementGoalPublishStatus,//Published
            'Average_Yearly_Rating': averageYearlyRating,
            'Updated_On': loginEmpCurrentDateTime,
            'Updated_By': loginEmpId
        };

        /** Update the ratings for the the performance id where rating status should be 'Unpublished' */
        return(organizationDbConnection(ehrTables.performanceGoalAchievement)
        .update(updateDetails)
        .where('Performance_Assessment_Id',performanceId)
        .where('Goal_Publish_Status', defaultValues.goalsAndAchievementGoalPublishStatus)
        .where('Rating_Publish_Status',defaultValues.goalsAndAchievementGoalUnPublishStatus)
        .then((updateResponse) => {
            if(updateResponse){
                return 'success'
            }else{
                console.log('Unable to publish ratings as the ratings are not in unpublished status or the ratings are already published for the performance assessment id,',performanceId);
                return 'EPM0024';
            }
        })
        .catch(updateCatchError => {
            console.log('Error occurred in the updateRatingDetails() function .catch block. ',updateCatchError,' for the employee id: ',performanceEmpId);
            return updateCatchError;
        }));
    }catch(updateMainCatchError){
        console.log('Error occurred in the updateRatingDetails() function main catch block. ',updateMainCatchError,' for the employee id: ',performanceEmpId);
        return updateMainCatchError;
    }
}

// publish the employee(s) ratings
module.exports.publishRatings = async (parent, args, context, info) => {
    console.log("Inside publishRatings() function.");
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const loginEmpId = context.Employee_Id;
        const orgCode = context.Org_Code;
        const userIp = context.User_Ip;

        // Check Goals and achievement form access rights.
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmpId, formName.goalsAndAchievement, '', 'UI');

        // If the login employee is admin or manager and having update access for the 'Goals and achievement' form
        if ((Object.keys(checkRights).length >0 && checkRights.Role_Update===1) && (checkRights.Is_Manager===1 || checkRights.Employee_Role==='admin')) {
            //Function to validate inputs
            validationError=await employeeGoalsAndRatingInputValidation(args,'validateMonthYear');
            if(Object.keys(validationError).length > 0 || (!(args.performanceAssessmentId) 
            || args.performanceAssessmentId.length === 0)){
                console.log('Validation error in the publish ratings function.',validationError);
                throw('_EC0007');
            }else{
                // get the login employee current date and time based on login employee location
                let loginEmpCurrentDateTime = await commonLib.func.getEmployeeTimeZone(loginEmpId, organizationDbConnection, 1);

                // call the function to get the rating performance details based on the goal and rating publish status, assessment month, assessment year, login employee-admin/manager role
                let empPerformanceDetails = await ratingsPublishableEmpPerformanceDetails(organizationDbConnection,loginEmpId,checkRights.Employee_Role,args);

                // if the performance assessment details exist
                if(empPerformanceDetails && empPerformanceDetails.length > 0){
                    // get all the performance assessment month and year for the performance year based on the given assessment period
                    let monthsYear = await getPerformanceMonthYear(orgCode,organizationDbConnection,'getseparatemonthsyear',args);

                    if(monthsYear && monthsYear.length > 0){
                        console.log('Response from the getPerformanceMonthYear() function. ',monthsYear);
                        let totalRecordCount = empPerformanceDetails.length;

                        let updateEmpPerformanceRatingsResponse;
                        let updateSuccessCount = 0,
                        notPublishedCount = 0;
                        let updatedPerformanceAssessmentIds = [];

                        // iterate the employee id to update the ratings details in the goals and achievement table
                        for(let empPerformance of empPerformanceDetails){
                            // call the function to update the ratings in the goals and achievement table
                            updateEmpPerformanceRatingsResponse = await updateRatingDetails(orgCode,organizationDbConnection,empPerformance,loginEmpId,loginEmpCurrentDateTime,monthsYear);

                            // if the performance and goals details are inserted
                            if(updateEmpPerformanceRatingsResponse === 'success'){
                                updateSuccessCount += 1;
                                updatedPerformanceAssessmentIds.push(empPerformance.Performance_Assessment_Id);
                            }else{
                                if(updateEmpPerformanceRatingsResponse === 'EPM0024'){
                                    notPublishedCount+= 1;
                                }
                                console.log('Ratings are not updated. Error response returned from the updateRatingDetails() function for the performance details,',empPerformance,' is ',updateEmpPerformanceRatingsResponse);
                            }
                        }

                        /** If any of the employee performance rating details are updated */
                        if(updateSuccessCount > 0){
                            let responseErrorCode;

                            // form inputs to update system log.
                            let systemLogParams = {
                                action: systemLogs.roleUpdate,
                                userIp: userIp,
                                employeeId: loginEmpId,
                                formName: formName.goalsAndAchievement,
                                trackingColumn: '',
                                organizationDbConnection: organizationDbConnection,
                                uniqueId: updatedPerformanceAssessmentIds
                            };

                            /** If the all the employee(s) - rating details are updated */
                            if(updateSuccessCount === totalRecordCount){
                                //Example Log message: 'Update Goals And Achievement - Publish Ratings for March 2021'
                                systemLogParams.trackingColumn = 'Publish Ratings for -';
                            }else{
                                responseErrorCode = 'EPM0117';//records are partially inserted
                                //Example Log message: 'Update Goals And Achievement - Publish Ratings partially for March 2021'
                                systemLogParams.trackingColumn = 'Publish Ratings partially for -';
                            }
                            let performanceMonthName = defaultValues.monthsArray[(args.month)-1];
                            // call function to add the system log
                            await commonLib.func.createSystemLogActivities(systemLogParams);

                            let sendEmailArgs = {
                                orgCode: context.Org_Code,
                                loginEmployeeId: loginEmpId,
                                performanceAssessmentIds: updatedPerformanceAssessmentIds,
                                templateName: awsSesTemplates.performanceRatingPublishedNotification,
                                emailContent: 'Your rating for the assigned goals are published for the month of '+performanceMonthName+' '+args.year,
                                emailSubject: 'Rating published for '+performanceMonthName+' '+args.year
                            };
                            //Send the ratings published email to the respective employees
                            await sendEmailToEmployees(organizationDbConnection,sendEmailArgs);

                            // destroy database connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;

                            /** If the records are updated partially */
                            if(responseErrorCode){
                                throw responseErrorCode;
                            }else{
                                return { errorCode: '', message: 'Ratings are published successfully.'};
                            }
                        }else{
                            console.log('Ratings are not published for any of the employees.');
                            throw (notPublishedCount > 0) ? 'EPM0024' : 'EPM0118';//throw ratings are not published
                        }
                    }else{
                        console.log('Empty Performance-month and year is returned from the getPerformanceMonthYear() function. ',monthsYear);
                        throw 'EPM0015';//throw error while publishing employee ratings
                    }
                }else{
                    console.log('No record exist to publish ratings as the login employee id is not the eligible approver or the goal publish status and rating publish status is not valid. ',empPerformanceDetails);
                    throw 'EPM0023';// throw no record found
                }
            }
        }else{
            throw('_DB0102');//throw employee does not have edit access
        }
    }catch(publishRatingsMainCatchErr) {
        console.log('Error in the publishRatings() function main catch block. ',publishRatingsMainCatchErr);
        // destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(publishRatingsMainCatchErr, 'EPM0015');
        throw new ApolloError(errResult.message,errResult.code);// return response
    }
};