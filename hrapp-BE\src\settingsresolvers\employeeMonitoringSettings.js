// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require common constant files
const constants = require('../../common/appconstants');
// Organization database connection
const knex = require('knex');
// require table alias
const tables = require('../../common/tablealias');

// variable declarations
let errResult = validationError = systemLogParams = validationError = {};
let organizationDbConnection =  '';
let ehrTables = tables.ehrTables;
let checkRights = false;

// resolver definition
const resolvers = {
    Query: {
        // function to get the employee monitoring settings data
        retrieveEmployeeMonitorSettings: async (parent, args, context, info) =>{
            try{
                let logInEmpId = context.Employee_Id;
                // get the organization data base connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // check if the call is from desktop application or web application
                // we will send isCallFromWebApp = 1 from web application only. Not desktop application
                if (args.isCallFromWebApp === 1){
                    // to check form view access rights
                    checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId, constants.formName.employeeMonitorSettings, constants.roles.roleView);
                }else{
                    // set default access rights is true
                    checkRights = true;
                }
                //check view rights
                if (checkRights === true) {
                    // get the employee monitor settings data from table
                    return(
                        organizationDbConnection
                        .transaction(function(trx){
                            return(
                                organizationDbConnection(ehrTables.employeeMonitorSettings)
                                .select('Employee_Monitor_Settings_Id', 'Capture_Screenshot', 
                                'Screenshot_Frequency','No_Of_Screenshots_Per_Frequency')
                                .transacting(trx)
                                .then(settingResponse =>{
                                    // check if record exists or not
                                    if (settingResponse && settingResponse[0]){
                                        // return response to UI
                                        return { errorCode: '', message: 'Employee Monitor setting retrieved successfully', settingsData: settingResponse[0]}
                                    }else{
                                        // throw error if record is doesnot exists
                                        throw ('_EC0001');
                                    }
                                    
                                })
                            )
                        })
                        .catch(function (retrieveEmployeeMonitorSettingsInsideCatchErr) {
                            console.log('Error in retrieveEmployeeMonitorSettings() function .catch block', retrieveEmployeeMonitorSettingsInsideCatchErr);
                            if (retrieveEmployeeMonitorSettingsInsideCatchErr === '_EC0001'){
                                errResult = commonLib.func.getError(retrieveEmployeeMonitorSettingsInsideCatchErr, '_EC0001');
                            }else{
                                errResult = commonLib.func.getError(retrieveEmployeeMonitorSettingsInsideCatchErr, 'SE0008');
                            }
                            throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, settingsData: {} }));
                        })
                        // close db connection
                        .finally(() => {
                            organizationDbConnection.destroy();
                        })
                    )
                } else if (checkRights === false) {
                    // throw error if view rights is not exists
                    throw ('_DB0100');
                } else {
                    // throw error
                    throw (checkRights);
                }
            } catch (retrieveEmployeeMonitorSettingsMainCatchErr){
                console.log('Error in retrieveEmployeeMonitorSettingsMainCatchErr() function main catch block',retrieveEmployeeMonitorSettingsMainCatchErr);
                // access denied error
                if (retrieveEmployeeMonitorSettingsMainCatchErr === '_DB0100') {
                    errResult = commonLib.func.getError('', '_DB0100');
                } else {
                    errResult = commonLib.func.getError('', 'SE0108');
                }
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                // return response
                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, settingsData: {} }));
            }
        }
    },
    Mutation:{
        //Update the settings configuration for Employee Monitoring
        updateEmployeeMonitorSettings: async (parent, args, context, info) => {
            try {
                let logInEmpId = context.Employee_Id;
                let { captureScreenshots , screenshotsFrequency,noOfScreenshotsPerFrequency } = args;
                // get the organization data base connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                // to check form view access rights
                checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId, constants.formName.employeeMonitorSettings, constants.roles.roleUpdate);
                //check view rights
                if (checkRights === true) {
                    // validate screenshotsFrequency
                    if (!(captureScreenshots === 1 || captureScreenshots === 0)) {
                        validationError['IVE0041'] = commonLib.func.getError('', 'IVE0041').message;
                    }
                    // based on the captureScreenshots validate the screenshotsFrequency and noOfScreenshotsPerFrequency
                    if (captureScreenshots === 1){
                        // validate screenshotsFrequency
                        if (!(screenshotsFrequency && screenshotsFrequency === 10 || screenshotsFrequency === 30)) {
                            validationError['IVE0042'] = commonLib.func.getError('', 'IVE0042').message; 
                        }
                        // validate screenshotsFrequency
                        if (!(noOfScreenshotsPerFrequency && noOfScreenshotsPerFrequency === 1 || noOfScreenshotsPerFrequency === 2 || noOfScreenshotsPerFrequency === 3)) {
                            validationError['IVE0043'] = commonLib.func.getError('', 'IVE0043').message;
                        }
                    }
                    // check validation error exists or not
                    if (Object.keys(validationError).length === 0) {
                        // update the setting data in the employee_monitor_settings table
                        return(
                            organizationDbConnection
                            .transaction(function(trx){
                                // check record exists or not
                                return(
                                    organizationDbConnection(ehrTables.employeeMonitorSettings)
                                    .select('Lock_Flag')
                                    .where('Employee_Monitor_Settings_Id', args.employeeMonitoringSettingsId)
                                    .transacting(trx)
                                    .then(async (settingsData) =>{
                                        // check settingsData exists or not
                                        if (settingsData && settingsData[0]){
                                            // check loock_Flag. Whether any of the employees are opened and edit this record
                                            if (settingsData[0].Lock_Flag === 0 || settingsData[0].Lock_Flag === logInEmpId){   

                                                // Call function getEmployeeTimeZone() to get current date and time based on employee time zone 
                                                // Send employeeId ,dbConnection and another flag as 1(This flag is to get the time zone date with time)
                                                let empTimeZone = await commonLib.func.getEmployeeTimeZone(logInEmpId, organizationDbConnection, 1);  
                                                // check type of empTimeZone if its string then send empTimeZone as it is
                                                empTimeZone = (typeof (empTimeZone) === 'string') ? empTimeZone : null;
                                                // update the setting data in the employee_monitor_settings table
                                                return (
                                                    organizationDbConnection(ehrTables.employeeMonitorSettings)
                                                    .update({
                                                        Capture_Screenshot: captureScreenshots,
                                                        Screenshot_Frequency: screenshotsFrequency,
                                                        No_Of_Screenshots_Per_Frequency: noOfScreenshotsPerFrequency,
                                                        Updated_By:logInEmpId,
                                                        Updated_On: empTimeZone
                                                    })
                                                    .where('Employee_Monitor_Settings_Id', args.employeeMonitoringSettingsId)
                                                    .then(async (updateSettings) => {
                                                        // form inputs to update system log activities commonly
                                                        systemLogParams = {
                                                            action: constants.systemLogs.roleUpdate,
                                                            userIp: context.userIp,
                                                            employeeId: logInEmpId,
                                                            formName: constants.formName.employeeMonitorSettings,
                                                            trackingColumn: 'Screenshots Configuration',
                                                            organizationDbConnection: organizationDbConnection,
                                                            uniqueId: args.employeeMonitoringSettingsId
                                                        }

                                                        // call function createSystemLogActivities() to update system log activities
                                                        await commonLib.func.createSystemLogActivities(systemLogParams);
                                                        // return response to UI
                                                        return { errorCode: '', message: 'Employee monitor settings updated successfully', validationError:'' }
                                                    })
                                                )
                                            }else{
                                                // throw error if lock flag is set already
                                                throw ('_EC0003');
                                            }
                                        }else{
                                            // throw error if record is doesnot exists
                                            throw ('_EC0001');
                                        }                                        
                                    })
                                )                                
                            })
                            .catch(function (updateEmployeeMonitorSettingsInsideCatchErr) {
                                console.log('Error in updateEmployeeMonitorSettings() function .catch block', updateEmployeeMonitorSettingsInsideCatchErr);
                                errResult = commonLib.func.getError(updateEmployeeMonitorSettingsInsideCatchErr, 'SE0009');
                                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, validationError:'' }));
                            })
                            // close db connection
                            .finally(() => {
                                organizationDbConnection.destroy();
                            })
                        )                        
                    }else{
                        // throw validation error
                        throw ('IVE0000');
                    }
                } else if (checkRights === false) {
                    // throw error if view rights is not exists
                    throw ('_DB0102');
                } else {
                    // throw error
                    throw (checkRights);
                }
            } catch (updateEmployeeMonitorSettingsMainCatchErr){
                console.log('Error in updateEmployeeMonitorSettings() function main catch block',updateEmployeeMonitorSettingsMainCatchErr);
                // access denied error
                if (updateEmployeeMonitorSettingsMainCatchErr === '_DB0102') {
                    errResult = commonLib.func.getError('', '_DB0102');
                } else if (updateEmployeeMonitorSettingsMainCatchErr === 'IVE0000') {
                    console.log('Validation error in updateEmployeeMonitorSettings()', validationError);
                    errResult = commonLib.func.getError('', 'IVE0000');
                    // return response
                    throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, validationError: validationError }));
                } else {
                    errResult = commonLib.func.getError('', 'SE0109');
                }
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                // return response
                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, validationError:'' }));
            }
        }
    }
};
exports.resolvers = resolvers;