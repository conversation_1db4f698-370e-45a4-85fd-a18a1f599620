'use strict';
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// Function to initiate triggerBatchEmailNotification step function
module.exports.triggerBatchEmailNotification  = async(event, context) =>{
    try{
        console.log('Inside triggerBatchEmailNotification function',event);
        //Form the step functions inputs
        const params = {
            stateMachineArn: process.env.stateMachineArn
        };
        let triggerStepFunctionResponse = await triggerStepFunction(params);
        console.log('Response after triggering triggerBatchEmailNotification step function',triggerStepFunctionResponse);

        return {errorCode:'',message: 'triggerBatchEmailNotification initiated successfully.'};
    }
    catch(mainCatchError){
        console.log('Error in triggerBatchEmailNotification function main catch block.', mainCatchError);
        let errResult = commonLib.func.getError(mainCatchError, 'EE00006');
        return {errorCode:errResult.code,message: errResult.message};
    }
};

//Function to trigger step functions
async function triggerStepFunction(params){
    try{
        //Require aws sdk
        const AWS = require('aws-sdk');
        const stepfunctions = new AWS.StepFunctions();

        //Trigger step function
        return new Promise(function(resolve, reject) {
            stepfunctions.startExecution(params, function (error, data) {
                if (error) {
                    console.log("Error in initiating step function in the triggerStepFunction function.",error)
                    resolve('');             
                }
                else{
                    console.log('Step function triggered successfully.');
                    resolve(data);
                }
            })
        })
    }
    catch(error){
        console.log('Error in triggerStepFunction function catch block',error);
        return '';
    }
}