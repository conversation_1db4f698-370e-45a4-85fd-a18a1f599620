// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require moment package
const moment = require('moment-timezone');
// require common constant files
const { formName } = require('../../../common/appconstants');
// require table alias
const { ehrTables } = require('../../../common/tablealias');
// require validation file
const {employeeGoalsAndRatingInputValidation} = require('../../../common/performanceManagementValidation');
// require common function
const {getPerformanceMonthYear,formCurrentPerformanceYear} = require('../../../common/performanceManagementCommonFunction');

// variable declarations
let errResult = {};
let organizationDbConnection = '';
let validationError={};

// resolver definition
const resolvers = {
    Query: {
        // function to retrieve the employee yearly performance ratings for the input month/year
        retrieveEmployeeYearlyRating: async (parent, args, context, info) => {
            try{
                console.log('Inside retrieveEmployeeYearlyRating function',args);
                // get the organization data base connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                let logInEmpId = context.Employee_Id;
                // check whether the loggedIn employee has manager/admin access to goals and achievement form or not
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.goalsAndAchievement, '', 'UI');
                if ((Object.keys(checkRights).length >0 && checkRights.Role_View===1) && (checkRights.Is_Manager===1 || checkRights.Employee_Role==='admin')) {
                    // function to validate the input field
                    validationError=await employeeGoalsAndRatingInputValidation(args,'validateMonthYearEmployee');
                    // check whether there is no input validation error
                    if(Object.keys(validationError).length ===0){
                        // variable declarations
                        let ratingArray=[];
                        let performanceMonthArray=[];
                        let response={};
                        // get the performance month and year based on the orgcode
                        let getPerformanceMonthYearResponse = await getPerformanceMonthYear(context.Org_Code,organizationDbConnection,'getseparatemonthsyear',args);
                        let performanceMonthLength=getPerformanceMonthYearResponse.length;
                        if(performanceMonthLength>0)
                        {
                            // iterate the loop for all the months in the performance year
                            for (let record of getPerformanceMonthYearResponse){
                                // function to get the overall rating for the input month and year
                                let employeeRatings=await getOverallRatingBasedOnInput(organizationDbConnection,record,args.employeeId);
                                // push the response in a separate array
                                ratingArray.push(employeeRatings);
                                // get the short form of month. //example - jan
                                performanceMonthArray.push(moment.monthsShort(record.month-1));
                            };
                            // form output response
                            response.ratingArray=ratingArray;
                            response.performanceMonthArray=performanceMonthArray;
                            // return performance month/year example: Jan 2021 - Dec 2021
                            response.performanceYear = await formCurrentPerformanceYear(getPerformanceMonthYearResponse[0].month,getPerformanceMonthYearResponse[0].year,(getPerformanceMonthYearResponse[performanceMonthLength-1].month),getPerformanceMonthYearResponse[performanceMonthLength-1].year);// performance year

                            // destroy database connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            // return response to UI
                            return {errorCode:'',message:'Employee performance of the year retrieved successfully.',employeeYearlyRatings:response};
                        }
                        else{
                            // destroy database connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            throw 'EPM0122';
                        }
                    }
                    else{
                        throw 'IVE0000';
                    }
                }
                else{
                    throw '_DB0100';
                }
            }catch(mainCatchError){
                console.log('Error in retrieveEmployeeYearlyRating function main catch block',mainCatchError);
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                if (mainCatchError === 'IVE0000') {
                    errResult = commonLib.func.getError('', 'IVE0000');
                    // return response
                    throw new UserInputError(errResult.message, { validationError: validationError });
                } else {
                    errResult = commonLib.func.getError(mainCatchError, 'EPM0019');
                    // return response
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        }
    }
};

exports.resolvers = resolvers;

// function to get overall rating based on input year/month
async function getOverallRatingBasedOnInput(organizationDbConnection,input,employeeId){
    try{
        return(
            organizationDbConnection(ehrTables.performanceGoalAchievement)
            .pluck('Overall_Rating')
            .where('Performance_Assessment_Month',input.month)
            .where('Performance_Assessment_Year',input.year)
            .where('Employee_Id',employeeId)
            .then(getData => {
                return (getData.length)?getData[0]:0;
            })
            .catch(catchError => {
                console.log('Error in getOverallRatingBasedOnInput function .catch block',catchError);
                return 0;
            })
        );
    }
    catch(error){
        console.log('Error in getOverallRatingBasedOnInput function main catch block',error);
        return 0;
    }
};
