// require table names
const { ehrTables } = require('../../../common/tablealias');
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// function to update weekoff details
async function updateWeekOffDetails(weekoffData, empTimeZone, loginId,orgDb){
    try{
        console.log('Inside updateWeekOffDetails() function',weekoffData);
        // variable declarations
        let { employeeId, shiftTypeId, shiftDate } = weekoffData;
        let errResult;
        
        // check employee valid or not
        if(!employeeId || employeeId === 0){
            errResult = commonLib.func.getError('', 'IVE0083');
            return { success: false, code:errResult.code, message: errResult.message };
        }
        // validate shift type
        if(!shiftTypeId || shiftTypeId === 0){
            errResult = commonLib.func.getError('', 'IVE0084');
            return { success: false, code: errResult.code, message: errResult.message };
        }
        // validate shiftDate
        if (!shiftDate || shiftDate === ""){
            errResult = commonLib.func.getError('', 'IVE0085');
            return { success: false, code: errResult.code, message: errResult.message };
        }

        // Check leave already exist for this date
        let isLeaveExists = await commonLib.employees.getLeaves(employeeId,shiftDate,shiftDate,'update-weekoff',orgDb);

        // check leave count
        if (isLeaveExists){
            console.log('Leave already exist for this date.');
            errResult = commonLib.func.getError('', 'SS0144');
            return { success: false, code: errResult.code, message: errResult.message };
        }

        // check compensatory_off exist for this
        let isCompensatoryOffExists = await commonLib.employees.getCompensatoryOff(employeeId, shiftDate, shiftDate, 'update-weekoff',orgDb);

        // check compOffCount exist or not
        if (isCompensatoryOffExists) {
            console.log('Compensatory Off already exist for this date.');
            errResult = commonLib.func.getError('', 'SS0146');
            return { success: false, code: errResult.code, message: errResult.message };
        }

        // check attendance exist or not
        let getAttendanceDetails = await commonLib.employees.getAttendance(shiftDate,employeeId,orgDb);
        if(getAttendanceDetails.errorCode){
            console.log('Response from the getAttendance function',getAttendanceDetails);
            errResult = commonLib.func.getError('', getAttendanceDetails.errorCode);
            return { success: false, code: errResult.code, message: errResult.message };
        }else{
            let isAttendanceExists = getAttendanceDetails.attendanceDetails;
            // check attendance exist or not
            if (isAttendanceExists) {
                console.log('Attendance already exist for this date.');
                errResult = commonLib.func.getError('', 'SS0148');
                return { success: false, code: errResult.code, message: errResult.message };
            }
        }

        let validateHolidayOverrideResponse = await validateWeekOffApplicableOnHoliday(orgDb,employeeId,shiftTypeId,shiftDate);
        
        /** If the week off is not allowed to apply for the shift date which falls on holiday */
        if(validateHolidayOverrideResponse.isWeekOffApplicable){
            // check weekoff already updated or not
            let isWeekOffRecordExists = await orgDb(ehrTables.shiftEmpMapping)
                                        .select('Shift_Schedule_Id','Week_Off')
                                        .where('Employee_Id', employeeId)
                                        .where('Shift_Type_Id', shiftTypeId)
                                        .where('Shift_Start_Date', shiftDate)
                                        .where('Shift_End_Date', shiftDate);

            // Check record exists or not
            if (isWeekOffRecordExists.length === 0){
                console.log('Shift does not scheduled for this date.');
                errResult = commonLib.func.getError('', 'SS0145');
                return { success: false, code: errResult.code, message: errResult.message };
            }
            // check weekoff already updated or not
            if (isWeekOffRecordExists[0] && isWeekOffRecordExists[0].Week_Off === 1) {
                console.log('Week off already added for this date.');
                errResult = commonLib.func.getError('', 'SS0143');
                return { success: false, code: errResult.code, message: errResult.message };
            }

            // update weekoff details. If shift is scheduled for the specified weekoff date
            let updateResponse = await orgDb(ehrTables.shiftEmpMapping)
                                .update({
                                    Week_Off: 1,
                                    Updated_On: empTimeZone,
                                    Updated_By: loginId
                                })
                                .where('Employee_Id', employeeId)
                                .where('Shift_Type_Id', shiftTypeId)
                                .where('Shift_Start_Date', shiftDate)
                                .where('Shift_End_Date', shiftDate)
                                .then(updateWeekOffDetails =>{
                                    // check update is success or failure
                                    if(updateWeekOffDetails){
                                        console.log('Week off updated successfully.')
                                        return { success: true, code: '', message : ''};
                                    }else{
                                        console.log('Error while updatig the weekoff details');
                                        let errResult = commonLib.func.getError('', 'SS0143');
                                        return { success: false, code: errResult.code, message: errResult.message };
                                    }
                                })
                                .catch(weekOffUpdateError =>{
                                    console.log('Error while updating the week off in updateWeekOffDetails() function .catch block.', weekOffUpdateError);
                                    let errResult = commonLib.func.getError('', 'SS0002');
                                    return { success: false, code: errResult.code, message: errResult.message };
                                });
            // return response back to function
            return updateResponse;
        }else{
            console.log('This date cannot be scheduled with the week off. Response from the validateHolidayOverrideeResponse() function.',validateHolidayOverrideResponse);
            errResult = commonLib.func.getError('', 'SS0147');
            return { success: false, code:errResult.code, message: errResult.message };
        }
    }catch(updateWeekOffDetailsError){
        console.log('Error in updateWeekOffDetails() function catch block.', updateWeekOffDetailsError);
        let errResult = commonLib.func.getError(updateWeekOffDetailsError, 'SS0142');
        return { success: false, code: errResult.code, message: errResult.message };
    }
};
module.exports.updateWeekOffDetails = updateWeekOffDetails;
// function to get shift details based on start and end date
async function getShiftDetails(employeeId, Salary_Date, Last_SalaryDate,orgDb) {
    return (
        orgDb
        .select('SEM.Shift_Schedule_Id', 'SEM.Employee_Id', 'SEM.Shift_Type_Id', 'SEM.Shift_Start_Date as Shift_Date',
        'SEM.Week_Off', 'ST.Colour_Code', 'WS.Title as Shift_Type')
        .from(ehrTables.shiftEmpMapping + ' as SEM')
        .leftJoin(ehrTables.empShiftType + ' as ST', 'SEM.Shift_Type_Id', 'ST.Shift_Type_Id')
        .leftJoin(ehrTables.workSchedule + ' as WS', 'ST.WorkSchedule_Id', 'WS.WorkSchedule_Id')
        .where('SEM.Employee_Id', employeeId)
        .where((qb) => {
            if (Salary_Date && Last_SalaryDate) {
                qb.whereBetween('SEM.Shift_Start_Date', [Salary_Date, Last_SalaryDate])
                qb.orWhereBetween('SEM.Shift_End_Date', [Salary_Date, Last_SalaryDate])
            }
        })
        .then(shiftData => {
            return shiftData;
        })
        .catch(getShiftDetailsError => {
            console.log('Error in getShiftDetails() function .catch block.', getShiftDetailsError);
            return getShiftDetailsError;
        })
    );
};
module.exports.getShiftDetails = getShiftDetails;

/**
 * Validate the week off can be provided or not for the shift date which falls on holiday based on the shift type.
 * @param {Object} orgDb - Organization Database connection object
 * @param {Number} employeeId - Shift assigned employee id
 * @param {Number} shiftTypeId - Shift type id selected for scheduling the shift 
 * @param {Date} shiftDate - Date on which the shift type is scheduled
 * @returns {Object} - Return response object with the params - isWeekOffApplicable in try block. If isWeekOffApplicable is 1 then 
 * week off can be allowed to apply for the shift date and the shift type id. If isWeekOffApplicable is 0 then week off is not allowed
 * to apply for the shift date and the shift type id.
 * @throws {Object} - Throws an error when an error occured.
 */
async function validateWeekOffApplicableOnHoliday(orgDb,employeeId,shiftTypeId,shiftDate){
    try{
        let validateWeekOffApplicableOnHolidayResponse = {
            isWeekOffApplicable: 0
        };

        // Get the Holiday_Override flag for the shift type in the shift type table
        let isHolidayOverride = await orgDb.select('Holiday_Override')
        .from(ehrTables.empShiftType)
        .where('Shift_Type_Id', shiftTypeId)
        .first()
        .then(isExist=>{
            return isExist.Holiday_Override ? isExist.Holiday_Override : 0;
        })
        .catch(holidayOverrideError => {
            console.log('Error while getting the Holiday_Override flag in validateWeekOffApplicableOnHoliday() function in .catch block.', holidayOverrideError);
            return 0;
        });

        // If Holiday_Override is 0 the we should not allow to give week off on holidays.
        if (isHolidayOverride === 0){
            //Get all the holiday details for the shift date
            let allHolidayDetails= await commonLib.shiftAndTimeManagement.getAllHolidayDates(orgDb, shiftDate, shiftDate);
            let employeeIdArray = Array.isArray(employeeId) ? employeeId : [employeeId];
            for (let empId of employeeIdArray){
                //Get the employee holiday details
                let employeeHolidayDetails = allHolidayDetails[empId] || null;
                //Holiday for the shift date
                let holidayExist = employeeHolidayDetails?.[shiftDate] || null;
                //If the shift date is holiday then week off is not applicable
                if (holidayExist !== null){
                    validateWeekOffApplicableOnHolidayResponse.isWeekOffApplicable = 0;
                    break;
                }else{
                    validateWeekOffApplicableOnHolidayResponse.isWeekOffApplicable = 1;
                }
            }
        }else{
            //If the holiday can be overrided then week off can be applicable for all the days including the holiday
            validateWeekOffApplicableOnHolidayResponse.isWeekOffApplicable = 1;
        }
        return validateWeekOffApplicableOnHolidayResponse;
    }catch(validateWeekOffApplicableOnHolidayError){
        console.log('Error in the validateWeekOffApplicableOnHoliday() function in the main catch block.',validateWeekOffApplicableOnHolidayError);
        throw validateWeekOffApplicableOnHolidayError;
    }
}
module.exports.validateWeekOffApplicableOnHoliday = validateWeekOffApplicableOnHoliday;

// Return Employee Ids if employee already have shift for given date or overlapped current shift time with existing shift
async function getShiftDateExistEmpId(orgDb, employeeIds, shiftStartDate, shiftEndDate, shiftTypeId, shiftScheduleId = null) {
    let shiftExistsEmpIds = [];
    let shiftOverlapEmpIds = [];
    let overlapErrorMessage = '';
    let shiftExistsEmployeeDetails = [];
    try {
        if (employeeIds.length > 0) {
            let shiftExistsData = await orgDb.select("Employee_Id",'Shift_Start_Date')
                .from(ehrTables.shiftEmpMapping)
                .where((qb) => {
                    if (shiftScheduleId) {
                        qb.whereNot("Shift_Schedule_Id", shiftScheduleId);
                    }
                })
                .whereIn('Employee_Id', employeeIds)
                .andWhere((qb1) => {
                    qb1.where((qb2) => {
                        qb2.whereBetween('Shift_Start_Date', [shiftStartDate, shiftEndDate])
                        qb2.orWhereBetween('Shift_End_Date', [shiftStartDate, shiftEndDate])
                    })
                    qb1.orWhere((qb3) => {
                        qb3.where('Shift_Start_Date', '<', shiftStartDate);
                        qb3.where('Shift_End_Date', '>', shiftEndDate)
                    })
                })
                .then((res) => {
                    return res;
                })
            shiftExistsEmployeeDetails = shiftExistsData;
            for (let arr of shiftExistsData) {
                shiftExistsEmpIds.push(arr.Employee_Id)
            }

            let validEmpIds = employeeIds.filter(x => !shiftExistsEmpIds.includes(x));

            if (shiftTypeId) {
                let currentShiftStartDetails = await commonLib.employees.getGraceTimeDetails(null, shiftTypeId, shiftStartDate, orgDb);

                let currentShiftEndDetails = await commonLib.employees.getGraceTimeDetails(null, shiftTypeId, shiftEndDate, orgDb);

                for (empId of validEmpIds) {
                    if (currentShiftStartDetails.considerationFrom && currentShiftEndDetails.considerationTo) {
                        let lastShiftEndDate = await orgDb(ehrTables.shiftEmpMapping)
                            .max("Shift_End_Date as maxEndDate")
                            .where("Employee_Id", empId)
                            .where("Shift_End_Date", "<", shiftStartDate)
                            .where((qb) => {
                                if (shiftScheduleId) {
                                    qb.whereNot("Shift_Schedule_Id", shiftScheduleId);
                                }
                            })
                            .then((res) => {
                                return res;
                            })
                        lastShiftEndDate = lastShiftEndDate[0] ? lastShiftEndDate[0].maxEndDate : null;

                        let lastShiftId = await orgDb.select("Shift_Type_Id")
                            .from(ehrTables.shiftEmpMapping)
                            .where("Employee_Id", empId)
                            .where("Shift_End_Date", lastShiftEndDate)
                            .then((res) => {
                                return res;
                            })

                        // Get overlap shift scheduling flag from settings
                        let shiftSettings = await orgDb(ehrTables.rosterSettings).select('Overlap_Shift_Schedule').first();

                        lastShiftId = lastShiftId[0] ? lastShiftId[0].Shift_Type_Id : null;

                        if (lastShiftEndDate && lastShiftId) {

                            let lastShiftDetails = await commonLib.employees.getGraceTimeDetails(null, lastShiftId, lastShiftEndDate, orgDb);

                            // check Overlap_Shift_Schedule flag enabled or not
                            if (shiftSettings.Overlap_Shift_Schedule === 0) {
                                if (lastShiftDetails.considerationTo) {
                                    if (lastShiftDetails.considerationTo > currentShiftStartDetails.considerationFrom) {
                                        overlapErrorMessage += `The last shift's business hours end time (${lastShiftDetails.considerationTo}) overlaps with the current shift's business hours start time (${currentShiftStartDetails.considerationFrom}). `;
                                        shiftOverlapEmpIds.push(empId);
                                        continue;
                                    }
                                } else {
                                    shiftOverlapEmpIds.push(empId);
                                    overlapErrorMessage += `The last shift's business hours end time is missing and cannot be validated.`;
                                    continue;
                                }
                            } else {
                                // check regularTo exist or not
                                if (lastShiftDetails.regularTo) {
                                    /** We should not allow to overlap the current shift considerationFrom date-time and the last day shift regularTo date-time. If the last day 
                                     * shift regularTo date-time is greater than the current shift considerationFrom date-time then it is a overlapping shift. In this condition 
                                     * there is no need to check equal to condition because for the overlapping shifts the current shift considerationTo will be always the
                                     * next shift considerationFrom date-time - 1minute.
                                    */
                                    if (lastShiftDetails.regularTo > currentShiftStartDetails.considerationFrom) {
                                        overlapErrorMessage += `The last shift's regular hours end time (${lastShiftDetails.regularTo}) overlaps with the current shift's business hours start time (${currentShiftStartDetails.considerationFrom}). `;
                                        shiftOverlapEmpIds.push(empId);
                                        continue;
                                    }
                                } else {
                                    shiftOverlapEmpIds.push(empId);
                                    overlapErrorMessage += `The last shift's regular hours end time is missing and cannot be validated. `;
                                    continue;
                                }
                            }
                        }

                        let futureShiftStartDate = await orgDb(ehrTables.shiftEmpMapping)
                            .min("Shift_Start_Date as minStartDate")
                            .where("Employee_Id", empId)
                            .where("Shift_Start_Date", ">", shiftEndDate)
                            .where((qb) => {
                                if (shiftScheduleId) {
                                    qb.whereNot("Shift_Schedule_Id", shiftScheduleId);
                                }
                            })
                            .then((res) => {
                                return res;
                            })
                        futureShiftStartDate = futureShiftStartDate[0] ? futureShiftStartDate[0].minStartDate : null;

                        let futureShiftId = await orgDb.select("Shift_Type_Id")
                            .from(ehrTables.shiftEmpMapping)
                            .where("Employee_Id", empId)
                            .where("Shift_Start_Date", futureShiftStartDate)
                            .then((res) => {
                                return res;
                            })

                        futureShiftId = futureShiftId[0] ? futureShiftId[0].Shift_Type_Id : null;
                        if (futureShiftStartDate && futureShiftId) {

                            let futureShiftDetails = await commonLib.employees.getGraceTimeDetails(null, futureShiftId, futureShiftStartDate, orgDb);

                            // check Overlap_Shift_Schedule flag enabled or not
                            if (shiftSettings.Overlap_Shift_Schedule === 0) {
                                if (futureShiftDetails.considerationFrom) {

                                    if (futureShiftDetails.considerationFrom < currentShiftEndDetails.considerationTo) {
                                        shiftOverlapEmpIds.push(empId);
                                        overlapErrorMessage += `The future shift's business hours start time (${futureShiftDetails.considerationFrom}) overlaps with the current shift's business hours end time (${currentShiftEndDetails.considerationTo}). `;
                                    }
                                } else {
                                    shiftOverlapEmpIds.push(empId);
                                    overlapErrorMessage += `The future shift's business hours start time is missing and cannot be validated. `;
                                }
                            } else {

                                // check regularFrom exist or not
                                if (futureShiftDetails.regularFrom) {
                                    /** We should not allow to overlap the next day shift considerationFrom date-time and the current shift regularTo date-time. If the next day 
                                     * shift considerationFrom date-time is less than the current shift regularTo date-time then it is a overlapping shift. In this condition 
                                     * there is no need to check "equal to" condition because for the overlapping shifts the current shift considerationTo will be always the
                                     * next shift considerationFrom date-time - 1minute.
                                    */
                                    if (futureShiftDetails.considerationFrom < currentShiftStartDetails.regularTo) {
                                        shiftOverlapEmpIds.push(empId);
                                        overlapErrorMessage += `The future shift's business hours start time (${futureShiftDetails.considerationFrom}) overlaps with the current shift's regular hours end time (${currentShiftStartDetails.regularTo}). `;
                                        continue;
                                    }
                                } else {
                                    shiftOverlapEmpIds.push(empId);
                                    overlapErrorMessage += `The future shift's business hours start time is missing and cannot be validated. `;
                                    continue;
                                }
                            }
                        }
                    } else {
                        shiftOverlapEmpIds.push(empId);
                        overlapErrorMessage += `The current shift's business hours start time and end time is missing and cannot be validated. `;
                    }
                }
            }

        }
        return {
            shiftExistsEmpIds,
            shiftOverlapEmpIds,
            overlapErrorMessage,
            shiftExistsEmployeeDetails
        }
    } catch (err) {
        console.log("Error in getShiftDateExistEmpId ", err)
        return {
            shiftExistsEmpIds,
            shiftOverlapEmpIds,
            overlapErrorMessage,
            shiftExistsEmployeeDetails
        }
    }
}
module.exports.getShiftDateExistEmpId = getShiftDateExistEmpId;
