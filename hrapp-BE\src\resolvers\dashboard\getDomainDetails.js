// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require table alias list
const tables = require('../../../common/tablealias');

// variable declaration
let ehrTables = tables.ehrTables;
let appManagerConnection='';

// resolver definition
const resolvers = {
    Query: {
        // function to get the domain details for presenting info in footer
        getDomainDetails: async (parent, args, context, info) => {
            try{   
                // Make appmanager database connection
                appManagerConnection = knex(context.connection.AppManagerDb);
                
                 return(
                    appManagerConnection
                    .transaction(function(trx){
                        // retrieve the domain details from domainSettings table
                        return(
                            appManagerConnection(ehrTables.domainSettings)
                            .select('Support_Email as supportEmail', 'Copy_Right as copyRight','Support_Link as supportLink',
                            'Terms_Link as termsLink','Privacy_Policy_Link as privacyPolicyLink','Chat_Bot as chatBot','Chargebee_Site_Name as paymentPortalSiteName')
                            .transacting(trx)
                            .then(async(getDomainData) => { 
                                // check whether data exist or not
                                let domainDetails=(getDomainData.length>0)?getDomainData[0]:{};
                                // function to get orgname if licensing enabled
                                const licensingDetails = await enableLicensing(appManagerConnection,context.Org_Code);
                                // error will be handled in catch block
                                // return success response
                                return { errorCode: '', message: 'Domain details retrieved successfully',domainDetails : domainDetails,licensingDetails: licensingDetails }
                            })
                            // catch the errors
                            .catch(function (err) {
                                console.log('Error in retrieve domain details',err);
                                throw 'DB0002';
                            })                     
                        );
                    })
                    .then(function (result) {
                        return result;
                    })
                    //catch db-connectivity errors
                    .catch(function (catchErrror) {
                        console.log('Error in retrieve domain details block', catchErrror);
                        // get the code and message from common function based on returned error code
                        let errResult = commonLib.func.getError(catchErrror, 'DB0002');
                        // return response
                        throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, domainDetails : {} , licensingDetails: ''}));
                    })
                    /**close db connection */
                    .finally(() => {
                        appManagerConnection.destroy();
                    })
                );
            } catch (mainCatchError) {
                console.log('Error in get domain details function main catch block',mainCatchError);
                // destroy database connection
                appManagerConnection ? appManagerConnection.destroy() : null;
                // get the code and message from common function based on returned error code
                let errResult = commonLib.func.getError(mainCatchError, 'DB0002');
                // return response
                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, domainDetails : {} , licensingDetails: ''}));
            }
        }
    }

};


// Function to get the licensing details based on orgcode
// If any error in this function we will send response as success. 
//So that remaining information present in UI
async function enableLicensing(appManagerConnection,orgCode)
{
    return new Promise(function (resolve, reject) {
        return(
            appManagerConnection
            .transaction(function(trx){
                return(
                    // get the sourceId from hrapp RegisterUser table
                    appManagerConnection(ehrTables.hrappRegisterUser)
                    .select('Source_Id as sourceId')
                    .where('Org_Code',orgCode)
                    .transacting(trx)
                    .then((getId) => {
                        if(getId.length>0)
                        {
                            let sourceId=getId[0].sourceId;
                            // Get the organization name if enable licensing is enabled
                            return(
                                appManagerConnection(ehrTables.hrappLogin)
                                .select('Organization_Name')
                                .where('User_Id',sourceId)
                                .andWhere('Enable_Licensing',1)
                                .transacting(trx)
                                .then((getOrgName) => {
                                    // return organization name
                                    let licensingOrgName= (getOrgName.length>0)?getOrgName[0].Organization_Name:'';
                                    resolve(licensingOrgName);
                                })
                            )
                        }
                        else{
                            console.log('Error in retrieve sourceId');
                            resolve('');
                        }
                    })
                    .catch(function (err) {
                        console.log('Error in retrieve domain details',err);
                        resolve('');
                    }) 
                )
                .then(function (result) {
                    resolve(result);
                })
                //catch db-connectivity errors
                .catch(function (Error) {
                    console.log('Error in retrieve licensing details', Error);
                    resolve('');
                })
            })
        );
    });
};

exports.resolvers = resolvers;
