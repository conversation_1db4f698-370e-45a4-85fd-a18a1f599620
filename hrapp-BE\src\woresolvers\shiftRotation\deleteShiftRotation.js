// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formIds, systemLogs } = require('../../../common/appconstants');

//function to delete the shift rotation
let organizationDbConnection, appManagerDbConnection;
module.exports.deleteShiftRotation = async (parent, args, context, info) => {
    try {
        console.log("Inside deleteShiftRotation function.")
        let employeeId = context.Employee_Id;
        appManagerDbConnection = knex(context.connection.AppManagerDb);
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, null, null, 'ui', null, formIds.shiftRotation, { appManagerDbConnection: appManagerDbConnection, orgCode: context.Org_Code });
        if (!checkRights || !checkRights.Role_Delete) {
            throw '_DB0103';
        }

        // Check if the Rotation_Id exists
        let existingRecord = await organizationDbConnection(ehrTables.shiftRotation)
            .select('*')
            .where('Rotation_Id', args.Rotation_Id)
            .first();

        if (!existingRecord) {
            throw 'SS0105'
        }

        //Validate association
        await validateShiftRotationAssociation(organizationDbConnection, args.Rotation_Id)

        await organizationDbConnection.transaction(async (trx) => {
            await Promise.all([
                //Delete shift rotation
                organizationDbConnection(ehrTables.shiftRotation).where('Rotation_Id', args.Rotation_Id).del().transacting(trx),
                organizationDbConnection(ehrTables.shiftRotationSchedule).where('Rotation_Id', args.Rotation_Id).del().transacting(trx),
            ])
        })

        // Add System Log
        let systemLogParams = {
            action: systemLogs.roleDelete,
            userIp: context.User_Ip,
            employeeId: employeeId,
            formName: checkRights.Custom_Form_Name,
            trackingColumn: 'Scheduler_Name',
            organizationDbConnection: organizationDbConnection,
            uniqueId: existingRecord.Rotation_Id,
        };

        //Call function to add the system log
        await commonLib.func.createSystemLogActivities(systemLogParams);

        return { errorCode: "", message: `${checkRights.Custom_Form_Name} deleted successfully.` };

    }
    catch (e) {
        console.log('Error in deleteShiftRotation function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'SS0006');
        throw new ApolloError(errResult.message, errResult.code);
    }
    finally {
        //Destroy DB connection
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
}

async function validateShiftRotationAssociation(organizationDbConnection, rotationId) {
    try {
        let shiftEmpMapping = await organizationDbConnection(ehrTables.shiftEmpMapping)
            .select('Rotation_Id')
            .where('Rotation_Id', rotationId)

        if (shiftEmpMapping && shiftEmpMapping.length) {
            throw 'SS0107'
        }
    }
    catch (err) {
        console.log('Error in validateShiftRotationAssociation', err)
        throw err
    }
}