module.exports.getTeamLeaveHistory  = async(parent, args, context, info) => {
    console.log("Inside getTeamLeaveHistory() function.");
    //Require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    //Require knex for database connection
    const knex = require('knex');
    //Require common constant files
    const { formName } = require('../../../common/appconstants');
    const { ehrTables } = require('../../../common/tablealias');

    let organizationDbConnection;
    let errResult;
    let loginEmpId;
    let orgCode;
    try{
        loginEmpId = context.Employee_Id;
        orgCode=context.Org_Code;
        
        //Get the organization database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);
        
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmpId, formName.leaves, '', 'UI');
    
        if (Object.keys(checkRights).length >0 && checkRights.Role_View===1){
            let isAdmin = 0;
            //Check login employee is  admin or not
            if (checkRights.Employee_Role.toLowerCase() === 'admin') {
                isAdmin = 1;
            }
            let empDetailsSubQuery;
            if (isAdmin === 1) {
                //If the login employee is admin return all the employees
                empDetailsSubQuery = organizationDbConnection(ehrTables.empPersonalInfo + ' as EP')
                                    .pluck('EP.Employee_Id')
                                    .innerJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EP.Employee_Id')
                                    .whereIn('EJ.Emp_Status', ['Active', 'InActive'])
                                    .orderBy('EP.Employee_Id','ASC');
            } else {
                //If the login employee is not admin/super admin then return only their record and if they are manager then return their subordinate employees
                empDetailsSubQuery = organizationDbConnection(ehrTables.empPersonalInfo + ' as EP')
                                .pluck('EP.Employee_Id')
                                .innerJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EP.Employee_Id')
                                .where((qb)=>{
                                    qb.where('EJ.Manager_Id', loginEmpId)
                                    qb.orWhere('EP.Employee_Id', loginEmpId)
                                })
                                .whereIn('EJ.Emp_Status', ['Active', 'InActive'])
                                .orderBy('EP.Employee_Id','ASC');
            }
            
            //Get the employee ids
            let employeeIds = await empDetailsSubQuery.then(empResponse => {
                return empResponse;
            });

            if(employeeIds && employeeIds.length > 0){
                let teamLeaveHistory = await commonLib.employees.getTeamLeaveHistory(organizationDbConnection, loginEmpId, employeeIds,orgCode);
                //Destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;

                return {
                    errorCode: null,
                    message: "Leave history retrieved successfully",
                    leaveHistory: (teamLeaveHistory && teamLeaveHistory.length >0) ? JSON.stringify(teamLeaveHistory) :''
                }
            }else{
                console.log('Login employee does not have any subordinate employees.',employeeIds,orgCode,loginEmpId);
                throw '_EC0001';
            }
        }else{
            console.log('Employee does not have view access.',checkRights,orgCode,loginEmpId);
            throw '_DB0100';// throw employee does not have view access
        }
    }
    catch(teamLeaveHistoryMainErr)
    {
        console.log('Error in getTeamLeaveHistory() function main catch block.',teamLeaveHistoryMainErr,orgCode,loginEmpId);
        //Destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(teamLeaveHistoryMainErr, 'ELE00101');
        throw new ApolloError(errResult.message,errResult.code);
    }
}