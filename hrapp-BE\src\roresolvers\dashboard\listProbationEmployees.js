//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make database connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError,ApolloError } = require('apollo-server-lambda');
//Require common validation
const { booleanNumberValidation } =  require('../../../common/commonvalidation');
//Require moment package
const moment=require('moment');
//Require common function
const {getProbationEmployeesList} = require('../../../common/remindersCommonFunctions');

//List the probation employees in the team dashboard
module.exports.listProbationEmployees = async (parent, args, context, info) => {
    console.log("Inside listProbationEmployees() function.");
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    try{
        organizationDbConnection = knex(context.connection.OrganizationDb);
        
        if(!booleanNumberValidation(args.isTeamDashboard)){
            validationError['IVE0207'] = commonLib.func.getError('', 'IVE0207').message;
        }

        if(Object.keys(validationError).length === 0){
            let loginEmployeeId = context.Employee_Id;
            let todayDate = moment().format('YYYY-MM-DD');
            let probationEmployeesDetails = await getProbationEmployeesList(organizationDbConnection,args.isTeamDashboard,loginEmployeeId,0,todayDate);
            //Destroy database connection
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return {errorCode:'',message:'Probation employees list retrieved successfully.',probationEmployeesDetails:probationEmployeesDetails};
        }else{
            throw 'IVE0000';// throw validation error
        }
    }catch(mainCatchError) {
        console.log('Error in the listProbationEmployees() function main catch block. ',mainCatchError);
        //Destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            console.log('Validation error in the listProbationEmployees() function. ',validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            //Return error response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else{
            errResult = commonLib.func.getError(mainCatchError, 'EE00001');
            //Return error response
            throw new ApolloError(errResult.message,errResult.code);
        }
    }
};