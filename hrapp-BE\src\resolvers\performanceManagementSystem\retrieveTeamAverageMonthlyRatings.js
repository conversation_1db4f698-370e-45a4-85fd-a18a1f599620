// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
// require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../../common/appconstants');
// require table alias
const {ehrTables} = require('../../../common/tablealias');
// require pms validation function
const {employeeGoalsAndRatingInputValidation} = require('../../../common/performanceManagementValidation');
// require common function
const { getPerformanceMonthYear,formCurrentPerformanceYear } = require('../../../common/performanceManagementCommonFunction');
// require moment package
const moment = require('moment-timezone');

// list the team average yearly ratings
module.exports.retrieveTeamAverageMonthlyRatings = async (parent, args, context, info) => {
    console.log("Inside retrieveTeamAverageMonthlyRatings() function.");
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const orgCode = context.Org_Code;

        // Check Goals and achievement form access rights.
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, context.Employee_Id, formName.goalsAndAchievement, '', 'UI');

        // If the login employee is admin or manager and having view access for the 'Goals and achievement' form
        if ((Object.keys(checkRights).length >0 && checkRights.Role_View===1) && (checkRights.Employee_Role==='admin')) {
            // function to validate inputs
            validationError=await employeeGoalsAndRatingInputValidation(args,'validateMonthYear');
            // check the input validation error exist or not
            if(Object.keys(validationError).length ===0){
                // get the months and year for the given performance year
                let performanceYearMonths = await getPerformanceMonthYear(orgCode,organizationDbConnection,'getseparatemonthsyear',args);

                let performanceMonthLength=performanceYearMonths.length;
                if(performanceMonthLength > 0){
                    // average overall rating query
                    let overallRatingQry = organizationDbConnection(ehrTables.performanceGoalAchievement)
                    .select(organizationDbConnection.raw("AVG(Overall_Rating) as Average_Monthly_Rating"),'Performance_Assessment_Month');

                    // iterate all the performance month and year to form the where condition to get the average rating
                    for(let inputMonthYear of performanceYearMonths){
                        overallRatingQry = overallRatingQry.orWhere(qb => {
                            qb.where('Performance_Assessment_Month', inputMonthYear.month)
                            qb.where('Performance_Assessment_Year', inputMonthYear.year)
                        });
                    }

                    overallRatingQry = overallRatingQry.groupBy('Performance_Assessment_Month','Performance_Assessment_Year');//find the average rating by performance month

                    // get the average yearly rating for each month in a given performance year
                    let teamMonthlyAverageRatingsResponse  = await overallRatingQry.then(async(averageRatingDetail) =>{
                        let averageMonthlyRatingArray = [], performanceMonthsArray = [];
                        let inputMonthAverageRating;
                        let response={};

                        // iterate all the performance month and year to form the response
                        for(let inputMonthYear of performanceYearMonths){
                            inputMonthAverageRating = [];
                            // push the performance months from the actual performance start month to end month in the month format. Example: JAN
                            performanceMonthsArray.push(moment.monthsShort((inputMonthYear.month) - 1));

                            // find the average monthly rating for the given performance month from the overall months
                            inputMonthAverageRating = await getInputMonthAverageRating(averageRatingDetail,inputMonthYear);
                            
                            // if average rating exist for the performance month
                            if(inputMonthAverageRating.length > 0){
                                inputMonthAverageRating = inputMonthAverageRating[0];
                                // if average rating JSON exist for the performance month then push the average rating of the performance month in an array
                                if(Object.keys(inputMonthAverageRating).length > 0){
                                    averageMonthlyRatingArray.push(inputMonthAverageRating.Average_Monthly_Rating ? (inputMonthAverageRating.Average_Monthly_Rating.toFixed(1)) : 0);
                                }else{
                                    averageMonthlyRatingArray.push(0);
                                }
                            }else{
                                averageMonthlyRatingArray.push(0);   
                            }
                        }        
                        response.performanceMonths = performanceMonthsArray;// performance months
                        response.averageMonthlyRatings = averageMonthlyRatingArray;// monthly average rating
                        // return performance month/year example: Jan 2021 - Dec 2021
                        response.performanceYear = await formCurrentPerformanceYear(performanceYearMonths[0].month,performanceYearMonths[0].year,(performanceYearMonths[performanceMonthLength-1].month),performanceYearMonths[performanceMonthLength-1].year);// performance year

                        return response;
                    }).catch(overallRatingCatchError =>{
                        console.log('Error in the retrieveTeamAverageMonthlyRatings() .catch block',overallRatingCatchError);
                        throw 'EPM0123';// throw process error
                    });

                    // destroy database connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;

                    return {errorCode:'',message:'Team average yearly ratings retrieved successfully.',teamMonthlyAverageRatings:teamMonthlyAverageRatingsResponse};
                }else{
                    console.log('Empty response is returned from the getPerformanceMonthYear() function.',performanceYearMonths);
                    throw 'EPM0124';// throw error occurred while retrieving the months in a performance year.   
                }
            }else{
                throw 'IVE0000';// throw validation error
            }
        }else{
            throw '_DB0100';// throw employee does not have view access
        }
    }catch(teamAvgMonthlyRatingsMainCatchErr) {
        console.log('Error in the retrieveTeamAverageMonthlyRatings() function main catch block. ',teamAvgMonthlyRatingsMainCatchErr);
        // destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (teamAvgMonthlyRatingsMainCatchErr === 'IVE0000') {
            console.log('Validation error in the retrieveTeamAverageMonthlyRatings() function. ',validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });// return response
        } else{
            errResult = commonLib.func.getError(teamAvgMonthlyRatingsMainCatchErr, 'EPM0020');
            throw new ApolloError(errResult.message,errResult.code);// return response
        }
    }
};

// function to get the performance average rating details for the given performance month from the overall months
async function getInputMonthAverageRating(averageYearlyRating,inputMonthYear){
    return averageYearlyRating.filter(
        function(averageYearlyRating) {
          return averageYearlyRating.Performance_Assessment_Month === inputMonthYear.month
        }
    );
}