// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require file to access constant values
const { formName,systemLogs } = require('../../common/appconstants');
// require validation file
const { serviceProviderValidation} = require('./settingsFormInputValidations/inputValidation');
// require file to access common functions
const {insertSystemLogs} = require('../../common/commonFunction');
// require table alias
const { ehrTables } = require('../../common/tablealias');
// require common function
const { validateServiceProviderNameExist } =require('../../common/commonFunction');

// resolver definition
const resolvers = {
    Mutation:{
        // function to add/update service provider details
        addServiceProviderDetails: async (parent, args, context, info) => {
            // variable declarations
            let organizationDb='';
            let validationError={};
            try{
                console.log('Inside addServiceProviderDetails function',args);
                // variable declarations
                let loggedInEmpId=context.Employee_Id;
                let ipAddress=context.User_Ip;
                let checkRights;
                // get the organization database connection
                organizationDb = knex(context.connection.OrganizationDb);
                // check general form access rights
                checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDb, loggedInEmpId, formName.general,'','UI');
                if (Object.keys(checkRights).length === 0) {
                    throw (args.isEditForm)?'_DB0102':'_DB0101'; // throw error if employee does not have access
                }
                else if(checkRights.Employee_Role.toLowerCase() !== 'admin'){
                    throw '_DB0109';
                }
                else {
                    // check whether employee have edit access when input isEditForm is 1 else add access to general form
                    if((args.isEditForm===1 && checkRights.Role_Update === 1) || (args.isEditForm===0 && checkRights.Role_Add === 1))
                    {
                        // validate the inputs
                        validationError=await serviceProviderValidation(args);
                        // check input validation error exist or not
                        if(Object.keys(validationError).length ===0){
                            // function to validate service provider name exist or not
                            await validateServiceProviderNameExist(organizationDb,args.serviceProviderName,args.isEditForm,args.serviceProviderId);
                            // get the employee timezone based on location
                            let employeeTimeZone = await commonLib.func.getEmployeeTimeZone(loggedInEmpId, organizationDb,1);
                            // based on input field differentiate add or update action
                            if(args.isEditForm){
                                let updateParams={
                                    Service_Provider_Name: args.serviceProviderName,
                                    Contact_Person_Name: args.contactPersonName,
                                    Contact_Person_Phone_Number:args.contactNumber,
                                    Email_Id:args.emailId,
                                    Website:(args.website)?(args.website):null,
                                    Service_Provider_Logo:(args.serviceProviderLogo)?(args.serviceProviderLogo):null,
                                    Service_Provider_Mou:(args.serviceProviderMou)?(args.serviceProviderMou):null,
                                    Updated_On: employeeTimeZone,
                                    Updated_By: loggedInEmpId
                                }
                                // update service provider details based on id
                                return(
                                    organizationDb(ehrTables.serviceProvider)
                                    .update(updateParams)
                                    .where('Service_Provider_Id',args.serviceProviderId)
                                    .then(async() =>{
                                        let message = 'Update service provider details' + ' - ' + args.serviceProviderId;
                                        await insertSystemLogs(organizationDb,systemLogs.roleUpdate,ipAddress,loggedInEmpId,'','','',message);
                                        organizationDb ? organizationDb.destroy() : null;
                                        return { errorCode:'',message:'Service provider details updated successfully.'};                                                
                                    })
                                    .catch(function (catchError) {
                                        console.log('Error in addServiceProviderDetails function .catch block.',catchError);
                                        organizationDb ? organizationDb.destroy() : null;
                                        errResult = commonLib.func.getError(catchError, 'SGE0102');
                                        // throw error response to UI
                                        throw new ApolloError(errResult.message, errResult.code);    
                                    })
                                );
                            }
                            else{
                                // form insert params
                                let insertFields={
                                    Service_Provider_Name: args.serviceProviderName,
                                    Contact_Person_Name: args.contactPersonName,
                                    Contact_Person_Phone_Number:args.contactNumber,
                                    Email_Id:args.emailId,
                                    Website:(args.website)?(args.website):null,
                                    Service_Provider_Logo:(args.serviceProviderLogo)?(args.serviceProviderLogo):null,
                                    Service_Provider_Mou:(args.serviceProviderMou)?(args.serviceProviderMou):null,
                                    Location_Id:args.locationId,
                                    Added_On: employeeTimeZone,
                                    Added_By: loggedInEmpId
                                }
                                return(
                                    organizationDb(ehrTables.serviceProvider)
                                    .insert(insertFields)
                                    .then(async(insertRecord) =>{
                                        let message = 'Add service provider details' + ' - ' + insertRecord;
                                        await insertSystemLogs(organizationDb,systemLogs.roleAdd,ipAddress,loggedInEmpId,'','','',message);
                                        organizationDb ? organizationDb.destroy() : null;
                                        return { errorCode:'',message:'Service provider details added successfully.'};                                                
                                    })
                                    .catch(function (catchError) {
                                        console.log('Error in addServiceProviderDetails function .catch block.',catchError);
                                        organizationDb ? organizationDb.destroy() : null;
                                        errResult = commonLib.func.getError(catchError, 'SGE0101');
                                        // throw error response to UI
                                        throw new ApolloError(errResult.message, errResult.code);    
                                    })
                                );
                            }
                        }
                        else{
                            throw 'IVE0000';
                        }
                    }
                    else{
                        throw (args.isEditForm)?'_DB0102':'_DB0101'; // throw error if employee does not have access
                    }
                }
            }
            catch(mainCatchError)
            {
                console.log('Error in addServiceProviderDetails function main catch block',mainCatchError);
                // destroy database connection
                organizationDb ? organizationDb.destroy() : null;
                if (mainCatchError === 'IVE0000') {
                    console.log('Validation error in addServiceProviderDetails function - ',validationError);
                    let errResult = commonLib.func.getError(mainCatchError, 'IVE0000');
                    throw new UserInputError(errResult.message, { validationError: validationError });
                }
                else if(mainCatchError === 'IVE0006'){
                    validationError['IVE0006'] = commonLib.func.getError('', 'IVE0006').message;
                    let errResult = commonLib.func.getError('', 'IVE0000');
                    throw new UserInputError(errResult.message, { validationError: validationError });
                }
                else{
                    let errResult = commonLib.func.getError(mainCatchError, 'SGE0001');
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        }
    }
};

exports.resolvers = resolvers;
