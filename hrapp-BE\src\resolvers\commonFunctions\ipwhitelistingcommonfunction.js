// require table alias
const tables = require('../../../common/tablealias');
var ehrTables = tables.ehrTables;
// function to get location based on locationId
async function getLocation(locationId, organizationDbConnection){
    // get location name by using locationId
    return(
        organizationDbConnection(ehrTables.location)
        .select('Location_Name')
        .where('Location_Id',locationId)
        .then(locationData =>{
            // return response to resolver
            return locationData[0] ? locationData[0].Location_Name : '';
        }).catch(error =>{
            console.log('Error in getLocation() function .catch block',error);
            // return response to resolver
            return '';
        })
    )
}
module.exports.getLocation = getLocation;