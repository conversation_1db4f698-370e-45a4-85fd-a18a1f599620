// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require table alias list
const tables = require('../../../common/tablealias');

let ehrTables = tables.ehrTables;

// resolver definition
const resolvers = {
    Query: {
        // function to retrieve the static tds contract details
        getStaticContractTaxDetails: async (parent, args, context, info) => {
            // variable declarations
            let organizationDb='';
            let errResult;
            try{   
                console.log('Inside retrieve static TdsContractDetails function');
                // make database connection
                organizationDb = knex(context.connection.OrganizationDb);
                // get assessment year from org details table
                let getYear= await commonLib.func.getOrgDetails(context.Org_Code,organizationDb,0);
                // check whether value exist or not
                if(Object.keys(getYear).length > 0)
                {
                    // retrieve tax sections,tax rates based on tax status which are in active and assessment year
                    return(
                        organizationDb(ehrTables.contractorTaxSection+' as CTS')
                        .select('CTS.Tax_Section_Id as taxSectionId','CTS.Tax_Section_Name as taxSectionName','CTS.Description as description',
                        'CTR.Resident_Threshold_Limit as residentThresholdLimit','CTR.NonResident_Threshold_Limit as nonResidentThresholdLimit',
                        'CTR.Resident_Tds_Rate as residentTdsRate','CTR.NonResident_Tds_Rate as nonResidentTdsRate',
                        'CTR.Resident_NoPan_Tds_Rate as residentNoPanTdsRate','CTR.NonResident_NoPan_Tds_Rate as nonResidentNoPanTdsRate')
                        .leftJoin(ehrTables.contractorTaxRates+' as CTR','CTS.Tax_Section_Id','CTR.Tax_Section_Id')   
                        .where('CTR.Tax_Status','Active')  
                        .andWhere('CTR.Assessment_Year',getYear.Assessment_Year)
                        .then((getTaxData) => {
                            // destroy database connection
                            organizationDb ? organizationDb.destroy() : null;
                            // return responses
                            return { errorCode: '', message: 'Contract tax details retrieved successfully.', contractTaxDetails:(getTaxData.length>0)?getTaxData:[]};
                        })
                        .catch(function (error) {
                            console.log('Error in getStaticContractTaxDetails .catch block',error);
                            // destroy database connection
                            organizationDb ? organizationDb.destroy() : null;
                            errResult = commonLib.func.getError(error, 'ERE0025');
                            // return response
                            throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, contractTaxDetails : [] }));
                        })          
                    );
                }
                else{
                    console.log('Assessment year does not exist or error in retrieve data from org details table');
                    throw 'ERE0122';
                }
            }
            catch(mainCatchError)
            {
                console.log('Error in getStaticContractTaxDetails function catch block',mainCatchError);
                // destroy database connection
                organizationDb ? organizationDb.destroy() : null;
                // get the code and message from common function based on returned error code
                errResult = commonLib.func.getError(mainCatchError, 'ERE0025');
                // return error response
                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, contractTaxDetails : [] }));                
            }
        }
    }
}

exports.resolvers = resolvers;
