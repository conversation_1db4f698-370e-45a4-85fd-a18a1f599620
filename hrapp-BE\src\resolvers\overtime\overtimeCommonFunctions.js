// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require table names
const { ehrTables } = require('../../../common/tablealias');
// require common constant files
const { formIds } = require('../../../common/appconstants');
// require common validation file
const validation = require('../../../common/commonvalidation');
// function to validate the inputs
function validateInputs(args) {
    try {
        let validationError = {};
        // get input values
        let { employeeId, otStartTime, otEndTime,reason ='' } = args;
        // validate employeeId
        if (!employeeId) {
            validationError['IVE0083'] = commonLib.func.getError('', 'IVE0083').message;
        }
        // Validate otStartTime
        if (!otStartTime) {
            validationError['IVE0101'] = commonLib.func.getError('', 'IVE0101').message;
        }
        // Validate otEndTime
        if (!otEndTime) {
            validationError['IVE0102'] = commonLib.func.getError('', 'IVE0102').message;
        }
        // start date should be lessthan end date
        if (otStartTime >= otEndTime) {
            validationError['IVE0098'] = commonLib.func.getError('', 'IVE0098').message;
        }
        // vaidate reason if exists
        if(reason){
            let validateReason = validation.checkLength(reason, 5, 500);
            if (!validateReason) {
                validationError['IVE0002'] = commonLib.func.getError('', 'IVE0002').message;
            }
        }
        // return validation error back to function
        return validationError;
    } catch (validateInputsError) {
        console.log('Error in validateInputs() function catch block.', validateInputsError);
        throw (validateInputsError);
    }
};
module.exports.validateInputs = validateInputs;

// function to get managerIds
async function getApprover(status, employeeId,orgDb){
    let managerId = 0;
    try{    
        // if status is Applied then get First level approver
        // if status is Approved then get the Second level approver
        if(status.toLowerCase() === 'applied'){
            // get first level manager configuration
            let configuration = await orgDb(ehrTables.approvalManagement)
                                .select('First_Level_Approver')
                                .where('Form_Id', formIds.overtime)
                                .then(approverConfiguration =>{
                                    // return response back to function
                                    return approverConfiguration;
                                });
            if (configuration[0] && configuration[0].First_Level_Approver === 'FIRST LINE MANAGER'){
                managerId = await orgDb(ehrTables.empJob)
                                .select('Manager_Id')
                                .where('Employee_Id',employeeId)
                                .then(managerData =>{
                                    // return response
                                    return managerData[0] ? managerData[0].Manager_Id : null;
                                });
                // Return managerId
                return managerId;
            } else if (configuration[0] && configuration[0].First_Level_Approver === 'INDIVIDUAL MANAGER'){
                managerId = await orgDb(ehrTables.approvalManagementEmployees)
                                .select('Employee_Id')
                                .where('Form_Id', formIds.overtime)
                                .where('Approval_Level', 1) // 2 - Second level, 1 - First Level
                                .then(managerData => {
                                    // return response
                                    return managerData[0] ? managerData[0].Employee_Id : null;
                                });
                // Return managerId
                return managerId;
            } else{
                // Return managerId
                return managerId;
            }
        }else{
            // get Second level manager configuration
            let configuration = await orgDb(ehrTables.approvalManagement)
                .select('Second_Level_Approver')
                .where('Form_Id', formIds.overtime)
                .then(approverConfiguration => {
                    // return response back to function
                    return approverConfiguration;
                });
            if (configuration[0] && configuration[0].Second_Level_Approver === 'SECOND LINE MANAGER') {
                managerId = await orgDb(ehrTables.empJob)
                            .select('Manager_Id')
                            .where('Employee_Id', employeeId)
                            .then(managerData => {
                                // return response
                                return managerData[0] ? managerData[0].Manager_Id : null;
                            });
                // Return managerId
                return managerId;
            } else if (configuration[0] && configuration[0].Second_Level_Approver === 'INDIVIDUAL MANAGER') {
                managerId = await orgDb(ehrTables.approvalManagementEmployees)
                            .select('Employee_Id')
                            .where('Form_Id', formIds.overtime)
                            .where('Approval_Level', 2) // 2 - Second level, 1 - First Level
                            .then(managerData => {
                                // return response
                                return managerData[0] ? managerData[0].Employee_Id : null;
                            });
                // Return managerId
                return managerId;
            } else{
                // Return managerId
                return managerId;
            }
        }
    } catch (getApproverError) {
        console.log('Error in getApprover() function catch block.', getApproverError);
        return managerId;
    }
};
module.exports.getApprover = getApprover;

// get workschdule Id based on the employee type
async function getWorkScheduleId(employeeId,startDate,employeeType,orgDb){
    // check employee type based on that get workschedule
    if (employeeType === 'Employee Level'){
        return( 
            orgDb(ehrTables.empJob)
            .select('Work_Schedule')
            .where('Employee_Id', employeeId)
            .then(workScheduleId => {
                // return response
                return workScheduleId[0] ? workScheduleId[0].Work_Schedule : null;
            })
            .catch(getWorkScheduleIdError => {
                console.log('Error while getting the workschedule Id in overtimeWageCalculation() function if block.', getWorkScheduleIdError);
                // return response
                throw (getWorkScheduleIdError);
            })
        );
    }else{
        return (
            orgDb(ehrTables.shiftEmpMapping + ' as SEM')
            .select('EST.WorkSchedule_Id')
            .innerJoin(ehrTables.empShiftType + ' as EST', 'EST.Shift_Type_Id', 'SEM.Shift_Type_Id')
            .where('SEM.Employee_Id', employeeId)
            .where('SEM.Shift_Start_Date', startDate)
            .then(workScheduleId => {
                // Check shift record exists or not
                if (workScheduleId[0]) {
                    return workScheduleId[0].WorkSchedule_Id
                } else {
                    // return response
                    return 0;
                }
            })
            .catch(getWorkScheduleIdError =>{
                console.log('Error while getting the workscheduleId in getWorkScheduleId() function else block.', getWorkScheduleIdError);
                throw (getWorkScheduleIdError);
            })
        );
    }
};
module.exports.getWorkScheduleId = getWorkScheduleId;

// function to get and check the leave and compoff details
async function checkLeaveAndCompOff(employeeId, date,orgDb){
    let totalLeaveHours = 0;
    let totalCompOffDuration = 0;
    try{
        // Check leave or compoff exist or not. If not then we should allow them to apply overtime details
        let leaveDetails = await commonLib.employees.getLeaves(employeeId, date, date, 'get-overtime-prerequisites', orgDb);
        // get compensatory off details
        let compOffDetails = await commonLib.employees.getCompensatoryOff(employeeId, date, date, 'get-overtime-prerequisites', orgDb);
        // Check leaveDetails array is not empty
        if (leaveDetails.length){
            for (let leave of leaveDetails) {
                totalLeaveHours = totalLeaveHours + leave.Hours;
            }
        }
        // Check compOffDetails array is not empty
        if(compOffDetails.length){
            for (let compOff of compOffDetails) {
                totalCompOffDuration = totalCompOffDuration + compOff.Duration
            }
        }
        // return response
        return { totalLeaveHours, totalCompOffDuration };
    } catch (checkLeaveAndCompOffError) {
        console.log('Error in checkLeaveAndCompOff() function catch block.', checkLeaveAndCompOffError);
        // return response
        return { totalLeaveHours, totalCompOffDuration };
    }
};
module.exports.checkLeaveAndCompOff = checkLeaveAndCompOff;

// function to for time in HH:MM format
async function formTotalHours(totalSeconds){
    try{
        // get hours from totalSeconds
        let hours = parseInt(totalSeconds / (60 * 60));
        if (hours){
            hours = hours > 9 ? hours : '0' + hours;
        }
        // get remaining seconds from totalSeconds
        let reminingSeconds = (totalSeconds % (60 * 60));
        let minutes = '00';
        if (reminingSeconds){
            // get minutes from reminingSeconds
            minutes = parseInt(reminingSeconds / 60);
            minutes = minutes ? minutes : '00';
            if(minutes !== '00'){
                minutes = parseInt(minutes) > 9 ? parseInt(minutes) : '0' + parseInt(minutes);
            }
        }       
        // return response
        return hours + ':' + minutes;
    }catch(formTotalHoursError){
        console.log('Error in formTotalHours() function catch block.',formTotalHoursError);
        return '00:00';
    }
};
module.exports.formTotalHours = formTotalHours;

// function to calculate the compensatory balance while applying overtime and return the respective details based on the action
async function getCompensatoryOffBalanceDetailsForOvertime(orgDb,args){
    console.log('Inside the getCompensatoryOffBalanceDetailsForOvertime() function.')
    // require moment 
    const moment = require('moment-timezone');
    //variable declaration
    let compensatoryOffBalance = 0;
    let isEmployeeEligibleForShiftAllowance = 0;
    let response = {};
    let employeeSpecialWagesConfiguration = {};
    try{
        let employeeId = args.employeeId;

        let employeeSpecialWagesInputs = {
            employeeId: employeeId,
            date: args.regularFromDate,
            action: args.action,
            workScheduleDetail: args.workScheduleDetail,
            fetchEmployeeWorkingDaySpecialWageConfig: args.fetchEmployeeWorkingDaySpecialWageConfig
        }
        // get the special wage configuration for the date based on the holiday type and the salary type
        let employeeSpecialWagesResponse = await commonLib.employees.getEmployeeSpecialWageConfiguration(orgDb,employeeSpecialWagesInputs);

        // if the error response does not returned and the special wage configuration exist for the regular start date
        if(!employeeSpecialWagesResponse.errorCode && Object.keys(employeeSpecialWagesResponse.specialWagesConfiguration).length > 0){
            employeeSpecialWagesConfiguration = employeeSpecialWagesResponse.specialWagesConfiguration;
            //if the compensatory off is enabled in the special wage configuration then the employee comp off or shift allowance eligibility can be validated
            if(employeeSpecialWagesConfiguration.Comp_Off === 'Yes'){
                // get the total hours from otStartTime and otEndTime
                let otStartTime = moment(args.otStartTime, 'YYYY-MM-DD hh:mm:ss');
                let otEndTime = moment(args.otEndTime, 'YYYY-MM-DD hh:mm:ss');
                // Get difference between 2 dates in seconds
                let timeDiff = otEndTime.diff(otStartTime, 'seconds');
                // convert timeDiff seconds to hours
                timeDiff = (timeDiff / 3600).toFixed(2);
                let totalHours = timeDiff;

                if(employeeSpecialWagesConfiguration.Comp_Off_Balance_Approval === 'Manual'){
                    let compOffApplicability = employeeSpecialWagesConfiguration.Comp_Off_Applicability_For_Overtime_Hours;
                    if(compOffApplicability === 'Both Full Day & Half Day') {
                        if(totalHours >= employeeSpecialWagesConfiguration.Minimum_OT_Hours_For_Full_Day_Comp_Off) {
                            compensatoryOffBalance = 1;
                            isEmployeeEligibleForShiftAllowance = 1;
                        } else if(totalHours >= employeeSpecialWagesConfiguration.Minimum_OT_Hours_For_Half_Day_Comp_Off) {
                            compensatoryOffBalance = 0.5;
                            isEmployeeEligibleForShiftAllowance = 0;
                        }
                    }else if (compOffApplicability === 'Full Day' && totalHours >= employeeSpecialWagesConfiguration.Minimum_OT_Hours_For_Full_Day_Comp_Off) {
                        compensatoryOffBalance = 1;
                        isEmployeeEligibleForShiftAllowance = 1;
                    }else if (compOffApplicability === 'Half Day' && totalHours >= employeeSpecialWagesConfiguration.Minimum_OT_Hours_For_Half_Day_Comp_Off) {
                        compensatoryOffBalance = 0.5;
                        isEmployeeEligibleForShiftAllowance = 0;
                    }else{
                        console.log('Compensatory-off balance and the shift allowance eligibility cannot be calculated.Comp off configuration: ',employeeSpecialWagesConfiguration,'totalHours: ',totalHours);
                        compensatoryOffBalance = 0;
                        isEmployeeEligibleForShiftAllowance = 0;
                    }
                }else{
                    console.log('Compensatory-off balance and the shift allowance eligibility cannot be calculated as the Comp_Off_Balance_Approval value is automatic.Comp off configuration: ',employeeSpecialWagesConfiguration);
                    compensatoryOffBalance = 0;
                    isEmployeeEligibleForShiftAllowance = 0;
                }
            }else{
                console.log("Compensatory off is not enabled in the special wage configuration for the employee for the regular start date. ",employeeSpecialWagesConfiguration);
                compensatoryOffBalance = 0;
                isEmployeeEligibleForShiftAllowance = 0;
            }
        }else{
            console.log("Error or empty response is returned from the getEmployeeSpecialWageConfiguration() function. ",employeeSpecialWagesResponse);
            compensatoryOffBalance = 0;
            isEmployeeEligibleForShiftAllowance = 0;
        }

        /** If this function is triggered while calculating the compensatory off balance */
        if(args.action.toLowerCase() === 'calculateovertimecompoffbalance'){
            response = {
                compensatoryOffBalance :compensatoryOffBalance
            }
        }/** If this function is triggered while calculating the shift allowance */
        else if(args.action.toLowerCase() === 'calculateovertimeshiftallowance'){
            response = {
                isEmployeeEligibleForShiftAllowance :isEmployeeEligibleForShiftAllowance
            }
        }else{
            /** If this function is triggered while updating the compensatory off balance during the second level approval */
            response = {
                compensatoryOffBalance :compensatoryOffBalance,
                isEmployeeEligibleForShiftAllowance: isEmployeeEligibleForShiftAllowance,
                employeeSpecialWagesConfiguration: employeeSpecialWagesConfiguration
            }
        }

        return response;
    }catch(getCompOffDetailsMainCatchError){
        console.log('Error in the getCompensatoryOffBalanceDetailsForOvertime() function main catch block. ',getCompOffDetailsMainCatchError);
        return response;
    }
}
module.exports.getCompensatoryOffBalanceDetailsForOvertime = getCompensatoryOffBalanceDetailsForOvertime;

// get overtime settings from overtime_settings table
async function getOvertimeSettings(orgDb) {
    let overtimeSettings = await orgDb(ehrTables.overtimeSettings)
    .select('*')
    .then(overtimeSettingsResponse => {
        return overtimeSettingsResponse[0] ? overtimeSettingsResponse[0] : {};
    })
    .catch(getOvertimeSettingsError =>{
        console.log('Error in the getOvertimeSettings() function .catch block.', getOvertimeSettingsError);
        return {};
    });

    return overtimeSettings;//return response
}
module.exports.getOvertimeSettings = getOvertimeSettings;

// get the workschedule title using the work schedule id
async function getWorkScheduleTitle(orgDb,workScheduleId){
    try{
        let workScheduleTitle = await orgDb(ehrTables.workSchedule + ' as WS')
        .select('WS.Title')
        .where('WS.WorkSchedule_Id',workScheduleId)
        .then(titleResponse => {
            return (titleResponse && titleResponse.length > 0) ? titleResponse[0].Title : '';
        })
        .catch(titleCatchError =>{
            console.log('Error in the getWorkScheduleTitle() function .catch block.', titleCatchError);
            return '';
        });

        return workScheduleTitle;//return response
    }catch(getTitleMainCatchError){
        console.log('Error in the getWorkScheduleTitle() function main catch block. ',getTitleMainCatchError);
        return '';//return response
    }
}
module.exports.getWorkScheduleTitle = getWorkScheduleTitle;

/**
 * Find the overtime claim start date-time - actual shift and validate either the start date time is valid or not based on the overtime settings
 * @param {Object} orgDb - Organization Database connection object
 * @param {Object} args - Input params
 * @param {string} args.employeeId - Overtime claim request employee unique id
 * @param {string} args.otStartTime - Employee overtime claim start date-time.
 * @param {Number} args.allowRegularHoursOvertimeForWeekoffHoliday - Number should be either 0 or 1 to allow the employee to claim overtime from "shift - consideration start date-time" when the shift falls on weekoff or holiday
 * @returns {Object} Return response object with the params - error,currentWorkScheduleDetails,otStartDate,isWorkingDay in try and catch block
 */
async function validateOvertimeWorkSchedule(orgDb,args){
    console.log('Inside the validateOvertimeWorkSchedule() function.');
    // require moment 
    let moment = require('moment-timezone');
    // declare response object
    let response = {
        error : '',
        currentWorkScheduleDetails: {},
        workScheduleDetailsBeforeFormat: {},
        otStartDate: '',
        isWorkingDay: ''
    };
    try{
        let {employeeId,otStartTime,allowRegularHoursOvertimeForWeekoffHoliday} = args;
        // find the overtime start date falls in which shift duration and fetch those shift details
        let currentWorkScheduleDetails = await commonLib.employees.getCurrentWorkScheduleDetails(employeeId, otStartTime, orgDb);
        // get workSchedule details
        let { regularFrom, regularTo, considerationFrom, considerationTo } = currentWorkScheduleDetails;
        // Check regularFrom, regularTo, considerationFrom, considerationTo is not empty
        if (regularTo && regularFrom && considerationFrom && considerationTo){
            response.workScheduleDetailsBeforeFormat.considerationFrom = currentWorkScheduleDetails.considerationFrom;
            response.workScheduleDetailsBeforeFormat.considerationTo = currentWorkScheduleDetails.considerationTo;
            response.workScheduleDetailsBeforeFormat.regularFrom = currentWorkScheduleDetails.regularFrom;
            response.workScheduleDetailsBeforeFormat.regularTo = currentWorkScheduleDetails.regularTo;

            //convert the date from this format, "2021-01-15T21:00:00.000Z" to "2020-09-09 23:50:00"
            considerationFrom = considerationFrom.toISOString().replace(/T/, ' ').replace(/\..+/, '');
            considerationTo = considerationTo.toISOString().replace(/T/, ' ').replace(/\..+/, '');
            regularFrom = regularFrom.toISOString().replace(/T/, ' ').replace(/\..+/, '');
            regularTo = regularTo.toISOString().replace(/T/, ' ').replace(/\..+/, '');

            currentWorkScheduleDetails.considerationFrom = considerationFrom;
            currentWorkScheduleDetails.considerationTo = considerationTo;
            currentWorkScheduleDetails.regularFrom = regularFrom;
            currentWorkScheduleDetails.regularTo = regularTo ;
            response.currentWorkScheduleDetails = currentWorkScheduleDetails;//response object

            let otStartDate = moment(otStartTime).format('YYYY-MM-DD');// date-time to date format
            response.otStartDate = otStartDate;//response object
            // If we want to check corresponding overtime start date is working day or not based on workschedule,
            // then we have to send the totalWorkingDays as null and formName as leaves
            let isWorkingDay = await commonLib.payroll.getBusinessWorkingDays(orgDb, employeeId, otStartDate, otStartDate, 1, null, 'leaves');
            response.isWorkingDay = isWorkingDay;
            
            /** validate whether are eligible to apply overtime before regular to date-time. Because employee is eligible to apply the overtime before "regular to" 
             * date-time only when the "allowRegularHoursOvertimeForWeekoffHoliday" value is 1 and also the overtime claim shift should fall on either weekoff or holiday. */
            if (otStartTime < regularTo && (allowRegularHoursOvertimeForWeekoffHoliday === 0 || 
                (allowRegularHoursOvertimeForWeekoffHoliday === 1 && isWorkingDay ))){
                response.error = 'OT0110';// Overtime should not fall in the regular work hours
            }
            /** validate employee applies the overtime claim on or after the consideration start date-time. Because employee is eligible to apply the overtime on or after the
             * consideration start date-time when the "allowRegularHoursOvertimeForWeekoffHoliday" value is 1 and if the overtime is claimed on weekoff or holiday.In this statement,
             * "otStartTime < considerationFrom" will not fail as this is already checked in the function "getCurrentWorkScheduleDetails" */
            else if (otStartTime < considerationFrom && allowRegularHoursOvertimeForWeekoffHoliday === 1 && !(isWorkingDay)){
                response.error = 'OT0119';// Overtime can be claimed after the consideration start time
            }else{
                response.error = '';
            }
        }else{
            console.log('Error response from the getCurrentWorkScheduleDetails() function. ', currentWorkScheduleDetails);            
            response.error = (Object.keys(currentWorkScheduleDetails).length > 0 && currentWorkScheduleDetails.errorCode) ? currentWorkScheduleDetails.errorCode : 'OT0011';
        }
        return response;//return response
    }catch(validateOvertimeWorkScheduleMainCatchError){
        console.log('Error in the validateOvertimeWorkSchedule() function main catch block. ',validateOvertimeWorkScheduleMainCatchError);
        response.error = validateOvertimeWorkScheduleMainCatchError;
        return response;//return response
    }
}
module.exports.validateOvertimeWorkSchedule = validateOvertimeWorkSchedule;

/**
 * Get the shift allowance.
 * If the custom group exist for the work schedule id and the shift mode get the shift allowance from the custom group coverage-shift type configuration
 * only if the employee id exist in the custom group coverage - employee list. If the custom group does not exist for the work schedule id and the 
 * shift mode get the shift allowance from the organization coverage-shift type configuration.
 * @param {Object} orgDb - Organization Database connection object
 * @param {Object} args - Input params
 * @param {string} args.employeeId - employee unique id
 * @param {string} args.workScheduleId - work schedule id
 * @param {Array} args.shiftMode - manual | both
 * @param {string} args.source - The action name from where this function is triggered
 * @returns {Number} - Return shift allowance amount.
 * @throws {Object} - When an error occured throws error only if the request is from the additional wage claim form - calculate form. Otherwise return 0
 */
async function getEmployeeShiftAllowance(orgDb,args){
    let shiftAmount = 0;
    let {employeeId,workScheduleId,shiftMode,source} = args;
    try{
        //check the shift allowance- shift-type custom group exist for the shift modes
        let isShiftTypeCustomGroupExist = await checkShiftAllowanceCustomGroupShiftTypeExist(orgDb,workScheduleId,shiftMode,source);
        if(isShiftTypeCustomGroupExist === 1){
            // get the shift type custom group-configuration id associated with the employee and the shift-type form id from the custom employee group form
            let customGroupShiftTypeConfigIds = await commonLib.func.getCustomGroupParentId(orgDb,employeeId,formIds.shiftType);
            // if the employee id exists in the shift-type custom group
            if(customGroupShiftTypeConfigIds && customGroupShiftTypeConfigIds.length > 0){
                //get the shift allowance amount for the work schedule id from the shift-type custom group configuration ids
                shiftAmount = 
                await orgDb(ehrTables.shiftType + ' as ST')
                .select('STC.Amount')
                .innerJoin(ehrTables.shiftTypeConfiguration + ' as STC', 'ST.Shift_Name','STC.WorkSchedule_Id')
                .whereIn('STC.Shift_Mode', shiftMode)
                .where('ST.Coverage', 'CUSTOMGROUP')
                .where('ST.ShiftType_Status', 'Active')
                .whereIn('STC.Configuration_Id',customGroupShiftTypeConfigIds)
                .where('ST.Shift_Name', workScheduleId)
                .then(customGroupShiftTypeAmountDetail => {
                    if (customGroupShiftTypeAmountDetail && customGroupShiftTypeAmountDetail.length > 0) {
                        return customGroupShiftTypeAmountDetail[0].Amount;// return response
                    } else {
                        return 0;// return response
                    }
                })
                .catch(customGroupShiftTypeAmountCatchError =>{
                    console.log('Error while getting the custom group coverage shift-type amount in the .catch block.', customGroupShiftTypeAmountCatchError);
                    if(source.toLowerCase() === 'updateemployeeshiftallowance'){
                        return 0;
                    }else{
                        throw(customGroupShiftTypeAmountCatchError)
                    }
                })
            }
        }else{
            // get the shift amount for the work schedule id for the organization coverage
            shiftAmount = 
            await orgDb(ehrTables.shiftType + ' as ST')
            .select('ST.Amount')
            .where('ST.Coverage', 'ORGANIZATION')
            .where('ST.ShiftType_Status', 'Active')
            .where('ST.Shift_Name', workScheduleId)
            .then(organizationShiftTypeAmountDetail => {
                if (organizationShiftTypeAmountDetail && organizationShiftTypeAmountDetail.length > 0) {
                    return organizationShiftTypeAmountDetail[0].Amount;// return response
                } else {
                    return 0;// return response
                }
            })
            .catch(organizationShiftTypeAmountDetailCatchError =>{
                console.log('Error while getting the organization coverage shift-type amount in the .catch block.', organizationShiftTypeAmountDetailCatchError);
                if(source.toLowerCase() === 'updateemployeeshiftallowance'){
                    return 0;
                }else{
                    throw(organizationShiftTypeAmountDetailCatchError)
                }
            });
        }

        return shiftAmount;
    }catch(getEmployeeShiftAllowanceMainCatchError){
        console.log('Error in the getEmployeeShiftAllowance() function main catch block. ',getEmployeeShiftAllowanceMainCatchError);
        if(source.toLowerCase() === 'updateemployeeshiftallowance'){
            return 0;
        }else{
            throw(getEmployeeShiftAllowanceMainCatchError);
        }
    }
}
module.exports.getEmployeeShiftAllowance = getEmployeeShiftAllowance;

/**
 * validate the shift allowance - shift type custom group coverage exist or not for the shift modes based on the
 * work schedule id or for the whole shift type configuration.
 * @param {Object} orgDb - Organization Database connection object
 * @param {Number} workScheduleId - Work schedule id which is a optional paramater.
 * @param {Array} shiftMode - manual | both
 * @param {String} source - The action name from where this function is triggered
 * @returns {Number} - Return 1 when the custom group exist for the shift modes. Otherwise return 0. 
 * Also return 0 for the particular sources when an error occured.
 * @throws {Object} - When an error occured throws an error only when the request is from particular sources
 */
async function checkShiftAllowanceCustomGroupShiftTypeExist(orgDb,workScheduleId=0,shiftMode,source){
    try{
        // Get the shift allowance - shift-type configuration record count for the custom group coverage
        let customGroupCoverageShiftTypeDetails = orgDb(ehrTables.shiftType + ' as ST')
        .count('ST.ShiftType_Id as shiftTypeCount')
        .innerJoin(ehrTables.shiftTypeConfiguration + ' as STC', 'ST.Shift_Name','STC.WorkSchedule_Id')
        .whereIn('STC.Shift_Mode', shiftMode)
        .where('ST.Coverage', 'CUSTOMGROUP');

        // if work schedule id exist validate the shift type configuration for the work schedule id
        if(workScheduleId){
            customGroupCoverageShiftTypeDetails = customGroupCoverageShiftTypeDetails.where('ST.Shift_Name', workScheduleId);
        }

        let customGroupExistCount = await customGroupCoverageShiftTypeDetails
        .then(async(shiftTypeCountDetail) => {
            if (shiftTypeCountDetail && shiftTypeCountDetail.length > 0 && shiftTypeCountDetail[0].shiftTypeCount > 0) {
                return 1;
            }else{
                return 0;
            }
        }).catch(shiftTypeCountCatchError =>{
            console.log('Error in the checkShiftAllowanceCustomGroupShiftTypeExist() function .catch block.', shiftTypeCountCatchError);
            if(source.toLowerCase() === 'updateemployeeshiftallowance'){
                return 0;
            }else{
                throw('OT0010')
            }
        });
        return customGroupExistCount;
    }catch(shiftTypeCustomGroupExistMainCatchError){
        console.log('Error in the checkShiftAllowanceCustomGroupShiftTypeExist() function main catch block. ',shiftTypeCustomGroupExistMainCatchError);
        if(source.toLowerCase() === 'updateemployeeshiftallowance'){
            return 0;
        }else{
            throw(shiftTypeCustomGroupExistMainCatchError);
        }
    }
}
module.exports.checkShiftAllowanceCustomGroupShiftTypeExist = checkShiftAllowanceCustomGroupShiftTypeExist;

/**
 * Validate the inputs required for calculating the employee shift allowance
 * @param {Object} args - Input params
 * @param {Number} args.employeeId - Employee unique id
 * @param {Date} args.otStartTime - Employee overtime claim start date-time.
 * @param {Date} args.otEndTime - Employee overtime claim end date-time.
 * @param {Number} args.workScheduleId - Employee work schedule id
 * @returns {Object} Returns the empty object if all the input params are valid. Otherwise returns the validation error code with the error message for the invalid input(s) in an object
 * @throws {Object} Throws an error when the functional error occurs
 */
function validateCalculateShiftAllowanceInputs(args) {
    try {
        let validationError = {};
        // get input values
        let { employeeId, otStartTime,otEndTime,workScheduleId } = args;
        
        // validate employee id
        if (!employeeId) {
            validationError['IVE0083'] = commonLib.func.getError('', 'IVE0083').message;
        }
        // Validate overtime start date-time
        if (!otStartTime) {
            validationError['IVE0101'] = commonLib.func.getError('', 'IVE0101').message;
        }   
        // Validate otEndTime
        if (!otEndTime) {
            validationError['IVE0102'] = commonLib.func.getError('', 'IVE0102').message;
        }
        // validate start date-time is greater than or equal to end date-time
        if (otStartTime >= otEndTime) {
            validationError['IVE0098'] = commonLib.func.getError('', 'IVE0098').message;
        }
        // validate work shchedule id
        if (!workScheduleId) {
            validationError['IVE0150'] = commonLib.func.getError('', 'IVE0150').message;
        }
        
        return validationError;
    } catch (calculateShiftAllowanceInputsMainCatchError) {
        console.log('Error in validateCalculateShiftAllowanceInputs() function catch block.', calculateShiftAllowanceInputsMainCatchError);
        throw (calculateShiftAllowanceInputsMainCatchError);
    }
};
module.exports.validateCalculateShiftAllowanceInputs = validateCalculateShiftAllowanceInputs;

/**
 * Function to get the overtime claim details for the overtime claim ids based on the approval status 
 * @param {JSON} orgDb - Organization Database Connection Object
 * @param {String} oldApprovalStatus - Approval status associated with the overtime claim id
 * @param {Array} overtimeClaimIds - Overtime claim ids
 * @returns {Array} - Return the overtime claim details in a Array of Json 
 * @throws {JSON} - Throws when an error occured
 */
async function getOvertimeClaimStatusApprovalDetails(orgDb,oldApprovalStatus,overtimeClaimIds){
    return (
    orgDb.select('OT.Overtime_Claim_Id AS overtimeClaimId','OT.Employee_Id AS otEmployeeId','OT.Start_Date_Time AS otStartDateTime',
    'OT.End_Date_Time AS otEndDateTime','OT.Total_Hours AS totalHours','OT.Approval_Status AS oldApprovalStatus',
    'OT.Shift_Allowance_Applied_Work_Schedule_Id AS workScheduleId','Approved_By AS lastApprovedBy')
    .from(ehrTables.overtime + ' as OT')
    .whereIn('OT.Overtime_Claim_Id',overtimeClaimIds)
    .whereIn('OT.Approval_Status',oldApprovalStatus)
    .then(overtimeDetails => {
        if (overtimeDetails.length>0) {
            return overtimeDetails;
        } else {
            return [];
        }
    })
    .catch(overtimeClaimStatusApprovalDetailsError =>{
        console.log('Error in the getOvertimeClaimStatusApprovalDetails() .catch block.', overtimeClaimStatusApprovalDetailsError);
        throw (overtimeClaimStatusApprovalDetailsError);
    })
    );
}
module.exports.getOvertimeClaimStatusApprovalDetails = getOvertimeClaimStatusApprovalDetails;

/**
 * Function to validate the login employee id is the eligible approver for the overtime claim record
 * and return 1 if the record status can be updated. Otherwise return 0.
 * @param {JSON} eligibleApproverArgs - Input params
 * @param {Number} eligibleApproverArgs.isLoggedInEmpAdmin - 0 | 1
 * @param {String} oldApprovalStatus - Old approval status of the overtime claim id
 * @param {String} newApprovalStatus - New approval status of the overtime claim id
 * @param {Number} lastApprovedBy - Employee id who updated the status in the previous level approval
 * @param {Number} otEmployeeId - Overtime claim employee id
 * @param {Number} loginEmpId - Login employee id
 * @param {JSON} orgDb - Organization database connection object
 * @returns {Number} - 0 | 1
 * @throws {JSON} - Throws when an error occured.
 */
async function validLoginEmployeeIsEligibleApprover(eligibleApproverArgs,orgDb){
    try{
        let {isLoggedInEmpAdmin,oldApprovalStatus,newApprovalStatus,lastApprovedBy,otEmployeeId,loginEmpId} = eligibleApproverArgs;
        let updateOvertimeStatusRecord=0;
       
        /** If loggedInEmployeeId is admin then the employee can update the status of the OT record */
        if(isLoggedInEmpAdmin){
            updateOvertimeStatusRecord = 1;
        }else{
            /** If the old approval status is applied then the new approval status can be approved/returned/rejected. So if the approver type for the first level approval is 'First Line manager' in the approver management config 
             * then the reporting manager of the otEmployeeId has to be considered as the valid approver. Otherwise based on the other approver type the valid approver has to be fetched. If the old approval status is 
             * approved then the new approval status can be completed/returned/rejected. So if the approver type for the second level approval is 'Second Line manager' then the reporting manager
             * of the first level-approved by employeeid has to be considered as the valid approver. Otherwise based on the other approver type the valid approver has to be fetched. If the old approval 
             * status is completed, then only the admin has to update the status of the OT record */

            let uplineEmpId =0;
            if(newApprovalStatus.toLowerCase() !== 'paid'){
                if(oldApprovalStatus.toLowerCase() === 'applied'){
                    uplineEmpId = otEmployeeId;
                }else if(oldApprovalStatus.toLowerCase() === 'approved'){
                    uplineEmpId = lastApprovedBy;
                }else{
                    console.log('Old Approval Status is, ',oldApprovalStatus,'.When the old approval status is completed then status will not be updated to "paid" as only the admins are eligible for updating the status to paid. Or when the old approval status is returned then employee has to update and submit the overtime claim record. For other status no action should be performed.');
                }
                
                if(uplineEmpId){
                    // get valid manager id based on the new approval status
                    let eligibleApproverId = await getApprover(oldApprovalStatus,uplineEmpId,orgDb);
                    if(eligibleApproverId && loginEmpId === eligibleApproverId){
                        updateOvertimeStatusRecord = 1;
                    }else{
                        console.log('Overtime status will not be updated as the login employee id does not equal to the eligibleApproverId: ',eligibleApproverId,'and login employee id is: ', loginEmpId);
                    }
                }
            }
        }
        return updateOvertimeStatusRecord;
    }catch(validLoginEmployeeIsEligibleApproverCatchError){
        console.log('Error in the validLoginEmployeeIsEligibleApprover() function main catch block. ',validLoginEmployeeIsEligibleApproverCatchError);
        throw (validLoginEmployeeIsEligibleApproverCatchError);
    }
}
module.exports.validLoginEmployeeIsEligibleApprover = validLoginEmployeeIsEligibleApprover;

/**
 * Function to validate the overtime claim status update inputs.
 * @param {JSON} args - Input params
 * @param {String} args.newApprovalStatus - New approval status of the overtime claim id
 * @param {String} args.oneApprovalRecordComment - Comment for the overtime claim id for the status approval
 * @param {Array} args.overtimeClaimIds - Overtime claim ids
 * @returns {JSON} - Return the input error code and error message in a JSON when there are errors in the inputs.
 * Otherwise return empty JSON.
 * @throws {JSON} - Throws when an error occured.
 */
function validateUpdateOTStatusInputs(args){
    try{
        let validationError = {};
        let {newApprovalStatus,oneApprovalRecordComment,overtimeClaimIds} =args;

        let validNewApprovalStatus = ['Approved','Returned','Rejected','Completed','Paid'];   

        // Validate newApprovalStatus
        if (!newApprovalStatus || (!(validNewApprovalStatus.includes(newApprovalStatus)))) {
            validationError['IVE0106'] = commonLib.func.getError('', 'IVE0106').message;
        }

        if(Object.keys(validationError).length === 0){
            //Validate oneApprovalRecordComment
            if(oneApprovalRecordComment){
                let validateComment = validation.commentValidation(oneApprovalRecordComment); // comment validation
                if (!validateComment) {
                    validationError['IVE0037'] = commonLib.func.getError('', 'IVE0037').message2;
                }else{
                    validateComment = validation.checkLength(oneApprovalRecordComment,5,600); // comment length validation
                    if (!validateComment) {
                        validationError['IVE0037'] = commonLib.func.getError('', 'IVE0037').message3;
                    }
                }
            }else{
                //If the new approval status is returned,rejected then comment should exist
                if(newApprovalStatus === 'Returned' || newApprovalStatus === 'Rejected')  {
                    validationError['IVE0037'] = commonLib.func.getError('', 'IVE0037').message1;   
                }
            }

            //Validate overtimeClaimIds
            if(!overtimeClaimIds || overtimeClaimIds.length <= 0)  {
                validationError['IVE0105'] = commonLib.func.getError('', 'IVE0105').message;
            }

            // if filter month not exists
            if(!args.filterMonth){
                validationError['IVE0144'] = commonLib.func.getError('', 'IVE0144').message;
            }

            // if filter year not exists
            if(!args.filterYear){
                validationError['IVE0143'] = commonLib.func.getError('', 'IVE0143').message;
            }
        }
        return validationError;
    }catch(otStatusUpdateValidationCatchError){
        console.log('Error in the validateUpdateOTStatusInputs() function main catch block.',otStatusUpdateValidationCatchError);
        throw(otStatusUpdateValidationCatchError);
    }
}
module.exports.validateUpdateOTStatusInputs = validateUpdateOTStatusInputs;
/**
 * Function to update the overtime claim status and update the respective success response in the system log
 * @param {JSON} orgDb - Organization database connection object
 * @param {JSON} otStatusApprovalInputs - Input params
 * @returns {Number} - 0 | 1 - Return 0 when the status is not updated. Return 1 when the status is updated.
 */
async function updateOvertimeClaimApprovalStatus(orgDb,otStatusApprovalInputs){
    try{
        let { overtimeClaimId,newApprovalStatus,approvalComment,loginEmpTimeZoneCurrentDateTime,loginEmpId,userIp,formName,roleUpdate } = otStatusApprovalInputs;

        let statusApprovalDetails = {
            Approval_Status: newApprovalStatus,
            Approved_On: loginEmpTimeZoneCurrentDateTime,
            Approved_By: loginEmpId
        };
    
        /** Update the OT record status */
        let updateOTStatusResponse = await orgDb(ehrTables.overtime)
        .update(statusApprovalDetails)
        .where('Overtime_Claim_Id', overtimeClaimId)
        .where('Approval_Status', 'Approved')
        .then(async(otRecStatusUpdateRes)=>{
            /** If overtime status is updated */
            if(otRecStatusUpdateRes){
                if(approvalComment){
                    // form params to insert record in comment generation table
                    commentGenerationParams = {
                        Form_Id: formIds.overtime,
                        Emp_Comment: (approvalComment) ? approvalComment : null,
                        Approval_Status: newApprovalStatus,
                        Parent_Id: overtimeClaimId,
                        Employee_Id: loginEmpId,
                        Added_On: loginEmpTimeZoneCurrentDateTime
                    }

                    // Insert record in comment generation table
                    await commonLib.func.insertCommentsAndStatus(commentGenerationParams, orgDb);
                }

                // form inputs to update system log. Ex: Update 'formName' status - '[overtimeclaimids]' 
                let systemLogParams = {
                    action: roleUpdate,
                    userIp: userIp,
                    employeeId: loginEmpId,
                    formName: formName,
                    trackingColumn: 'status',
                    organizationDbConnection: orgDb,
                    uniqueId: overtimeClaimId
                };

                // call function createSystemLogActivities() to update system log activities
                await commonLib.func.createSystemLogActivities(systemLogParams);
            }else{
                console.log('Overtime claim status is not updated in the updateOvertimeClaimApprovalStatus() function for the overtime claim id ',overtimeClaimId);
            }
            return 1;
        })
        .catch(otRecStatusUpdateErr => {
            console.log('Error while updating the overtime claim status in updateOvertimeClaimApprovalStatus() function',otRecStatusUpdateErr);                                        
            return 0;
        });

        return updateOTStatusResponse;
    }catch(otStatusUpdateCatchError){
        console.log('Error in the updateOvertimeClaimApprovalStatus() function main catch block',otStatusUpdateCatchError);
        return 0;
    }
}
module.exports.updateOvertimeClaimApprovalStatus = updateOvertimeClaimApprovalStatus;

/**
 * Function to check whether payslip exist for the claim month or not
 * @param {Object} orgDb - Organization Database Connection
 * @param {NUmber} employeeId - Employee Id
 * @param {Date} employeeClaimedMonthStartDate - Additional wage claim month, year - calendar month start date 
 * and not based on the paycycle type
 * @returns {Number} - Return 1 when the addition wage claim month - payslip exist for the employee. Otherwise,
 * 0 will be returned
 * @throws {Object} - Throws when an error occured.
 */
async function validateEmployeeClaimMonth(orgDb,employeeId,employeeClaimedMonthStartDate){
    try{
        let employeeSalaryType = await commonLib.payroll.getEmployeeSalaryType(orgDb,employeeId);
        let employeeMaxPayslipMonth = await commonLib.func.maxPayslipMonth(orgDb,employeeId,employeeSalaryType);//2019-04-00

        let claimMonthPayslipExist = 0;
        if(employeeMaxPayslipMonth){
            employeeMaxPayslipMonth = employeeMaxPayslipMonth.split('-');
            let employeeMaxPayslipGeneratedDate = employeeMaxPayslipMonth[0]+'-'+employeeMaxPayslipMonth[1]+'-01';

            /** If the employee max generated payslip month(Ex:May,2021) is greater than the additional wage claim 
            * processing month(Ex:April,2021) then additional wage claim status should not be changed to completed.*/
            if(employeeMaxPayslipGeneratedDate >= employeeClaimedMonthStartDate){
                claimMonthPayslipExist = 1;
            }
        }

        return claimMonthPayslipExist;
    }
    catch(validateClaimMonthCatchError){
        console.log('Error in the validateEmployeeClaimMonth() function main catch block.',validateClaimMonthCatchError);
        throw validateClaimMonthCatchError;
    }
}
module.exports.validateEmployeeClaimMonth = validateEmployeeClaimMonth;