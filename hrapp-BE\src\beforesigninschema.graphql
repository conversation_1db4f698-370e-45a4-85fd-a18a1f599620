# defining custom data type
scalar Date 

type Query{
  checkOrganizationExists : checkOrganizationExistsResponse!
  domainDetails : domainDetailsResponse!
  getAllowUserSignin(
    emailId:String!
  ): getAllowUserSigninResponse!
}

type checkOrganizationExistsResponse{
  errorCode     : String,
  message       : String,
  isOrgExists   : <PERSON>olean
}

type domainDetailsResponse{
  errorCode : String,
  message   : String,
  domainDetails : String
}

type getAllowUserSigninResponse{
  errorCode:String,
  message:String,
  allowUserSignin:Boolean
}

schema {
  query: Query
}
