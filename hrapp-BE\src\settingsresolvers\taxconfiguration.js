// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require common constant files
const constants = require('../../common/appconstants');
// Organization database connection
const knex = require('knex');
// require table alias
const tables = require('../../common/tablealias');
// variable declarations

let errResult,validationError,systemLogParams = {};
let organizationDbConnection, connection =  '';
let ehrTables = tables.ehrTables;
let checkRights = false;

// resolver definition
const resolvers = {
    Query: {
        retrieveTaxConfiguration: async (parent, args, context, info) => {
            console.log('Inside retrieveTaxConfiguration.js function.');
            try{
                // Append the database name in connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                // to check form has view access rights
                checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, context.Employee_Id, constants.formName.tax, constants.roles.roleView);
                //check view rights
                if (checkRights === true) {
                    // get tax configuration details
                    return(
                        organizationDbConnection(ehrTables.taxconfiguration)                        
                        .select('Tax_Configuration_Id','Tax_Regime','Round_Off_Tds')
                        .then(taxconfiguration =>{
                            // check if tax config details exists
                            if(taxconfiguration && taxconfiguration[0]) {
                                return { errorCode: '', message: 'Tax configuration details retrieved successfully', taxConfigurationId : taxconfiguration[0].Tax_Configuration_Id , taxRegime: taxconfiguration[0].Tax_Regime , tdsAmountRoundOff: taxconfiguration[0].Round_Off_Tds}                                                      
                            }
                            else {
                                return { errorCode: '', message: 'Tax configuration details retrieved successfully', taxConfigurationId: null, taxRegime: '' , tdsAmountRoundOff: null}                                                      
                            }
                        }).catch(function (retrieveTaxConfigurationError){
                            console.log('Error in retrieveTaxConfiguration() function .catch block',retrieveTaxConfigurationError);
                            errResult = commonLib.func.getError(retrieveTaxConfigurationError, 'SE0107');
                            throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, taxConfigurationId: null, taxRegime: '', tdsAmountRoundOff :null }));
                        })
                        // close db connection
                        .finally(() => {
                            organizationDbConnection.destroy();
                        })
                    )
                } else if (checkRights === false) {
                    // throw error if view rights is not exists
                    throw('_DB0100');
                } else {
                    // throw error
                    throw(checkRights);
                }
            } catch (retrieveTaxConfigurationError) {
                console.log('Error in retrieveTaxConfiguration() function main catch block',retrieveTaxConfigurationError);
                // access denied error
                if (retrieveTaxConfigurationError === '_DB0100') {
                    errResult = commonLib.func.getError('', '_DB0100');
                } else {
                    errResult = commonLib.func.getError('', 'SE0005');
                }
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                // return response
                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, taxConfigurationId: null, taxRegime: '', tdsAmountRoundOff :null }));
            }
        }
    },
    Mutation : {
        // update the tax configuration details
        updateTaxConfiguration: async (parent, args, context, info) => {
            console.log('Inside updateTaxConfiguration.js function.');
            try{
                // Append the database name in connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                // get employeeId from context object(Taken from token itself)
                let employeeId = context.Employee_Id; 

                // to check form access rights
                checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, constants.formName.tax, constants.roles.roleUpdate);
                //check update rights exists
                if (checkRights === true) {  
                    // validate the inputs
                    validationError = {}; 
                    // validate tax regime
                    if(args.taxRegime) {
                        if(args.taxRegime !== 'Old Regime' && args.taxRegime !== 'New Regime') {
                            validationError['IVE0040'] = commonLib.func.getError('', 'IVE0040').message2; //Invalid Tax Regime
                        }
                    }
                    else {
                        validationError['IVE0040'] = commonLib.func.getError('', 'IVE0040').message1; //Tax regime is required
                    }

                    // validate TDS Round Off payment
                    if(args.tdsAmountRoundOff !== 0 && args.tdsAmountRoundOff !== 1) {
                        validationError['IVE0041'] = commonLib.func.getError('', 'IVE0041').message; //TDS Round Off is required
                    }
                    
                    // check validation error exists or not
                    if (Object.keys(validationError).length === 0){
                        let isPayslipExistForFY = await commonLib.payroll.validatePayslipExistForFY(context.Org_Code,organizationDbConnection);
                        
                        if(isPayslipExistForFY == 1){
                            throw 'SE0015';
                        }else{
                            return(
                                organizationDbConnection
                                .transaction(function(trx){                            
                                    // retrieve tax config data
                                    return (
                                        organizationDbConnection(ehrTables.taxconfiguration)
                                        .select('Lock_Flag')
                                        .transacting(trx)
                                        .then(taxConfig =>{
                                            // check if data exists
                                            if(taxConfig && taxConfig[0]) {
                                                // check if lock flag is set by any other session
                                                if(taxConfig[0].Lock_Flag === 0 || taxConfig[0].Lock_Flag === employeeId ) {
                                                    // form inputs to update system log activities commonly
                                                    systemLogParams = {
                                                        action: constants.systemLogs.roleUpdate,
                                                        userIp: context.User_Ip,
                                                        employeeId: employeeId,
                                                        formName: constants.formName.tax,
                                                        trackingColumn: 'Tax Configuration',
                                                        organizationDbConnection: organizationDbConnection,
                                                        uniqueId: args.taxConfigurationId
                                                    }
                                                    // form input params
                                                    let inputParams = {
                                                        Tax_Regime: args.taxRegime,
                                                        Round_Off_Tds: args.tdsAmountRoundOff
                                                    };

                                                    // update the tax config data
                                                    return(
                                                        organizationDbConnection(ehrTables.taxconfiguration)
                                                        .update(inputParams)
                                                        .where('Tax_Configuration_Id',args.taxConfigurationId)
                                                        .transacting(trx)
                                                        .then(async (updatedTaxConfig) =>{
                                                            // call function createSystemLogActivities() to update system log activities
                                                            await commonLib.func.createSystemLogActivities(systemLogParams);
                                                            // return success response to UI
                                                            return { errorCode: '', message: 'Tax Configuration updated successfully', validationError: '' }
                                                        })
                                                    )
                                                }
                                                else {
                                                    // throw error if lock flag is set already
                                                    throw('_EC0003');
                                                }                                        
                                            }
                                            else {
                                                // throw error if record is doesnot exists
                                                throw('_EC0001');
                                            }
                                        })   
                                    )                             
                                })
                                //return the success result to user
                                .then(function (result) {
                                    return result;
                                })
                                //catch db-connectivity errors
                                .catch(function (updateTaxConfigInsideCatchError) {
                                    console.log("Error in updateTaxConfiguration() function .catch block", updateTaxConfigInsideCatchError);
                                    if (updateTaxConfigInsideCatchError === '_EC0003' || updateTaxConfigInsideCatchError === '_EC0001') {
                                        errResult = commonLib.func.getError('', updateTaxConfigInsideCatchError);
                                    } else {
                                        errResult = commonLib.func.getError(updateTaxConfigInsideCatchError, 'SE0108');
                                    }
                                    // return response to UI
                                    throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, validationError: '' }));
                                })
                                /**close db connection */
                                .finally(() => {
                                    organizationDbConnection.destroy();
                                })
                            )
                        }
                    } else {
                        // throw validation error
                        throw('IVE0000');
                    }
                }  else {
                    // throw error if edit rights is not exists
                    throw('_DB0102');
                }
            } catch (updateTaxConfigMainCatchError) {
                console.log("Error in updateTaxConfiguration() function main catch block", updateTaxConfigMainCatchError);
                let mainCatchErrorCodes = ['_DB0102','SE0015'];
                if (mainCatchErrorCodes.includes(updateTaxConfigMainCatchError)) {
                    errResult = commonLib.func.getError('', updateTaxConfigMainCatchError);
                }else if (updateTaxConfigMainCatchError === 'IVE0000') {
                    console.log('Validation error in updateTaxConfiguration()',validationError);
                    errResult = commonLib.func.getError('', 'IVE0000');
                    // return response
                    throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, validationError:validationError }));
                } 
                else {
                    // unhandled error
                    errResult = commonLib.func.getError('', 'SE0006');
                }
                // destroy database connection if exists
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                // return response
                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, validationError : '' }));
            }
        },
        
    }
};

exports.resolvers = resolvers;