// require resolver files
const checkOrgExists = require('./beforesigninresolvers/checkOrganizationExist');
const domainDetails = require('./beforesigninresolvers/domainDetails');
const getAllowUserSignin = require('./beforesigninresolvers/getAllowUserSignin');

// Define resolver
const resolvers = {
    Query: Object.assign({},
        checkOrgExists.resolvers.Query,
        domainDetails.resolvers.Query,
        getAllowUserSignin.resolvers.Query
    )
}
exports.resolvers = resolvers;