// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require common constant files
const { formIds, systemLogs } = require('../../../common/appconstants');
//require moment
const moment = require('moment');
//require input validator
const { validateCommonRuleInput } = require('../../../common/commonFunction');

//function to add/update the shift rotation
let organizationDbConnection, appManagerDbConnection;
let inputValidationError = {}
module.exports.addUpdateShiftRotation = async (parent, args, context, info) => {
    try {
        console.log("Inside addUpdateShiftRotation function.")

        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        appManagerDbConnection = knex(context.connection.AppManagerDb);

        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, null, null, 'ui', null, formIds.shiftRotation, { appManagerDbConnection: appManagerDbConnection, orgCode: context.Org_Code });
        if (!checkRights || (args.Rotation_Id && !checkRights.Role_Update) || (!args.Rotation_Id && !checkRights.Role_Add)) {
            throw args.Rotation_Id ? '_DB0102' : '_DB0101';
        }

        //Input Validation for shift rotation
        let fieldValidations = {
            'Scheduler_Name': 'IVE0484',
            'Leave_Entitlement_Per_Roster_Day': 'IVE0485',
            'Repeat_Count': 'IVE0486',
            'Applicable_Period': 'IVE0487'
        }

        inputValidationError = await validateCommonRuleInput( args, fieldValidations);

        if (Object.keys(inputValidationError).length) {
            throw 'IVE0000'
        }

        // validate shift name uniqueness
        const alreadyExists = await organizationDbConnection(ehrTables.shiftRotation)
            .select('Scheduler_Name')
            .where('Scheduler_Name', args.Scheduler_Name)
            .whereNot('Rotation_Id', args.Rotation_Id || null)
            .first();

        if (alreadyExists) throw 'SS0104';

        // prepare shift rotation data
        const shiftRotationData = {
            Scheduler_Name: args.Scheduler_Name,
            Repeat_Schedule: args.Repeat_Schedule,
            Repeat_Count: args.Repeat_Count,
            Enable_Roster_Leave: args.Enable_Roster_Leave,
            Leave_Entitlement_Per_Roster_Day: args.Leave_Entitlement_Per_Roster_Day,
            Leave_Replenishment_Period: args.Leave_Replenishment_Period,
            LeaveType_Id: args.LeaveType_Id,
            ...(args.Rotation_Id
                ? { Updated_By: employeeId, Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') }
                : { Added_By: employeeId, Added_on: moment().utc().format('YYYY-MM-DD HH:mm:ss') })
        };


        await organizationDbConnection.transaction(async (trx) => {
            if (args.Rotation_Id) {
                const existingRecord = await organizationDbConnection(ehrTables.shiftRotation)
                    .where('Rotation_Id', args.Rotation_Id)
                    .first();

                if (!existingRecord) throw 'SS0105';

                await validateShiftRotationAssociation(organizationDbConnection, args.Rotation_Id);

                // delete and insert new records in a transaction
                await Promise.all([
                    organizationDbConnection(ehrTables.shiftRotation).where('Rotation_Id', args.Rotation_Id).transacting(trx).del(),
                    organizationDbConnection(ehrTables.shiftRotationSchedule).where('Rotation_Id', args.Rotation_Id).transacting(trx).del()
                ]);

                const [shiftRotationId] = await organizationDbConnection(ehrTables.shiftRotation)
                    .insert(shiftRotationData)
                    .transacting(trx);

                const shiftRotationScheduleData = mapShiftRotationSchedule(args.Shift_Rotation_Schedule, shiftRotationId);
                await organizationDbConnection(ehrTables.shiftRotationSchedule)
                    .insert(shiftRotationScheduleData)
                    .transacting(trx);
            } else {
                const [shiftRotationId] = await organizationDbConnection(ehrTables.shiftRotation)
                    .insert(shiftRotationData)
                    .transacting(trx);

                const shiftRotationScheduleData = mapShiftRotationSchedule(args.Shift_Rotation_Schedule, shiftRotationId);
                await organizationDbConnection(ehrTables.shiftRotationSchedule)
                    .insert(shiftRotationScheduleData)
                    .transacting(trx);
            }
        });

        // Add System Log
        let systemLogParams = {
            action: args.Rotation_Id ? systemLogs.roleUpdate : systemLogs.roleAdd,
            userIp: context.User_Ip,
            employeeId: employeeId,
            formName: checkRights.Custom_Form_Name,
            trackingColumn: 'Scheduler_Name',
            organizationDbConnection: organizationDbConnection,
            uniqueId: args.Scheduler_Name,
            changedData: args
        };

        //Call function to add the system log
        await commonLib.func.createSystemLogActivities(systemLogParams);

        return { errorCode: "", message: `${checkRights.Custom_Form_Name} ${args.Rotation_Id ? 'updated' : 'added'} successfully.` };

    }
    catch (e) {
        console.log('Error in addUpdateShiftRotation function main catch block.', e);
        if (e === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateShiftRotation function - ', inputValidationError);
            // return response
            throw new UserInputError(errResult.message, {
                inputValidationError: inputValidationError,
            });
        } else {
            let errResult = commonLib.func.getError(e, 'SS0005');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
    finally {
        //Destroy DB connection
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
}

// helper function to map shift rotation schedule
function mapShiftRotationSchedule(scheduleArray, rotationId) {
    try{
        return scheduleArray.map(item => ({
            Rotation_Id: rotationId,
            ShiftType_Id: item.ShiftType_Id,
            Rotation_Level: item.Rotation_Level,
            Applicable_Period: item.Applicable_Period,
            Period_Unit: item.Period_Unit
        }));
    }catch(err){
        console.log('Error in mapShiftRotationSchedule', err)
        throw err
    }
}


async function validateShiftRotationAssociation(organizationDbConnection, rotationId) {
    try {
        let shiftEmpMapping = await organizationDbConnection(ehrTables.shiftEmpMapping)
            .select('Rotation_Id')
            .where('Rotation_Id', rotationId)

        if (shiftEmpMapping && shiftEmpMapping.length) {
            throw 'SS0106'
        }
    }
    catch (err) {
        console.log('Error in validateShiftRotationAssociation', err)
        throw err
    }
}