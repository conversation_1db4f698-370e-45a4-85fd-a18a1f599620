{"securityGroupIds": ["sg-06e06ee52057fb09f"], "subnetIds": ["subnet-023ff1fb8431b273f", "subnet-09dd9cf2a9239643c"], "dbSecretName": "PROD/CANNY/PGACCESS", "region": "ap-south-1", "hrappProfileBucket": "s3.images.cannyhr.com", "lambdaRole": "arn:aws:iam::378423228887:role/lambdaFullAccess", "dbPrefix": "cannyhr_", "domainName": "cannyhr", "authorizerARN": "arn:aws:lambda:ap-south-1:378423228887:function:ATS-cannyhr-firebaseauthorizer", "customDomainName": "api.cannyhr.com", "logoBucket": "s3.logos.cannyhr.com", "screenshotsBucket": "", "firebaseApiKey": "AIzaSyAz3TaOCY3KlDFyrNZ_feZe9dENgG1rU7g", "emailFrom": "<EMAIL>", "sesRegion": "us-east-1", "webAddress": ".com", "desktopClientURL": "", "pmsEmailNotificationArn": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhr-pmsReminderNotification", "batchEmailNotificationArn": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhr-batchEmailNotification", "asynchronousreportBucket": "asynchronousreport.cannyhr.com", "generateReportAsyncStepFunction": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhr-generateReportAsyncStepFunction", "updateRosterLeaveBatch": "arn:aws:lambda:ap-south-1:378423228887:function:BATCHPROCESSING-cannyhr-updateRosterLeaveBatch", "resourceArnPrefix": "arn:aws:lambda:ap-south-1:378423228887:function:HRAPPBACKEND-cannyhr"}