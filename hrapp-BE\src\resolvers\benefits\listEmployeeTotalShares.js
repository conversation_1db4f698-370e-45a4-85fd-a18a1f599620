/** ESOP list - Get all the employee total shares list based on the visibility status and login employee id only 
 * when the login employee has the benefits admin access and ESOP form - view access. */
module.exports.listEmployeeTotalShares = async (parent, args, context, info)=>{
    console.log('Inside the listEmployeeTotalShares() function.');
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // require knex for database connection
    const knex = require('knex');
    // require apollo server to return error message
    const { ApolloError } = require('apollo-server-lambda');
    // require common constant files
    const { formName,defaultValues } = require('../../../common/appconstants');
    // require table names
    const { ehrTables } = require('../../../common/tablealias');
    // variable declarations
    let errResult ={};
    let organizationDbConnection = '';

    try{
        // get the organization database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let logInEmpId = context.Employee_Id;
        // Check ESOP form view access rights exist for employee or not
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.esop, '', 'UI');
        if(Object.keys(checkRights).length>0 && checkRights.Role_View === 1) {
            // Check benefits admin form - update access rights exist for employee or not
            let checkBenefitsAdminRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.benefitsAdmin, '', 'UI');
            if(Object.keys(checkBenefitsAdminRights).length>0 && checkBenefitsAdminRights.Role_Update === 1) {
                return (
                organizationDbConnection
                .transaction(function (trx) {
                    //Get the esop benefits configuration
                    return(
                    organizationDbConnection(ehrTables.benefitsConfiguration+ ' as BC')
                    .select('BC.Value_Of_Share')
                    .transacting(trx)
                    .then(async (getBenefitsConfig) =>{
                        if(getBenefitsConfig && getBenefitsConfig.length > 0){
                            let valueOfShare = getBenefitsConfig[0].Value_Of_Share;

                            // Check super admin form - optional choice access rights exist for employee or not
                            let checkSuperAdminRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.superAdmin, '', 'UI');
                            let isLoginEmployeeSuperAdmin = 0;
                            if(Object.keys(checkSuperAdminRights).length>0 && checkSuperAdminRights.Role_Optional_Choice === 1) {
                                isLoginEmployeeSuperAdmin = 1;
                            }

                            let empVestedShare;
                            /** If the login employee is having super admin access then they can view super admin-visibility status records */
                            if(isLoginEmployeeSuperAdmin === 1){
                                empVestedShare = organizationDbConnection.raw(`(SELECT SUM(ESVH.Vested_Share) FROM employee_share_vest_history AS ESVH
                                WHERE ESVH.Employee_Allocated_Share_Id IN
                                  (SELECT Employee_Allocated_Share_Id FROM employee_share_details AS ESD2
                                   WHERE ESD2.Employee_Id = ESD.Employee_Id
                                   AND (ESD2.Added_By = ? OR ESD2.Visibility = 'Super Admins'))) as Employee_Total_Vested_Shares`, [logInEmpId]);
                            }else{
                                empVestedShare = organizationDbConnection.raw(`(SELECT SUM(ESVH.Vested_Share) FROM employee_share_vest_history AS ESVH
                                WHERE ESVH.Employee_Allocated_Share_Id IN (SELECT Employee_Allocated_Share_Id FROM employee_share_details AS ESD2 WHERE 
                                ESD2.Employee_Id = ESD.Employee_Id AND ESD2.Added_By = ?)) as Employee_Total_Vested_Shares`, [logInEmpId]);
                            }

                            let empVestedShareValue;
                            /** If the login employee is having super admin access then they can view super admin-visibility status records */
                            if(isLoginEmployeeSuperAdmin === 1){
                                empVestedShareValue = organizationDbConnection.raw(`(SELECT SUM(ESVH.Vested_Share * ?) FROM employee_share_vest_history AS ESVH
                                WHERE ESVH.Employee_Allocated_Share_Id IN (SELECT Employee_Allocated_Share_Id FROM employee_share_details AS ESD2 WHERE ESD2.Employee_Id = ESD.Employee_Id 
                                AND (ESD2.Added_By = ? OR ESD2.Visibility = 'Super Admins'))) as Employee_Total_Vested_Shares_Value`, [valueOfShare, logInEmpId]);
                            }else{
                                empVestedShareValue = organizationDbConnection.raw(`(SELECT SUM(ESVH.Vested_Share * ?) FROM employee_share_vest_history AS ESVH
                                WHERE ESVH.Employee_Allocated_Share_Id IN (SELECT Employee_Allocated_Share_Id FROM employee_share_details AS ESD2 WHERE ESD2.Employee_Id = ESD.Employee_Id 
                                AND (ESD2.Added_By = ?))) as Employee_Total_Vested_Shares_Value`, [valueOfShare, logInEmpId]);
                            }

                            let allEmployeesSharesListQuery = organizationDbConnection(ehrTables.employeeShareDetails+ ' as ESD')
                                .select(
                                    'ESD.Employee_Id','EJ.ESOP_Status','ED.Designation_Name',
                                    organizationDbConnection.raw('SUM(ESD.Allocated_Shares) as Employee_Total_Allocated_Shares'),
                                    organizationDbConnection.raw("SUM(ESD.Allocated_Shares * ?) as Employee_Total_Allocated_Shares_Value", [valueOfShare]),
                                    empVestedShare,empVestedShareValue,
                                    organizationDbConnection.raw("CONCAT_WS(' ',EP.Emp_First_Name,EP.Emp_Middle_Name, EP.Emp_Last_Name) as Employee_Name"),
                                    organizationDbConnection.raw('(CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE EJ.Employee_Id END) as User_Defined_EmpId')
                                )
                                .innerJoin(ehrTables.empJob+' as EJ','EJ.Employee_Id','ESD.Employee_Id')
                                .innerJoin(ehrTables.empPersonalInfo+' as EP','EJ.Employee_Id','EP.Employee_Id')
                                .innerJoin(ehrTables.designation+' as ED','EJ.Designation_Id','ED.Designation_Id')
                                .where(qb => {
                                    qb.where('ESD.Added_By', logInEmpId)
                                })//where condition to get the shares details which are added by the login employee id

                            /** If the login employee is having super admin access then they can view super admin-visibility status records */
                            if(isLoginEmployeeSuperAdmin === 1){
                                allEmployeesSharesListQuery = allEmployeesSharesListQuery
                                .orWhere(qb => {
                                    qb.where('ESD.Visibility', defaultValues.benefitsSuperAdminsVisibilityStatus)
                                });//where condition to get all the super admin visibility status - shares details
                            }

                            /** Get all the employees share and details for the benefits admin. */
                            return(
                            allEmployeesSharesListQuery
                            .groupBy('ESD.Employee_Id')
                            .transacting(trx)
                            .then((employeeMainListDetails) =>{
                                return { errorCode: '', message: 'Employee total shares retrieved successfully.', employeeTotalSharesList: employeeMainListDetails };
                            })
                            )
                        }else{
                            throw('BES0006');
                        }
                    })
                    )
                })
                .catch(function (listEmployeeTotalSharesInsideCatchError) {
                    console.log('Error in listEmployeeTotalShares() function .catch block', listEmployeeTotalSharesInsideCatchError);
                    errResult = commonLib.func.getError(listEmployeeTotalSharesInsideCatchError, 'BES0005');
                    throw new ApolloError(errResult.message,errResult.code);
                })
                // close db connection
                .finally(() => {
                    organizationDbConnection.destroy();
                })
                )
            }else{
                throw ('_DB0109');
            }
        }else{
            throw ('_DB0100');
        }
    }catch(listEmployeeTotalSharesCatchError){
        console.log('Error in the listEmployeeTotalShares() function main catch block.',listEmployeeTotalSharesCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(listEmployeeTotalSharesCatchError, 'BES0105');
        throw new ApolloError(errResult.message,errResult.code);
    }
};