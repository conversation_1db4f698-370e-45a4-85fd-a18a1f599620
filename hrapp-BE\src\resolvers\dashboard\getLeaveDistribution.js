/** Return the current date leave details for admin and employee admin to present in the team dashboard tab */
module.exports.getLeaveDistribution = async (parent, args, context, info) =>{
    console.log('Inside the getLeaveDistribution() function.');
    const { ehrTables } = require('../../../common/tablealias');
    const moment = require('moment-timezone');
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    const { formName, roles } = require('../../../common/appconstants');
    const knex = require('knex');
    const orgDb = knex(context.connection.OrganizationDb);

    try {
        let logInEmpId = context.Employee_Id;
        let timezone = await commonLib.func.getEmployeeTimeZone(logInEmpId, orgDb, 0);
        let todayDate = moment().tz(timezone).format('YYYY-MM-DD');

        let isAdmin = await commonLib.func.checkEmployeeAccessRights(orgDb, logInEmpId, formName.admin, roles.roleUpdate);
        let isEmployeeAdmin = await commonLib.func.checkEmployeeAccessRights(orgDb, logInEmpId, formName.employeeAdmin, roles.roleUpdate);

        if(isAdmin || isEmployeeAdmin)
        {
            let leaveDistributionQry = orgDb.select('LT.Leave_Name as leaveName', orgDb.raw('COUNT(EL.LeaveType_Id) as leaveCount'),
                                                orgDb.raw('GROUP_CONCAT(EL.Employee_Id) as Employee_Ids'))
                                                .from(ehrTables.empLeaves + ' as EL')
                                                .leftJoin(ehrTables.leavetype +' as LT', 'LT.LeaveType_Id', 'EL.LeaveType_Id')
                                                .where('EL.Start_Date', '<=', todayDate)
                                                .where('EL.End_Date', '>=', todayDate)
                                                .whereIn('EL.Approval_Status',['Applied', 'Approved', 'Cancel Applied', 'Returned'])
                                                .groupBy('EL.LeaveType_Id');

            let leaveDistribution = await leaveDistributionQry.select();

            let totalLeaveCount = 0;
            for(leave of leaveDistribution)
            {
                totalLeaveCount += leave.leaveCount;
                //convert string to array. Example: convert '4,2' to ['4','2']
                let currentDateLeaveEmployeeIds = (leave.Employee_Ids) ? leave.Employee_Ids.split(',') : [];
                if(currentDateLeaveEmployeeIds.length > 0){
                    let employeeFilterJSON = {
                        employeeId: currentDateLeaveEmployeeIds
                    };
                    // get the employee name and user defined employee id for the leave employee ids
                    let leaveTypeEmployeeDetails = await commonLib.func.getAllEmployeeDetails(orgDb,'getemployeepersonalandjobdetails',null,null,null,null,employeeFilterJSON,null)
                    leave.currentDateLeaveEmployeeDetails = leaveTypeEmployeeDetails;
                }else{
                    leave.currentDateLeaveEmployeeDetails = [];
                }
            }

            return {
                errorCode: '',
                message: 'Leave distribution retrieved successfully',
                totalLeaveCount,
                leaveDistribution
            }
        }
        else
        {
            console.log("Login employee is not admin and employee admin.");
            let errResult = commonLib.func.getError('', '_DB0100');
            return {
                errorCode: errResult.code,
                message: errResult.message,
                totalLeaveCount: null,
                leaveDistribution: null
            }
        }
    } catch(err) {
        console.log("Error in getLeaveDistribution() function main catch block.", err);
        let errResult = commonLib.func.getError('', 'DB0029');
        return {
            errorCode: errResult.code,
            message: errResult.message,
            totalLeaveCount: null,
            leaveDistribution: null
        }
    }
}