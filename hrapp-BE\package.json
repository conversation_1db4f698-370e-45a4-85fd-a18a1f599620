{"name": "hrapp<PERSON>", "version": "1.0.0", "description": "hrapp node modules", "main": "handler.js", "dependencies": {"@cksiva09/hrapp-corelib": "git+https://cksiva09:<EMAIL>/cksiva09/hrapp-corelib.git", "@cksiva09/validationlib": "^1.3.67", "apollo-server": "^2.4.8", "apollo-server-lambda": "^2.15.0", "aws-sdk": "^2.1692.0", "axios": "^0.21.1", "graphql": "^14.2.0", "is-ip": "^3.1.0", "knex": "^2.5.1", "moment": "^2.24.0", "moment-timezone": "^0.5.28", "mysql": "^2.18.1", "mysql2": "^3.14.0", "qs": "^6.13.1", "serverless-domain-manager": "^7.1.2", "serverless-prune-plugin": "^2.0.2", "serverless-step-functions": "^3.17.0"}, "devDependencies": {"eslint": "^6.5.1", "serverless-offline": "^13.2.0"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "sls offline --stage dev --region ap-south-1 --reload<PERSON>andler", "local": "sls offline --stage local --region ap-south-1 --reload<PERSON>andler"}, "author": "<PERSON><PERSON>", "license": "ISC"}