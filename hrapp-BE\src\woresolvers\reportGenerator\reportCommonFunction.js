//Require table alias
const { ehrTables } = require('../../../common/tablealias');
//Require moment package
const moment=require('moment');
// require package for creating excel
const {s3FileUpload}= require('../../../common/appconstants')
const fs = require('fs');
const { CommonLib } = require('@cksiva09/hrapp-corelib');
const{checkAccessGetEmployeeIdsForReports}=require('../../../common/commonFunction')



async  function callFunctionBasedOnInput(organizationDbConnection,args,reportDetails,logInEmpId,orgCode,isAdmin,isManager,historyId,requestDateTime)
{
    let updateParams={
        Aws_Path:'',
        Presigned_Url:'',
        Report_Status:"Failed",
        Email_Status:"Failed"
    };
    try{
        console.log("Inside callFunctionBasedOnInput function.")
        // check whether logged employee has access to reports form.Based on access get the employee ids
        let {employeeIdsArray}=await checkAccessGetEmployeeIdsForReports(organizationDbConnection,logInEmpId,args.filters.employeeIdsArray,isAdmin,isManager);
        // check data exist or not
        if(employeeIdsArray.length>0)
        {
            let functionName=reportDetails['Function_Name'];
            let reportData="";
            if(functionName==="getFileAndWebEventSumaryReport")
            {
                reportData= await getFileAndWebEventSumaryReport(organizationDbConnection,args.filters.startDate,args.filters.endDate,args.reportHeaders,args.orderBy,employeeIdsArray);
            }
            if(reportDetails['Report_Type']==='Synchronous')
            {
                return reportData;
            }
            else{
                let employeeDetails=await CommonLib.func.getDataFromTableAccordingToStatus(organizationDbConnection,ehrTables.empJob,"Employee_Id",logInEmpId);
              
                if(employeeDetails && employeeDetails.length>0)
                {
                    let reportName=reportDetails['Rep_Title'];
                    let logoPath="";
                    let signedUrlExpireSeconds=604800;
                    let orgDetails=await CommonLib.func.getOrgDetails(orgCode, organizationDbConnection,signedUrlExpireSeconds);
                    logoPath = orgDetails.logoPath ?orgDetails.logoPath : "";
                    if(reportData.length>0)
                    {   
                        let unixTime=moment().unix();
                        let reportHeaders=[];
                        let contentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                        let reportFormat=args.reportFormat;
                        reportName=reportName+"_"+unixTime;
                        let reportDataObject=reportData[0];
                        let reportKeys=Object.keys(reportDataObject);
                        for(let i=0;i<reportKeys.length;i++)
                        {
                            let header=await getHeaderBasedOnKey(reportKeys[i]);
                            reportHeaders.push({key:reportKeys[i],header:header});                
                        }
                        let exportToExcellResponse=await exportToExcell(reportData,reportHeaders,reportName,reportDetails['Rep_Title'],reportFormat);
                        if(exportToExcellResponse)
                        {
                            if(reportFormat==='csv')
                            {
                                contentType="text/csv";
                            }
                            let fileStream=fs.createReadStream('/tmp/'+reportName+"."+reportFormat);
                            let currentDate=moment.utc().format("YYYY-MM-DD")
                            let path=orgCode+'/'+currentDate+"/"+logInEmpId+"/"+reportName+"."+reportFormat;
                            await uploadFileToS3Bucket(path,contentType,fileStream);
                            let fileUrl=await CommonLib.func.getFileURL(process.env.region,process.env.asynchronousreportBucket,path,signedUrlExpireSeconds)
                            await removeFile('/tmp/'+reportName+"."+reportFormat);
                            if(fileUrl.length>0)
                            {   
                                updateParams['Aws_Path']=path;
                                updateParams['Presigned_Url']=fileUrl;
                                updateParams['Report_Status']="Success";
                                if(employeeDetails[0]['Emp_Email'])
                                {
                                    let employeeZone=await CommonLib.func.getEmployeeTimeZone(logInEmpId,organizationDbConnection);
                                    // find the offset for the zone
                                    let offSet = moment.tz(moment(), employeeZone).utcOffset();
                                    let requestTimeWithZone= moment.utc(requestDateTime).utcOffset(offSet).format('YYYY-MM-DD HH:mm:ss');
                                    let htmlTable=await createHtmlDesignForReport(reportName,reportData.length,requestTimeWithZone,fileUrl)
                                    await formNotificationParamAndSendEmail(htmlTable,logoPath,employeeDetails[0]['Emp_Email'],'Your requested report is available for download.');
                                    updateParams['Email_Status']="Success";
                                }
                                else{
                                    console.log("Email not found.");
                                    throw("PBP0106");
                                }  
                                await CommonLib.func.updateTableBasedOnCondition(organizationDbConnection,ehrTables.reportHistory,updateParams,"History_Id",historyId);
                                return true;
                            }
                            else{
                                console.log("Error while getting file url.")
                                throw("PBP0110");
                            }
                        }
                        else{
                            console.log("Error in exportToExcell function from else block",e);
                            throw("HBLC0104");
                        }
                    }
                    else{
                        let htmlTable=`<div style="font-weight:bold;font-size:17px;color:#260029; padding: 15px;">Sorry your report requset for ${reportName} requested for ${args.filters.startDate} to ${args.filters.endDate} has no records.
                        </div>`;
                        await formNotificationParamAndSendEmail(htmlTable,logoPath,"<EMAIL>",'Your requested report response.');
                    }
                } 
                else{
                    console.log("Error while getting employee details.");
                    throw('DB0017') 
                }
            }
        }
        else{
            console.log('No active employee found.');
            throw 'DB0017';
        }

    }
    catch(e)
    {
        console.log("Error in callFunctionBasedOnInput function main block",e);
        let errResult = CommonLib.func.getError(e, 'HBLC0101');
        updateParams['Error_Message']=errResult.message;
        if(reportDetails['Report_Type']==='Asynchronous')
        {
            await CommonLib.func.updateTableBasedOnCondition(organizationDbConnection,ehrTables.reportHistory,updateParams,"History_Id",historyId);
        }
        return false;
    }
}

async function getFileAndWebEventSumaryReport(organizationDbConnection,startDate,endDate,reportHeaders,orderBy,employeeIdsArray)
{
    try{
        console.log("Inside getFileAndWebEventSumaryReport function.")
        let subQuery=organizationDbConnection(ehrTables.fileAndWebEventSummary)
        let headerArray=reportHeaders.split(",");
        for(let i=0;i<headerArray.length;i++)
        {
            let headerAlias=await getFileAndWebEventSummaryAliasBasedOnFilter(headerArray[i]);
            if(headerAlias.includes('('))
            {
                subQuery=subQuery.select(organizationDbConnection.raw(headerAlias));
            }
            else{
                subQuery=subQuery.select(headerAlias)
            }
        }

        let orderArray=orderBy.split(",");
        for(let i=0;i<orderArray.length;i++)
        {
            let orderAlias=await getFileAndWebEventSummaryAliasBasedOnFilter(orderArray[i]);
            orderAlias=orderAlias.substring(0, orderAlias.indexOf(' '));
            subQuery=subQuery.orderBy(orderAlias)
        }
        
        // get the app summary data for the given date along with the employee details
        return(
            subQuery            
            .from(ehrTables.fileAndWebEventSummary + " as FES")
            .leftJoin(ehrTables.empPersonalInfo + ' as EPI', 'FES.Employee_Id', 'EPI.Employee_Id')
            .leftJoin(ehrTables.empJob + ' as EJ', 'EPI.Employee_Id', 'EJ.Employee_Id')
            .leftJoin(ehrTables.empPersonalInfo + ' as EPI1','EJ.Manager_Id','EPI1.Employee_Id')
            .leftJoin(ehrTables.assetManagement + " as AM",'AM.Employee_Id','FES.Employee_Id')
            .whereBetween('FES.Activity_Date',[startDate,endDate])
            .whereNotNull('AM.Employee_Id')
            .whereIn('FES.Employee_Id', employeeIdsArray)
            .groupBy('FES.Employee_Id','FES.Activity_Date')
            .then(data=>{
                return data;
            })
            .catch(e=>{
                console.log('Error in getFileAndWebEventSumaryReport() function .catch block.',e);
                throw 'EM0258';
            })
        );
    }
    catch(e)
    {
        console.log('Error in getFileAndWebEventSumaryReport() function .catch block.',e);
        throw('EM0258');
    }
}

async function exportToExcell(reportData,reportHeaders,exportFileName,sheetName,reportFormat)
{
    try{
        const {Workbook} = require('exceljs');

        // Create workbook and worksheet
        let workbook = new Workbook();
        let worksheet = workbook.addWorksheet(sheetName);
        // form the excel header
        worksheet.columns = reportHeaders;
        let headerRowNumber = [1];
        let headerColor = "92CDDC"; // default color
        worksheet.addRows(reportData);
        // header styles (background and border)
        if (headerRowNumber && headerRowNumber.length > 0) {
            for (let headerRow of headerRowNumber) {
                worksheet.getRow(headerRow).eachCell((cell) => {
                    cell.fill = {
                        type: "pattern",
                        pattern: "solid",
                        fgColor: { argb: headerColor },
                    };
                    cell.alignment = { vertical: "middle", horizontal: "center" };
                    cell.font = {
                        name: "Calibri",
                        size: 11,
                        bold: true,
                    };
                    cell.border = {
                        top: { style: "thin" },
                        left: { style: "thin" },
                        bottom: { style: "thin" },
                        right: { style: "thin" },
                    };
                });

                // freeze column and row
                worksheet.views = [
                {
                    state: "frozen",
                    xSplit: 3,
                    ySplit: headerRowNumber.length === 1 ? headerRowNumber[0] : 0,
                },
                ];
            }

            // auto width of column based on value length
            worksheet.columns.forEach((column) => {
                const lengths = column.values.map((v) => v.toString().length);
                const maxLength = Math.max(
                ...lengths.filter((v) => typeof v === "number")
                );
                column.width = maxLength+3;
            });

            if(reportFormat==='xlsx')
            {
                exportFileName=exportFileName+".xlsx";
                // function to create an excel file
               return(
                    await workbook.xlsx.writeFile('/tmp/'+exportFileName)
                    .then(function (error,data) {
                        if (error){
                            console.log('Error in creating the xlsx file',error);
                            return false;
                        }
                        else{
                            return true;
                        }
                    })
               )
            }
            else{
                exportFileName=exportFileName+".csv";
                // function to create an excel file
                return(
                    await workbook.csv.writeFile('/tmp/'+exportFileName)
                    .then(function (error,data) {
                        if (error){
                            console.log('Error in creating the CSV file',error);
                            return false;
                        }
                        else{
                            return true;
                        }
                    })
                )
            }
        }
    }
    catch(e)
    {
        console.log("Error in exportToExcell function main block",e);
        throw("HBLC0104");
    }
}

async function getHeaderBasedOnKey(key)
{
    try{
        let header="";
        for(let i=0;i<key.length;i++)
        {
            if(i==0)
            {
                header=header+key[i].toUpperCase();
            }
            else if(key[i]===key[i].toUpperCase())
            {
                header=header+" "+key[i].toUpperCase();
            }
            else{
                header=header+key[i];
            }
        }
        return header;
    }
    catch(e)
    {
        console.log("Error in getHeaderBasedOnKey main block",e);
        throw("HBLC0103");
    }
}

async function removeFile(filePath)
{
    try{
        fs.rmSync(filePath, { recursive: true })
        return true;
    }
    catch(e)
    {  
        console.log("Error while deleting the file",e);
        return false;
    }
}

async function uploadFileToS3Bucket(path,contentType,fileStream)
{
    try{
        let AWS = require("aws-sdk");   
        let  s3 = new AWS.S3();
        let params = {
            Bucket: process.env.asynchronousreportBucket,
            Key: path,
            Body: fileStream,
            ContentType: contentType,
            ServerSideEncryption:s3FileUpload.defaultEncryption
        };

        let response = await s3.upload(params).promise();
        return true;
    }
    catch(e)
    {
        console.log("Error in uploadFileToS3Bucket funciton main catch block.",e);
        throw("PBP0104");
    }
}

async function formNotificationParamAndSendEmail(htmlTable,logoPath,employeeEmail,emailSubject,)
{
    try{
        let notificationParams={
            "Source": process.env.emailFrom,
            "Template": "CommonNotificationEngine",
            "Destination": {
                "ToAddresses": [employeeEmail] 
            },
            "ReplyToAddresses": [process.env.emailFrom],
            "TemplateData": JSON.stringify({
                orgLogo:logoPath,
                emailSubject:emailSubject,
                htmlTable: htmlTable
            })
        }
        let sendEmailResponse=await CommonLib.func.sendEmailNotifications(notificationParams,process.env.sesTemplatesRegion);
        console.log(sendEmailResponse)
        if(!sendEmailResponse)
        {
            throw('PBP0105');
        }
        return true;
    }
    catch(e)
    {
        console.log("Error in formNotificationParamAndSendEmail function main block",e);
        throw('PBP0105');
    }
}

async function createHtmlDesignForReport(reportName,numberOfRecords,requestDateTime,reportUrl)
{
    try{
        let tableFinalBody=
        `<table border="0" cellpadding="0" cellspacing="0" height="0" width="95%" >
        <tbody>
           <tr align="center">
              <td align="center">
                <table border="0" cellpadding="0" cellspacing="0" width="100%">
                    <tbody align="center">
                    <div style="font-weight:bold;font-size:17px;color:#260029; padding: 5px;width: 400px;
text-align: start;">Your requested report is available for download.
                    </div>
                     <div>
                       <div style="width: 400px;text-align: start;border: 2px solid #F6F6F6;margin: 10px;">
                          <div style="padding: 10px; background: #F6F6F6;font-weight:bold;">Report Name</div>
                          <div style="padding: 10px;">${reportName}</div>
                       </div>
                        <div style="width: 400px;text-align: start;border: 2px solid #F6F6F6;margin: 10px;">
                          <div style="padding: 10px; background: #F6F6F6;font-weight:bold;">Number of records</div>
                          <div style="padding: 10px;">${numberOfRecords}</div>
                       </div>
                                                   <div style="width: 400px;text-align: start;border: 2px solid #F6F6F6;margin: 10px;">
                          <div style="padding: 10px; background: #F6F6F6;font-weight:bold;">Request Date Time</div>
                          <div style="padding: 10px;">${requestDateTime}</div>
                       </div>
                     </div>
                     <div style="margin-top: 40px;">
                         <a href="${reportUrl}" style="background-color:#483285;padding: 15px;color:white;margin-top: 4px;text-decoration: none;">Download</a>
                  </div>
                    </tbody>
                </table>
              </td>
           </tr>
        </tbody>
    </table>`

    return tableFinalBody; 
    }
    catch(e){
        console.log("Error in createHtmlDesignForReport function main block",e);
        throw "HBLC0105";
    }
}

async function getFileAndWebEventSummaryAliasBasedOnFilter(inputHeader)
{
    try{
        {
           let aliasMap={ 
                'Employee Id':"FES.Employee_Id as employeeId",
                'Activity Date':"FES.Activity_Date as activityDate",
                'Employee First Name':'EPI.Emp_First_Name as employeeFirstName',
                'Employee Last Name':'EPI.Emp_Last_Name as employeeLastName',
                'Employee Full Name':"CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as employeeFullName",
                'Employee Email':'EJ.Emp_Email as employeeEmail',
                'Manager':"CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as manager",
                'Create Count':"FES.Create_Count as createCount",
                'Rename Count':"FES.Rename_Count as renameCount",
                'Delete Count':"FES.Delete_Count as deleteCount",
                'Write Count':"FES.Write_Count as writeCount",
                'Access Count':"FES.Access_Count as accessCount",
                'Upload Count':"FES.Upload_Count as uploadCount",
                'Download Count':"FES.Download_Count as downloadCount",
                'Copy Count':"FES.Copy_Count as copyCount"
            }
            return aliasMap[inputHeader];
        }
    }
    catch(e)
    {
        console.log("Error in getFileAndWebEventSummaryAliasBasedOnFilter function main block",e);
        throw "HBLC0108";
    }
}

module.exports={
    callFunctionBasedOnInput
}
