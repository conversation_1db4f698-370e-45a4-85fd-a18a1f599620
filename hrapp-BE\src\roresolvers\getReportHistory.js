const { ApolloError} = require('apollo-server-lambda');
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
// require table names
const { ehrTables } = require('../../common/tablealias');
//Require moment
const moment = require('moment-timezone');

module.exports.getReportHistory = async (parent, args, context, info) => {
    let organizationDbConnection;
    try{
        let logInEmpId = context.Employee_Id;
        let minDate=moment.utc().subtract(7, "days").format("YYYY-MM-DD[T]HH:mm:ss");
        // get the organization database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);
        return(
           organizationDbConnection(ehrTables.reportHistory + " as RH")
           .select('RH.History_Id as historyId','RH.Rep_Id as reportId','RS.Rep_Title as reportTitle','RH.Aws_Path as awsPath','RH.Presigned_Url as presignedUrl','RH.Req_Params as requestParams','RH.Requested_By as employeeId','RH.Request_Date_Time as requestDateTime','RH.Status as status','RH.Error_Message as errorMessage')
           .leftJoin(ehrTables.reportSettings + " as RS","RH.Rep_Id","RS.Rep_Id")
           .where('RH.Requested_By',logInEmpId)
           .where('RH.Request_Date_Time','>=',minDate)
           .then(data=>{
               organizationDbConnection?organizationDbConnection.destroy():null;
               return {errorCode:"",message:"Report history details retrieved successfully.",reportHistoryDetails:data};
           })
           .catch(e=>{
               console.log("Error in getReportHistory() function .catch block.",e);
               throw('HBLC0111')
           })
        )
    }
    catch(e)
    {
        organizationDbConnection?organizationDbConnection.destroy():null;
        console.log("Error in getReportHistory() function main catch block.",e);
        let errResult = commonLib.func.getError(e,'HBLC0111');
        throw new ApolloError(errResult.message, errResult.code);
    }
}