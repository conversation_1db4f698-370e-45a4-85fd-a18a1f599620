// require table alias
const tables = require('../../common/tablealias');
let ehrTables = tables.ehrTables;
let appManagerTables = tables.appManagerTables;

async function checkAllowUserSignin(emailId, organizationDbConnection){
    return new Promise((resolve,reject)=>{
        return(
            organizationDbConnection(ehrTables.empPersonalInfo + ' as EMPI')
            .select('EMPI.Allow_User_Signin', 'EMPI.Enable_Sign_In_With_Mobile_No', 'EMJ.Employee_Id', 'EMJ.Emp_Status', 'EMTM.Member_Status','EMU.Firebase_Uid')
            .innerJoin(ehrTables.empJob + ' as EMJ','EMJ.Employee_Id','EMPI.Employee_Id')
            .innerJoin(ehrTables.empUser + ' as EMU', 'EMU.Employee_Id', 'EMJ.Employee_Id')
            .leftJoin(ehrTables.teamMembers + ' as EMTM','EMTM.Employee_Id','EMJ.Employee_Id')
            .where('EMJ.Emp_Email',emailId)
            .then(employeeData =>{
                resolve(employeeData);
            })
        ).catch(checkAllowUserSigninErr=>{
            console.log('Error in checkAllowUserSigninErr() function .catch block',checkAllowUserSigninErr);
            reject(checkAllowUserSigninErr);
        })
    })
};

// function to check checkBillingStatus of the organization
async function checkBillingStatus(orgCode, appManagerDbConnection){
    return new Promise((resolve, reject) => {
        return (
            appManagerDbConnection(appManagerTables.orgRateChoice)
            .select('Plan_Status')
            .where('Org_Code',orgCode)
            .where('Plan_Status','Active')
            .then(billingStatus => {
                // check billingStatus array length
                if (billingStatus.length === 0){
                    return (
                        appManagerDbConnection(appManagerTables.orgRateChoice)
                        .select('Plan_Status')
                        .where('Org_Code', orgCode)
                        .orderBy('Plan_Start_Date','desc')
                        .then(billingStatusLatest => {
                            // check billingStatusLatest array length. Based on that return response to resolver
                            (billingStatusLatest.length > 0) ? resolve(billingStatusLatest[0]) : reject('No plan exist for this organization.');
                        })
                    )
                }else{
                    // return response to reolver function
                    resolve(billingStatus[0]);
                }
            })
        ).catch(checkBillingStatusError => {
            console.log('Error in checkBillingStatus() function .catch block', checkBillingStatusError);
            reject(checkBillingStatusError);
        })
    })
}

// function to get getPaymentUrl
async function getPaymentUrl(orgCode,appManagerDbConnection){
    return new Promise((resolve, reject) => {
        return (
            appManagerDbConnection(appManagerTables.orgRateChoice)
            .select('Payment_Url')
            .where('Plan_Status','NonComplaint')
            .where('Org_Code', orgCode)
            .then(PaymentURL => {
                // return response to reolver function
                PaymentURL.length > 0 ? resolve(PaymentURL[0]) : resolve('');
            })
        ).catch(getPaymentUrlError => {
            console.log('Error in getPaymentUrl() function .catch block', getPaymentUrlError);
            reject(getPaymentUrlError);
        })
    })
}

// function to initialize the firebase sdk
async function initializeSdk(){
    // Require aws-sdk
    const AWS = require('aws-sdk');
    try{    
        // Create client for secrets manager
        let client = new AWS.SecretsManager({
            region: process.env.region
        });

        // Get secrets from aws secrets manager
        let secretKeys = await client.getSecretValue({ SecretId: process.env.dbSecretName }).promise();
        secretKeys = JSON.parse(secretKeys.SecretString);

        let serviceAccountkeys = {
            type: secretKeys.type,
            project_id: secretKeys.firebase_project_id,
            private_key_id: secretKeys.firebase_private_key_id,
            private_key: secretKeys.firebase_privatekey,
            client_email: secretKeys.firebase_client_email,
            client_id: secretKeys.firebase_client_id
        };
        // remove /n from the privatekey . because /n is consider as string here (\\n to \n)
        serviceAccountkeys.private_key = (serviceAccountkeys.private_key).replace(/\\n/g, "\n");
        // return response to resolver
        return serviceAccountkeys;
    } catch (initializeSdkError) {
        console.log('Error in initializeSdk() function main catch block', initializeSdkError);
        return {};
    }
} 
module.exports.checkBillingStatus = checkBillingStatus;
module.exports.checkAllowUserSignin = checkAllowUserSignin;
module.exports.getPaymentUrl = getPaymentUrl;
module.exports.initializeSdk = initializeSdk;