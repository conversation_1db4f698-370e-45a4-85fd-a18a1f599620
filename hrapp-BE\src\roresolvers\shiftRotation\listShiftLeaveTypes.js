// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formIds } = require('../../../common/appconstants');

//function to list the shift leave types
let organizationDbConnection;
module.exports.listShiftLeaveTypes = async (parent, args, context, info) => {
    try {
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, null, 'Role_View', 'Back-end', null, formIds.shiftRotation);
        if (!checkRights) {
            throw '_DB0100';
        }

        //Retrieve shift leave types
        let data = await organizationDbConnection(ehrTables.leavetype)
            .select("LeaveType_Id", "Leave_Name")
            .where('Leave_Enforcement_Configuration', 6);

        return { errorCode: "", message: "Shift leave types retrieved successfully.", shiftLeaveTypes: JSON.stringify(data) };

    }
    catch (e) {
        console.log('Error in listShiftLeaveTypes function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'SS0007');
        throw new ApolloError(errResult.message, errResult.code);
    }
    finally {
        //Destroy DB connection
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
}