// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError,UserInputError } = require('apollo-server-lambda');
// require file to access constant values
const { formName,systemLogs } = require('../../../common/appconstants');
// require table alias function
const { ehrTables } = require('../../../common/tablealias');
// require validation file
const benefitsInputValidation=require('../../../common/benefitsInputValidation');
const moment = require('moment-timezone')

// variable declarations
let organizationDbConnection='';
let errResult={};

// resolver definition
const resolvers = {
    Mutation:{
        // function to allocate shares to the employees
        addEmployeesForAllocatingShares: async (parent, args, context, info) => {
            let validationError={};
            try{
                console.log('Inside addEmployeesForAllocatingShares function');
                // variable declarations
                let logInEmpId=context.Employee_Id;
                let ipAddress=context.User_Ip;
                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                // check whether employee have add access for ESOP form
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.esop, '', 'UI');  
                if(Object.keys(checkRights).length>0 && (checkRights.Role_Add === 1)) {
                    // Check benefits admin form - edit access rights exist for employee or not
                    let checkBenefitsAdminRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.benefitsAdmin, '', 'UI');
                    if(Object.keys(checkBenefitsAdminRights).length>0 && checkBenefitsAdminRights.Role_Update === 1) {
                        //get the employee's max date of join
                        let maxDateOfJoin = await organizationDbConnection(ehrTables.empJob)
                        .select(organizationDbConnection.raw('MAX(Date_Of_Join) as maxDateOfJoin'))
                        .whereIn('Employee_Id', args.employeeIds)
                        maxDateOfJoin = maxDateOfJoin[0].maxDateOfJoin

                        //get the employees minimum resignation date
                        let resignationDates = await commonLib.payroll.getEmployeeResignationDate(organizationDbConnection, args.employeeIds)
                        let minResignationDate = null
                        if(resignationDates && resignationDates.length){
                            resignationDates = resignationDates.map((el) => moment(el.Resignation_Date));
                            minResignationDate = moment.min(resignationDates).format('YYYY-MM-DD');
                        }
                        // validate the input fields
                        validationError=await benefitsInputValidation.addFormInputValidation(args, maxDateOfJoin, minResignationDate);
                        // Check validation error exist or not
                        if (Object.keys(validationError).length === 0) {
                            let employeeTimeZone = await commonLib.func.getEmployeeTimeZone(logInEmpId, organizationDbConnection,1);
                            // form input params
                            let inputParams = args.employeeIds.map(field=>({
                                Employee_Id: field,
                                Allocated_Shares:args.allocatedShare,
                                Allocated_Date:args.allocatedDate,
                                Visibility:args.visibility,
                                Added_By: logInEmpId,
                                Added_On: employeeTimeZone
                            }));
                            // inserted record in employee share details
                            return(
                                organizationDbConnection
                                .insert(inputParams)
                                .from(ehrTables.employeeShareDetails)
                                .then(async() => {
                                    let systemLogParams = {
                                        action: systemLogs.roleAdd,
                                        userIp: ipAddress,
                                        employeeId: logInEmpId,
                                        formName: formName.esop,
                                        trackingColumn: '- Allocated shares for the employeeId(s)',
                                        organizationDbConnection: organizationDbConnection,
                                        uniqueId: args.employeeIds
                                    }
                                    // call function createSystemLogActivities() to update system log activities
                                    await commonLib.func.createSystemLogActivities(systemLogParams);
                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                    return { errorCode:'',message:'Employee shares allocated successfully.'};
                                })
                                .catch(function (catchError) {
                                    console.log('Error in addEmployeesForAllocatingShares function .catch block',catchError);
                                    errResult = commonLib.func.getError(catchError, 'BES0104');
                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                    throw new ApolloError(errResult.message,errResult.code);
                                })
                            );
                        }
                        else{
                            throw 'IVE0000';
                        }
                    }
                    else{
                        throw '_DB0109';
                    }
                }
                else if (checkRights === false) {
                    throw '_DB0101';
                } else {
                    throw (checkRights);
                }
            }
            catch(mainCatchError)
            {
                console.log('Error in addEmployeesForAllocatingShares function main catch block',mainCatchError);
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                if(mainCatchError==='IVE0000'){
                    errResult = commonLib.func.getError('', 'IVE0000');
                    throw new UserInputError(errResult.message, { validationError: validationError });
                }
                else{
                    errResult = commonLib.func.getError(mainCatchError, 'BES0004');
                    // return error response
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        }
    }
};

exports.resolvers = resolvers;
