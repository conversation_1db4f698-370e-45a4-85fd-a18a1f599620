type Query{
  retrieveTaxConfiguration:retrieveTaxConfigurationResponse!
  retrieveEmployeeMonitorSettings(isCallFromWebApp:Int):retrieveEmployeeMonitorSettingsResponse!
  viewPayrollSettings: viewPayrollSettingsResponse!,
  retrieveIncomeTaxDeclarationSettings:retrieveIncomeTaxDeclarationSettingsResponse!
  listAllServiceProviderDetails:listAllServiceProviderDetailsResponse!
  checkProviderNameExists(serviceProviderName:String!):commonResponse!
  viewMoreServiceProviderDetails(serviceProviderId:Int!):viewMoreServiceProviderDetailsResponse!
}

type Mutation {
    updateTaxConfiguration(
        taxConfigurationId : Int!
        taxRegime : String!
        tdsAmountRoundOff : Int!
    ) : SuccessResponse!

    updateEmployeeMonitorSettings(
        employeeMonitoringSettingsId : Int!,
        captureScreenshots:Int!,
        screenshotsFrequency:Int,
        noOfScreenshotsPerFrequency:Int
    ):SuccessResponse!

    updatePayrollSettings(settings: [updatePayrollSettingsInput!]!): SuccessResponse!

    updateIncomeTaxDeclarationSettings(
        lockDate : String!,
        newJoinersGraceDays:Int,
        notifyITDeclarationRelease:String!,
        notifyITDeclarationLock:String!,
        notifyITDeclarationSubmissionBeforeLockDate: String!,
        remindFiveDaysBefore:String,
        remindOneDayBefore:String,
        emailSubject:String,
        emailContent:String
    ):SuccessResponse!
    addServiceProviderDetails(isEditForm:Int!,serviceProviderId:Int,serviceProviderName:String!,contactPersonName:String!,contactNumber:String!,emailId:String!,locationId:Int!,website:String,serviceProviderLogo:String,serviceProviderMou:String):commonResponse!
    updateServiceProviderLocation(serviceProviderId:Int!,locationId:Int!):commonResponse!
}

input updatePayrollSettingsInput {
    formId: Int!
    choice: Int!
}

type viewPayrollSettingsResponse{
    errorCode: String
    message: String
    payrollSettings: String
}
type settingsData {
    Employee_Monitor_Settings_Id : Int,
    Capture_Screenshot : Int,
    Screenshot_Frequency : Int,
    No_Of_Screenshots_Per_Frequency : Int
}

type retrieveEmployeeMonitorSettingsResponse {
    errorCode: String,
    message: String,
    settingsData: settingsData
}

type retrieveTaxConfigurationResponse {
    errorCode     : String,
    message       : String,
    taxConfigurationId : Int,
    taxRegime     : String,
    tdsAmountRoundOff : Int
}

type SuccessResponse {
   errorCode : String,
   message : String,
   validationError : String
}

type incomeTaxDeclarationSettingsResponse {
    Lock_Date: String,
    New_Joiners_Grace_Days: Int,
    Notify_IT_Declaration_Release: String,
    Notify_IT_Declaration_Lock : String,
    Notify_IT_Declaration_Submission_Before_Lock_Date : String,
    Remind_Five_Days_Before : String,
    Remind_One_Day_Before : String,
    Email_Subject : String,
    Email_Content : String
}

type retrieveIncomeTaxDeclarationSettingsResponse {
    errorCode: String,
    message: String,
    incomeTaxDeclarationSettings: incomeTaxDeclarationSettingsResponse,
    fiscalStartDate: String,
    fiscalEndDate: String
}

type commonResponse{
    errorCode: String,
    message: String
}

type listAllServiceProviderDetailsResponse{
  errorCode:String,
  message:String,
  getProviderDetails:String
}

type viewMoreServiceProviderDetailsResponse{
  errorCode:String,
  message:String,
  providerDetails:providerDetails
}

type providerDetails{
    addedByEmployeeId: String,
    addedByUserName: String,
    addedOn: String,
    updatedByEmployeeId: String,
    updatedByUserName: String,
    updatedOn: String
}

schema {
  query: Query,
  mutation : Mutation
}
