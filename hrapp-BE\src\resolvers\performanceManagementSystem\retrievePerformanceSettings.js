// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName,defaultValues } = require('../../../common/appconstants');
// require table alias
const { ehrTables } = require('../../../common/tablealias');
// require pms common functions
const {getRatings,getPerformanceMonthYear} = require('../../../common/performanceManagementCommonFunction');

// variable declarations
let errResult = {};
let organizationDbConnection = '';

// resolver definition
const resolvers = {
    Query: {
        // function to retrieve the performance settings
        retrievePerformanceSettings: async (parent, args, context, info) => {
            try{
                console.log('Inside retrievePerformanceSettings function');
                // get the organization data base connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                let logInEmpId = context.Employee_Id;
                // check whether the employee has admin access to performance Management form or not
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId, formName.performanceManagement, '', 'UI');
                    return(
                        organizationDbConnection(ehrTables.performanceManagementSettings)
                        .select('Performance_Management_Mode','Maximum_Rating','GoalSettings_ReminderOne','GoalSettings_ReminderTwo',
                        'RatingUpdate_ReminderOne','RatingUpdate_ReminderTwo','Goal_Email_Subject', 'Goal_Email_Content',
                        'Rating_Email_Subject' , 'Rating_Email_Content','Enable_Email_Notification','Present_Only_Team_Members_To_Managers')
                        .then(async(getDetails) => {
                            if(getDetails.length>0){
                                let ratingDescription=await getRatings(organizationDbConnection);
                                getDetails[0]['ratingDetails']=ratingDescription;
                                // if any one record with rating published for the year then we should not also user to edit the ratings
                                // in case of any error in this function we will return as non-editable instead of error
                                if(getDetails[0].Performance_Management_Mode.toLowerCase()==='goals'){
                                    getDetails[0].isRatingEditable= await checkRatingPublishedRecordExist(organizationDbConnection,context.Org_Code);
                                }
                                // if performance mode is skills then check whether any record added for skill level assessment
                                else{
                                    getDetails[0].isRatingEditable= await checkSkillsRecordExist(organizationDbConnection);
                                }
                            }
                            // destroy database connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return {errorCode:'',message:'Performance management settings retrieved successfully.',settingsDetails:(getDetails.length)?getDetails[0]:[]};
                        })
                        .catch(catchError => {
                            console.log('Error in retrievePerformanceSettings function .catch block.', catchError);
                            // destroy database connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            errResult = commonLib.func.getError(catchError, 'EPM0101');
                            // throw error response to UI
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                    );
                
            }catch(mainCatchError){
                console.log('Error in retrievePerformanceSettings function main catch block',mainCatchError);
                errResult = commonLib.func.getError(mainCatchError, 'EPM0001');
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                // return response
                throw new ApolloError(errResult.message,errResult.code);
            }
        }
    }
};
exports.resolvers = resolvers;

// function to check whether any record found with rating published status for the current year
async function checkRatingPublishedRecordExist(organizationDbConnection,orgCode){
    try{
        // form the inputs with current year/month
        let args={
            year:new Date().getFullYear(),
            month:new Date().getMonth()+1
        }
        // get the performance month and year based on the orgcode
        let getPerformanceMonthYearResponse = await getPerformanceMonthYear(orgCode,organizationDbConnection,'getstartendmonthandyear',args);
        // check the record with rating published status for the current year
        let subQuery=organizationDbConnection(ehrTables.performanceGoalAchievement)
        .select('Performance_Assessment_Id')
        .where('Rating_Publish_Status', defaultValues.goalsAndAchievementGoalPublishStatus)

        if(getPerformanceMonthYearResponse.length>0){
            subQuery=subQuery
            .where(qb => {
                qb.where('Performance_Assessment_Year', getPerformanceMonthYearResponse[0].currentStartYear)
                qb.orWhere('Performance_Assessment_Year', getPerformanceMonthYearResponse[0].currentEndYear)
            })
        }
        else{
            subQuery=subQuery
            .where(qb => {
                qb.where('Performance_Assessment_Year', new Date().getFullYear())
            })
        }
        return(
            subQuery
            .then(getId => {
                // if record exist then return user cannot edit the ratings
                return (getId.length>0)?'no':'yes';
            })
            .catch(catchError => {
                console.log('Error in checkRatingPublishedRecordExist function .catch block.',catchError);
                return 'no';
            })
        );
    }
    catch(error){
        console.log('Error in checkRatingPublishedRecordExist function catch block.',error);
        return 'no';
    }
};

// function to check whether any record found for skill level assessment or not
async function checkSkillsRecordExist(organizationDbConnection){
    try{
        return(
            organizationDbConnection(ehrTables.skillLevels)
            .select('LineItem_Id')
            .then(getData => {
                // if record exist then return user cannot edit the maximum ratings
                return (getData.length>0)?'no':'yes';
            })
            .catch(catchError => {
                console.log('Error in checkSkillsRecordExist function .catch block.',catchError);
                return 'no';
            })
        );
    }
    catch(error){
        console.log('Error in checkSkillsRecordExist function catch block.',error);
        return 'no';
    }
};