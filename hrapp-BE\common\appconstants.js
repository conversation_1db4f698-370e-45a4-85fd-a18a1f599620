const formName = {
    ipWhitelist: 'IP Whitelisting',
    tax: 'Tax and Statutory Compliance',
    employeeMonitorSettings: 'Employee Monitoring',
    teamMembers: 'Team Members',
    attendance: 'Attendance',
    myActivity: 'My Activity',
    myteamActivity: 'My Team Activity',
    employeeAdmin: 'Employee Admin',
    leaves: 'Leaves',
    holidays: 'Holidays',
    admin: 'Admin',
    payrollAdmin: 'Payroll Admin',
    employeeDirectory: 'Employee Directory',
    empMonitoringReports: 'Activity Tracker Reports',
    shiftScheduling: "Shift Scheduling",
    shiftType: "Shift Type",
    overtime: 'Additional Wage Claim',
    performanceManagement: 'Performance Management',
    goalsAndAchievement: 'Goals And Achievement',
    superAdmin: 'Super Admin',
    compensatoryOffBalance: 'Compensatory Off Balance',
    shiftAllowance: 'Shift Allowance',
    redeemRewards: 'Redeem Rewards',
    esop: 'ESOP',
    benefitsAdmin: 'Benefits Admin',
    productivityMonitoring: 'Productivity Monitoring',
    general: 'General',
    employees: 'Employees',
    attendanceConfiguration: 'Attendance Configuration',
    accreditation: 'Accreditation',
    projects: 'Projects',
    defaultRetentionPeriod: 3,//by default 3 months
    onDutySettings: "On Duty Settings",
    preApprovalSettings: "Pre Approvals",
    compOff: "Comp Off",
    specialWages: "Special Wages",
    geoFencingSelfieAttendance: "Geo-Fencing & Selfie Attendance",
    lopRecovery: 'LOP Recovery',
    userAccounts: 'User Accounts',
    jobPost: 'Job Posts',
    timesheets: 'Timesheets',
    empPersonalInfo: 'Leave Request',
    overTime: 'Over Time'
}

const formIds = {
    myActivity: 198,
    leaves: 31,
    shortTimeOff: 128,
    shortTimeOffNew: 352,
    compensatoryOff: 139,
    compensatoryOffNew: 334,
    advanceSalary: 53,
    bonus: 46,
    commission: 48,
    deductions: 49,
    loan: 54,
    reimbursement: 50,
    reimbursementNew: 267,
    shiftAllowance: 56,
    attendance: 29,
    resignation: 34,
    superAdmin: 147,
    timesheets: 23,
    transfer: 33,
    incomeUnderSection24: 192,
    performanceAssesment: 84,
    deferredLoan: 73,
    taxDeclaration: 61,
    inbox: 36,
    hraDeclarations: 119,
    shiftScheduling: 163,
    overtime: 210,
    goalsAndAchievement: 214,
    shiftType: 57,//shift type configuration form for the shift allowance
    announcement: 157,
    productivityMonitoring: 196,
    attendanceConfiguration: 248,
    accreditation: 232,
    holidays: 8,
    projects: 3,
    onDutySettings: 224,
    workFormHomePreApproval: 244,
    workDuringWeekOffPreApprovals: 245,
    workDuringHolidayPreApprovals: 246,
    compOff: 250,
    specialWages: 85,
    geoFencingSelfieAttendance: 227,
    lopRecoverySettings: 253,
    admin: 22,
    ServiceProviderAdmin: 219,
    jobPost: 15,
    shiftRotation: 293,
    onDutyPreApprovals: 301,
    shiftSwap:306,
    shiftSwapApproval: 307,
    employeeLeave:333,
    workDuringHoliday: 246,
    workDuringWeekOff: 245,
    workFromHome: 244,
    recruitmentRequest: 291,
    newPosition: 290,
    employeeTravel: 35,
    employeeTravelNew: 341,
    projectSettings:268,
    empSalaryRevision:360,
    teamSummary:243,
    myProfile:18,
    overTimeNew:363

}

const roles = {
    roleAdd: 'Role_Add',
    roleUpdate: 'Role_Update',
    roleDelete: 'Role_Delete',
    roleView: 'Role_View'
}

const systemLogs = {
    roleAdd: 'Add',
    roleUpdate: 'Update',
    roleDelete: 'Delete'
}

const defaultValues = {
    integrationUrlConstant: 'subscription',
    reportUrlConstant: 'hr-reports',
    departmentHierarchyUrlConstant: 'departments',
    hrReportsUrlConstant: 'view-hrreports',
    yearStartDate: '01-01',
    yearEndDate: '12-31',
    goalsAndAchievementAssessmentStatus: 'Goals Assigned',
    goalsAndAchievementGoalUnPublishStatus: 'Unpublished',
    goalsAndAchievementGoalPublishStatus: 'Published',
    ratedAssessmentStatus: 'Rated',
    performanceDefaultStartMonth: 1,
    pmsRedirectionUrl: "/in/performance-management/performance-evaluation",
    maximumNumberOfMailTriggered: 50,
    compOffAttendanceOvertimeClaimSourceName: 'Attendance And Overtime Claim',
    compOffOvertimeClaimSourceName: 'Overtime Claim',
    benefitsSuperAdminsVisibilityStatus: 'Super Admins',
    performanceGoalStatus: ['Active', 'Archive'],
    monthsArray: ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
    monthNumbersArray: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    emailNotificationStatus: ['NotStarted', 'Success', 'Failure'],
    openEmailNotificationStatus: 'Open',
    employeeRedirectionUrl: "/employees/employees",
    resignationRedirectionUrl: "/employees/resignation",
    longLeaveTotalDays: 10,
    defaultRetentionPeriod: 3, // By default 3 months
}

const signInRestAPI = {
    signInAPI: 'https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=',
    refreshTokenAPI: 'https://securetoken.googleapis.com/v1/token?key=',
    refreshTokenGrantType: 'refresh_token',
    refreshTokenExpiresIn: 15 // In days, configurable and changable

}

const s3FileUpload = {
    contentType: 'image/png',
    contentEncoding: 'base64',
    txtFileType: 'text/plain',
    defaultEncryption: 'AES256',
    binaryFile: 'application/octet-stream'
}

const awsSesTemplates = {
    inviteTeamMembers: 'InviteTeamMembers',
    requestRights: 'RequestRights',
    performanceGoalsReminder: 'PerformanceGoalsReminder',
    performanceRatingsReminder: 'PerformanceRatingsReminder',
    performanceGoalsPublishedNotification: 'PerformanceGoalsPublishedNotification',
    performanceRatingPublishedNotification: 'PerformanceRatingPublishedNotification',
    commonNotification: 'CommonNotification'
}

const urlEncryption = {
    encryptionKey: 'pD!m*LsZQmT4'
}

const employeeMonitorDefaultValues = {
    activityDataExpiresIn: 30 //days
}
const defaultDateFormat = {
    dateFormat: 'YYYY-MM-DD hh:mm'
};

module.exports.formName = formName;
module.exports.roles = roles;
module.exports.systemLogs = systemLogs;
module.exports.defaultValues = defaultValues;
module.exports.signInRestAPI = signInRestAPI;
module.exports.s3FileUpload = s3FileUpload;
module.exports.awsSesTemplates = awsSesTemplates;
module.exports.formIds = formIds;
module.exports.urlEncryption = urlEncryption;
module.exports.employeeMonitorDefaultValues = employeeMonitorDefaultValues;
module.exports.defaultDateFormat = defaultDateFormat;