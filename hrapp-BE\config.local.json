{"securityGroupIds": ["sg-098a0324de5632c9e", "sg-03b2b753038f67293"], "subnetIds": ["subnet-020dff85cddc0752e", "subnet-0b1a6fbc2c7b782ad"], "dbSecretName": "hrapp-stage", "region": "ap-south-1", "hrappProfileBucket": "s3.hrapp-dev-public-asset", "lambdaRole": "arn:aws:iam::692647644057:role/lambdaFullAccess", "dbPrefix": "hrapp_", "domainName": "hrapp", "authorizerARN": "arn:aws:lambda:ap-south-1:692647644057:function:firebase-lambda-authorizer", "customDomainName": "", "logoBucket": "s3.hrapp-dev-public-images", "screenshotsBucket": "s3.hrapp.employeemonitoring", "emailFrom": "<EMAIL>", "sesRegion": "us-east-1", "firebaseApiKey": "AIzaSyB-QCDxis2HG3hHIreLPiidSlN_eCyi3m8", "webAddress": "", "desktopClientURL": "https://hrapp-downloadable-assets.s3.ap-south-1.amazonaws.com/hrapp-activity-tracker/windows/HRAPP.zip", "pmsEmailNotificationArn": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-pmsReminderNotification", "batchEmailNotificationArn": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-batchEmailNotification", "asynchronousreportBucket": "asynchronousreport.hrapp.co.in", "generateReportAsyncStepFunction": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-generateReportAsyncStepFunction", "updateRosterLeaveBatch": "arn:aws:lambda:ap-south-1:692647644057:function:BATCHPROCESSING-dev-updateRosterLeaveBatch", "resourceArnPrefix": "arn:aws:lambda:ap-south-1:692647644057:function:HRAPPBACKEND-dev"}