//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require common constant files
const { ehrTables } = require('../../../common/tablealias');
//Require moment
const moment = require('moment');

//Function to validate the input fields
function validateInputs(args){
    try{
        if(args.employeeId){
            return 'success';
        }else{
            handleError('validationError','_EC0007', args);
        }
    }catch(error){
        console.log('Error in validateInputs() function main catch block', error);
        throw error;
    }
}

//Function to get the payslip month details for the employee
async function getLastPayslipMonth(inputDetails){
    try {
        console.log('Inside getLastPayslipMonth function.');
        let { employeeId,context } = inputDetails;

        const axios = require('axios');

        // Form Headers
        const headers = {
            authorization: context.Auth_Token,
            refresh_token: context.refreshToken,
            employee_id: context.Employee_Id,
            partnerid: context.partnerid,
            org_code: context.Org_Code,
            empUid: context.empUid,
        };

        let payload = {
            employeeId: employeeId,
            formName: 'shiftSwap'
        }

        const url = `https://${context.Org_Code}.${process.env.domainName}${process.env.webAddress}/payroll/salary-payslip/get-last-payslip-month`;
        const qs = require('qs');
        const response = await axios.request({
            url,
            method: 'POST',
            data: qs.stringify(payload),
            headers: {
                ...headers,
                'Content-Type': 'application/x-www-form-urlencoded',
                Referer: url,
                Cookie: commonLib.func.getCookieValue(headers),
            },
            responseType: 'json',
        });

        if (!response || response.status !== 200) {
            console.error('Error in calling PHP controller getLastPayslipMonth function.', response);
            throw 'SS0163';
        }

        if (!response || !response.data || !response.data.salaryDates) {
            console.error('Empty response in getLastPayslipMonth function.', response);
            throw 'SS0163';
        }

        return response.data.salaryDates;
    } catch (error) {
        console.error('Error in getLastPayslipMonth function main catch block', error);
        throw error;
    }
}

//Function to get the minimum and maximum date for shift swap
async function getShiftSwapDateLimits(organizationDbConnection, inputDetails){
    try {
        console.log('Inside getShiftSwapDateLimits function.');
        let { rosterSettings,employeeId } = inputDetails;
        let minimumStartDate = '';

        let momentTimezone = require('moment-timezone');
        let employeeTimeZone = await commonLib.func.getEmployeeTimeZone(employeeId, organizationDbConnection,0);
        employeeTimeZone = (typeof(employeeTimeZone) === 'string') ? employeeTimeZone : 'Asia/Kolkata';
        let currentDate =  momentTimezone().tz(employeeTimeZone).format('YYYY-MM-DD');
        
        if(rosterSettings.Allow_Past_Shift_Swaps.toLowerCase() === 'yes'){
            minimumStartDate = await calculatePastSwapMinimumDate(organizationDbConnection, inputDetails,currentDate);
        }else{
            minimumStartDate = moment(currentDate).add(1, 'days').format('YYYY-MM-DD');
        }
        let resignationDate = await commonLib.payroll.getEmployeeResignationDate(organizationDbConnection,employeeId,['Approved']);

        return {minimumDate: minimumStartDate, maximumDate: resignationDate};
    } catch (error) {
        console.log('Error in getShiftSwapDateLimits() function main catch block', error,inputDetails);
        throw error;
    }
}

//If past shift swap is allowed then calculate the minimum date
async function calculatePastSwapMinimumDate(organizationDbConnection, inputDetails,currentDate){
    try {
        console.log('Inside calculatePastSwapMinimumDate function.');
        let { employeeId,rosterSettings } = inputDetails;

        let employeePayslipMonthDetails = await getLastPayslipMonth(inputDetails);
        
        let startDate = '',
        dojOrPayMonthStartDate = '';
        if(employeePayslipMonthDetails['payslipGenerated'] && employeePayslipMonthDetails['payslipGenerated'] == 1){
            dojOrPayMonthStartDate =  employeePayslipMonthDetails['salaryDate'];
        }else{
            //Get the date of join
            dojOrPayMonthStartDate = await commonLib.func.getDateOfJoiningBasedOnEmpId(organizationDbConnection,employeeId,0);
        }

        if(dojOrPayMonthStartDate){
            let minimumDate = moment(currentDate).subtract(rosterSettings?.Max_Shift_Swap_Days, 'days').format('YYYY-MM-DD');

            //Compare date of join or (last payslip generated month+ 1month - salary date) and the (current date - max days)
            if(moment(minimumDate) > moment(dojOrPayMonthStartDate)){
                /** Consider the payslip generated for june,2022 month and the current date is 20 Aug 2022 then the (current date-40 days)
                 * is 10 July 2022. In this case,the dojOrPayMonthStartDate will be 1 July 2022. So we need to open date from 10 July 2022.*/
                /** Or if the employee joins after before 10 July 2022 and payslip is not generated
                 * then we will open date from 10 July 2022.*/
                startDate = minimumDate;
            }else{
                startDate = dojOrPayMonthStartDate;
            }

            return startDate;
        }else{
            console.log('Empty dojOrPayMonthStartDate in calculatePastSwapMinimumDate() function', error,inputDetails,employeePayslipMonthDetails,dojOrPayMonthStartDate);
            throw 'SS0164';
        }
    } catch (error) {
        console.log('Error in calculatePastSwapMinimumDate() function main catch block', error,inputDetails);
        throw error;
    }
}

//Trigger function to fetch the shift swap dates
async function fetchShiftSwapDates(organizationDbConnection, inputDetails){
    try {
        console.log('Inside fetchShiftSwapDates function.');
        let rosterSettings = await organizationDbConnection(ehrTables.rosterSettings).select('*').first();
        inputDetails.rosterSettings = rosterSettings;

        let dateRangeResult = await getShiftSwapDateLimits(organizationDbConnection, inputDetails);
        return dateRangeResult;
    } catch (error) {
        console.log('Error in fetchShiftSwapDates() function main catch block', error, inputDetails);
        throw error;
    }
}
function handleError(error,errorCode, args){
    let errResult;
    console.log('Inside in getShiftSwapDateRange handleError function.', error,errorCode,args);
    errResult = commonLib.func.getError(errorCode, 'SS0009');
    throw new ApolloError(errResult.message, errResult.code);
}

//Function to get the minimum date and maximum date for an employee
module.exports.getShiftSwapDateRange = async (parent, args, context, info) => {
    let organizationDbConnection;

    try {
        console.log("Inside getShiftSwapDateRange function.")
        organizationDbConnection = knex(context.connection.OrganizationDb);

        validateInputs(args);

        let inputDetails = {
            employeeId: args.employeeId,
            context: context
        }

        let {minimumDate,maximumDate} = await fetchShiftSwapDates(organizationDbConnection, inputDetails);

        return { errorCode: "", message: `Shift swap date range retrieved successfully.`, minimumDate:minimumDate, maximumDate: maximumDate};
    }
    catch (e) {
        handleError(error,errorCode, args);
    }
    finally {
        organizationDbConnection ? organizationDbConnection.destroy : null;//Destroy DB connection
    }
}