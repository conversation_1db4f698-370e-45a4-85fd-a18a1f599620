const { ApolloError } = require('apollo-server-lambda');
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require table names
const { ehrTables } = require('../../../common/tablealias');
// require common constant files
const { systemLogs } = require('../../../common/appconstants');
// Organization database connection
const knex = require('knex');
const moment = require('moment');

// function to update language preference in emp_job table
module.exports.updateEmpLanguagePreference = async (parent, args, context, info) => {
    let orgDb;
    try {
        orgDb = knex(context.connection.OrganizationDb);
        // Get login employee ID
        let loginId = context.Employee_Id;

        // Update the language preference in emp_job table
        const updateResult = await orgDb(ehrTables.empPersonalInfo)
            .where('Employee_Id', args.employeeId)
            .update({
                Language_Preference: args.languagePreference,
            });

        if (updateResult) {
            // Form inputs to update system log activities
            let systemLogParams = {
                action: systemLogs.roleUpdate,
                userIp: context.User_Ip,
                employeeId: loginId,
                formName: 'Employee Job',
                trackingColumn: 'Language Preference',
                organizationDbConnection: orgDb,
                uniqueId: args.employeeId
            };

            // Call function createSystemLogActivities() to update system log activities
            await commonLib.func.createSystemLogActivities(systemLogParams);
            return {
                success: true,
                errorCode: null,
                message: "Language preference updated successfully"
            };

        }
        else{
            console.log('Unable to update the language preference for the employee id, ',args.employeeId);
            throw 'DB0031';
        }
        // Return success response
    }
    catch (err) {
        console.log('Error in updateEmpLanguagePreference function main catch block', err);
        let errResult = commonLib.func.getError('err', 'DB0031');
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        // Destroy database connection
        orgDb ? orgDb.destroy() : null;
    }
};
