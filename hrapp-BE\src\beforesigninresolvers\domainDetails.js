// Organization database connection
var knex = require('knex');
// resolver definition
const resolvers = {
    Query: {
        domainDetails: async (parent, args, context, info) => {
            console.log('Inside domainDetails.js function.');
            try {
                // Get App-manager database connection
                var appManagerConnection = knex(context.connection.AppManagerDb);
                return appManagerConnection.select('D.Org_Email', 'D.Contact_No',
                                                'D.Organization_Name', 'D.Support_Email',
                                                'D.Copy_Right', 'D.Support_Link',
                                                'D.Sales_Email', 'D.Product_Site',
                                                'D.Street1', 'D.Street2',
                                                'D.City', 'D.State',
                                                'D.Country', 'D.Account_No',
                                                'D.<PERSON>ncode', 'D.Talk_Link',
                                                'D.Product_Logo', 'D.Terms_Link',
                                                'D.Privacy_Policy_Link', 'D.Chat_Bot',
                                                'D.Connected_Banking', 'C.Country_Name')
                    .from('domain_settings as D')
                    .leftJoin('country as C','C.Country_Code','D.country')
                    .then(domainDetails =>{
                        //  check if domain details is empty or not
                        if (domainDetails[0]){
                            return {
                                errorCode: null,
                                message: 'success',
                                domainDetails: JSON.stringify(domainDetails[0])
                            }
                        }else{
                            throw new Error('SSO0003');
                        }                        
                    })
                    .catch(function (err) {
                        console.log('error while retrieving the domain details', err);
                        if (err.code == 'ECONNREFUSED') {
                            throw new Error(JSON.stringify({ errorCode: "SSO0000", message: 'Error in database connection', domainDetails: null }));
                        } else if(err.code == 'SSO0003') {
                            throw new Error(JSON.stringify({ errorCode: "SSO0003", message: 'Domain details not found.', domainDetails: null }));
                        } else {
                            throw new Error(JSON.stringify({ errorCode: "SSO0004", message: 'Something went wrong', domainDetails: null }));
                        }
                    })
                    /**close db connection */
                    .finally(() => {
                        appManagerConnection.destroy();
                    })
            }catch(error){
                console.log('error while checking the domain details', error);
                throw new Error(JSON.stringify({ errorCode: "SSO0005", message: 'Error while retrieving the domain details.', domainDetails: null}));
            }
        }
    }
};

module.exports.resolvers = resolvers;