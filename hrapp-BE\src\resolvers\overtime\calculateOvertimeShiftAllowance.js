// resolver function to calculate the shift allowance for the overtime from the additional wage claim form
module.exports.calculateOvertimeShiftAllowance = async (parent, args, context, info) => {
    console.log('Inside calculateOvertimeShiftAllowance() function');
    // require apollo server errors
    const { UserInputError,ApolloError } = require('apollo-server-lambda');
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // Organization database connection
    const knex = require('knex');
    // require validation file
    const {validateCalculateShiftAllowanceInputs,getOvertimeSettings,validateOvertimeWorkSchedule,getEmployeeShiftAllowance,getCompensatoryOffBalanceDetailsForOvertime} = require('./overtimeCommonFunctions');
    // require moment 
    const moment = require('moment-timezone');
    //variable declaration
    let orgDb;
    let errResult;
    let validationError = {};
    try{
        // get db connection
        orgDb = knex(context.connection.OrganizationDb);
        // declare input params
        let {employeeId,otStartTime,otEndTime,workScheduleId} = args;
        validationError = validateCalculateShiftAllowanceInputs(args);
        // check validation error is exist or not
        if (Object.keys(validationError).length === 0) {
            // get the overtime settings
            let overtimeSettings = await getOvertimeSettings(orgDb);
            if(Object.keys(overtimeSettings).length > 0){
                if(overtimeSettings.Shift_Allowance_Applicable_For_Overtime){
                    // flag to allow the user to claim overtime from "shift - consideration start date time" when the shift falls on weekoff or holiday
                    let allowRegularHoursOvertimeForWeekoffHoliday = overtimeSettings.Allow_Regular_Hours_Overtime_For_Weekoff_holiday;
                    let validateWorkScheduleInputs = {
                        employeeId: employeeId,
                        otStartTime: otStartTime,
                        allowRegularHoursOvertimeForWeekoffHoliday:allowRegularHoursOvertimeForWeekoffHoliday
                    }
                    let validateOTWorkscheduleResponse = await validateOvertimeWorkSchedule(orgDb,validateWorkScheduleInputs);
                    /** If the overtime start date-time is valid */
                    if(!validateOTWorkscheduleResponse.error){
                        /** Validate overtime end date-time is less than the regular to date-time when the regular hours-overtime claim is applicable for week off and holiday 
                         * and when the overtime is claimed on weekoff or holiday. Because for this duration the shift allowance is not applicable as it will be automatically
                         * handled during payslip generation.*/
                        if (allowRegularHoursOvertimeForWeekoffHoliday === 1 && !(validateOTWorkscheduleResponse.isWorkingDay) && 
                        otStartTime >= validateOTWorkscheduleResponse.currentWorkScheduleDetails.considerationFrom &&
                        otEndTime <= validateOTWorkscheduleResponse.currentWorkScheduleDetails.regularTo){
                            throw ('OT0125');//Shift allowance is not applicable as the overtime is claimed for the weekoff or holiday on or before the regular to date-time
                        }else{
                            let shiftAllowanceEligiblityCalculationInputs = {};
                            shiftAllowanceEligiblityCalculationInputs.employeeId = args.employeeId;
                            shiftAllowanceEligiblityCalculationInputs.otStartTime = validateOTWorkscheduleResponse.currentWorkScheduleDetails.regularTo;
                            shiftAllowanceEligiblityCalculationInputs.otEndTime = args.otEndTime;
                            shiftAllowanceEligiblityCalculationInputs.workScheduleDetail = validateOTWorkscheduleResponse.currentWorkScheduleDetails;
                            shiftAllowanceEligiblityCalculationInputs.action = 'calculateOvertimeShiftAllowance';
                            shiftAllowanceEligiblityCalculationInputs.fetchEmployeeWorkingDaySpecialWageConfig = 1;
                            shiftAllowanceEligiblityCalculationInputs.regularFromDate = moment(validateOTWorkscheduleResponse.currentWorkScheduleDetails.regularFrom).format("YYYY-MM-DD");// convert date time to date format.
                            
                            let eligibilityResponse = await getCompensatoryOffBalanceDetailsForOvertime(orgDb,shiftAllowanceEligiblityCalculationInputs);
                            let isEmployeeEligibleForShiftAllowance = eligibilityResponse.isEmployeeEligibleForShiftAllowance;

                            let shiftAllowance = 0;

                            if(isEmployeeEligibleForShiftAllowance > 0){
                                /**
                                 * manual - employee has to manually apply for shift allowance
                                 * both - refers to manual and auto. auto refers to applying shift allowance for the employee actual regular shift
                                */
                                let shiftMode = ['manual','both'];
                                let calculateShiftAllowanceInputs = {
                                    employeeId: employeeId,
                                    workScheduleId: workScheduleId,
                                    shiftMode: shiftMode,                            
                                    source: 'calculateemployeeshiftallowance'
                                };
                                shiftAllowance = await getEmployeeShiftAllowance(orgDb,calculateShiftAllowanceInputs);
                            }else{
                                shiftAllowance = 0;
                            }

                            // destroy database connection
                            orgDb ? orgDb.destroy() : null;
                            return { errorCode: '', message: 'Shift allowance is calculated successfully for the overtime.',shiftAllowance: (shiftAllowance ? shiftAllowance : 0)};
                        }
                    }else{
                        console.log('Response from the validateOvertimeWorkSchedule() function. ',validateOTWorkscheduleResponse);
                        /** If an error occured during overtime work schedule validation or overtime start date-time is not valid throw an error returned from the function. */
                        throw(validateOTWorkscheduleResponse.error);
                    }
                }else{
                    console.log("Shift allowance cannot be claimed for the overtime as the shift allowance applicable flag is disabled in the overtime settings.");
                    throw('OT0123');// throw error
                }
            }else{
                console.log('Empty or error response is returned from the getOvertimeSettings() function.', overtimeSettings);
                throw('OT0120');// throw error
            }
        }else{
            // throw validation error
            throw ('IVE0000');
        }
    }catch(calculateShiftAllowanceMainCatchError){
        console.log('Error in the calculateOvertimeShiftAllowance() function main catch block',calculateShiftAllowanceMainCatchError);
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
        if(calculateShiftAllowanceMainCatchError === 'IVE0000'){
            console.log('Validation error in the calculateOvertimeShiftAllowance() function.',validationError);
            errResult = commonLib.func.getError('', calculateShiftAllowanceMainCatchError);
            throw new UserInputError(errResult.message, { validationError: validationError });// return response
        }else{
            let errResult = commonLib.func.getError(calculateShiftAllowanceMainCatchError, 'OT0126');
            throw new ApolloError(errResult.message, errResult.code);// return response
        }
    }
};