// resolver function to get the weekly calendar shift details
module.exports.retrieveWeeklyCalenderShiftDetails = async (parent, args, context, info) => {
    console.log('Inside retrieveWeeklyCalenderShiftDetails() function');
    // require apollo errors
    const { ApolloError } = require('apollo-server-lambda');
    // require common constant files
    const { formName } = require('../../../common/appconstants');
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // Organization database connection
    const knex = require('knex');
    // require retrieveMonthlyCalenderShiftDetails
    const { getShiftDetails } = require('./weekOffCommonFunction');
    // require moment-timezone
    const moment = require('moment-timezone');

    let orgDb;
    try {
        //get db connection
        orgDb = knex(context.connection.OrganizationDb);
        // get loggedIn employeeId
        let logInEmpId = context.Employee_Id;
        let isAdmin = 0;
        let isViewRights = 0;
        // Check form view access rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(orgDb, logInEmpId, formName.shiftScheduling, '','UI');
        // check checkRights is not empty json or not
        if (Object.keys(checkRights).length === 0) {
            // if view rights not exists
            let errResult = commonLib.func.getError('', '_DB0100');
            return {
                success: false,
                errorCode: errResult.code,
                message: errResult.message
            }
        } else {
            // check loggedIn employee is  admin or not
            if (checkRights.Role_View === 1 && checkRights.Employee_Role.toLowerCase() === 'admin') {
                isAdmin = 1;
            // if not admin check view rights alone exist or not
            } else if (checkRights.Role_View === 1) {
                isViewRights = 1;
            }
            // check view rights exist or not
            if(isAdmin || isViewRights){
                // get start and end date from input
                let { salaryStartDate, salaryEndDate } = args;
                // check salaryStartDate and salaryEndDate exist or not
                if(salaryStartDate && salaryEndDate){
                    // Form the inputs. Filter month is not required to get the shift roster employees as the salary start date is send as an input
                    let rosterEmployeesArgs = {
                        isAdmin: isAdmin,
                        employeeId: logInEmpId,
                        salaryStartDate: salaryStartDate 
                    };
                    // call common function to get the shift roster employee details based on the salary start date and the login employee admin access.
                    let rosterEmployees = await commonLib.func.getAllEmployeeDetails(orgDb, 'getRosterWeeklyCalendarEmployees', context.Org_Code,
                        process.env.domainName, process.env.region, process.env.hrappProfileBucket, rosterEmployeesArgs, null);
                    if(rosterEmployees.length > 0){
                        // iterate the roster employees and get the shift details
                        for (let empRecord of rosterEmployees){
                            // variable initialization
                            let employeeId = empRecord.employee_id;
                            let dateOfJoin = empRecord.doj;
                            let timeOffDates = [];
                            let shiftDates = [];
                            let shiftMonthStartDate = '';
                            let shiftMonthEndDate = '';
                            let exitDate = empRecord.resignation_date;// employee applied,approved,incomplete status - resignation date
                            // append exitDate with response
                            empRecord.exitDate = exitDate;
                            // get shiftDetails
                            let shiftDetails = await getShiftDetails(employeeId, salaryStartDate, salaryEndDate, orgDb);
                            // get timeOff details
                            let leaveResponse = await commonLib.employees.getLeaves(employeeId, salaryStartDate, salaryEndDate, 'shift-calendar', orgDb);
                            // get compensatory details
                            let compOffResponse = await commonLib.employees.getCompensatoryOff(employeeId, salaryStartDate, salaryEndDate, 'shift-calendar', orgDb);
                            // iterate the leaveResponse and form timeoff dates as an array
                            for (let timeOff of leaveResponse) {
                                // Check shift start and end date difference
                                let startDate = moment(timeOff.Start_Date, 'YYYY-MM-DD');
                                let endDate = moment(timeOff.End_Date, 'YYYY-MM-DD');
                                // Get difference between 2 dates in days
                                let dateDiff = endDate.diff(startDate, 'days');
                                // check dateDiff
                                if (dateDiff === 0) {
                                    // push time timeoff details into an array because the date will be the business working day and not the week off or holiday.
                                    timeOffDates.push(timeOff.Start_Date);
                                } else {
                                    /** If date difference is greater than 0 then get the between dates by adding the one date with the start date and validate the 
                                     * date is either leave date(business working day) or not and push the valid leave date in an array.
                                    */
                                    for (let day = 0; day <= dateDiff; day++) {
                                        let leaveNextDate = moment(timeOff.Start_Date, "YYYY-MM-DD").add(day, 'days').format('YYYY-MM-DD');
                                        let empLeaveCalculationDays = await commonLib.employees.getLeaveTypeLeaveCalculationDays(orgDb,timeOff.LeaveType_Id);
                                        /** If the leave calculation days exist for a leave type then validate the date is leave date or not. Otherwise 
                                         * we should not consider it as the leave date. */ 
                                        if(Object.keys(empLeaveCalculationDays).length > 0){
                                            let leaveDayCountForRequestDate = await commonLib.employees.getTotalDaysBasedOnLeaveCalculationDays(orgDb,employeeId,empLeaveCalculationDays,leaveNextDate,leaveNextDate);
                                            /** If the leave calculation days for a leave type is 'All days of salary month' then the date can be considered as the leave.
                                             *  If the leave calculation days for a leave type is 'Business working day' then the date can be considered as the leave only 
                                             *  when the date is a business working day and it can be considered as the leave date. Or if the date is not a business working 
                                             *  day then date may be either week off or holiday and it should not be considered as the leave date.
                                            */
                                            if(leaveDayCountForRequestDate > 0){
                                                // push time timeoff details into an array
                                                timeOffDates.push(leaveNextDate);
                                            }
                                        }else{
                                            console.log('Empty response from the getLeaveTypeCalculationDays() function, ',empLeaveCalculationDays,' while getting the leave calculation days for a leave-type id,',timeOff.LeaveType_Id);
                                            throw('SS0155');//throw error occurred while fetching the leave calculation days for a leave type
                                        }
                                    }
                                }
                            }
                            // iterate the compOffResponse and get the date push it into timeOffDates array
                            for (let compOff of compOffResponse) {
                                // push time compOff details into a timeOffDates array
                                timeOffDates.push(compOff.Compensatory_Date);
                            }   
                            // get weekoff count and week off details
                            for (let shift of shiftDetails) {
                                // push shift dates into an array
                                shiftDates.push(shift.Shift_Date);
                                // check timeoff date exist in the shift details
                                if (timeOffDates.includes(shift.Shift_Date) === true) {
                                    shift.Time_Off = 1;
                                } else {
                                    shift.Time_Off = 0;
                                }
                            }
                            // check DOJ is greater than salaryStartDate the consider DOJ as start date
                            if (new Date(dateOfJoin) > new Date(salaryStartDate)) {
                                shiftMonthStartDate = dateOfJoin;
                            } else {
                                shiftMonthStartDate = salaryStartDate;
                            }
                            // check employee's resignation exit date is exist. If yes check exit date with the salaryEndDate.
                            // If exit date is less than salaryEndDate then consider exit date as end date
                            if (exitDate && (new Date(exitDate) < new Date(salaryEndDate))) {
                                shiftMonthEndDate = exitDate;
                            } else {
                                shiftMonthEndDate = salaryEndDate
                            }
                            // get non scheduled shift dates based on the salaryStartDate and salaryEndDate
                            // Check shift start and end date difference
                            shiftMonthStartDate = moment(shiftMonthStartDate, 'YYYY-MM-DD');
                            shiftMonthEndDate = moment(shiftMonthEndDate, 'YYYY-MM-DD');
                            // Get difference between 2 dates in days
                            let totalDays = shiftMonthEndDate.diff(shiftMonthStartDate, 'days');
                            // if date difference is greater than 0 then get the between dates by adding the one date with the start date 
                            for (let day = 0; day <= totalDays; day++) {
                                let tmpDate = moment(shiftMonthStartDate, "YYYY-MM-DD").add(day, 'days').format('YYYY-MM-DD');
                                //check if the date is exist in shiftDates array if yes then shift is scheduled for that date otherwise
                                // consider the dates as non shift dates
                                if (shiftDates.includes(tmpDate) === false) {
                                    // form json record for non shift schedule date
                                    let nonshiftRecordJson = {};
                                        nonshiftRecordJson.Shift_Schedule_Id = null;
                                        nonshiftRecordJson.Employee_Id = employeeId;
                                        nonshiftRecordJson.Shift_Date = tmpDate;
                                        shiftDetails.push(nonshiftRecordJson);
                                    }
                            }
                            // append shiftDetails with response
                            empRecord.shiftDetails = shiftDetails;
                        }
                        // return response back to UI
                        return { success: true,errorCode: '', message: 'Employee shift details retrieved successfully', shiftDetails:JSON.stringify(rosterEmployees) }
                    }else{
                        console.log('No shift roster employees found.');
                        let errResult = commonLib.func.getError('', 'SS0156');
                        return {
                            success: false,
                            errorCode: errResult.code,
                            message: errResult.message
                        }
                    }
                }else{
                    console.log('Input params Salary start date and end date does not exist.');
                    let errResult = commonLib.func.getError('', 'SS0141');
                    return {
                        success: false,
                        errorCode: errResult.code,
                        message: errResult.message
                    }
                }
            } else{
                console.log('Login employee is not admin and not having view access.');
                // if view rights not exists
                let errResult = commonLib.func.getError('', '_DB0100');
                return {
                    success: false,
                    errorCode: errResult.code,
                    message: errResult.message
                }
            }            
        }
    } catch (retrieveWeeklyCalenderShiftDetailsMainCatch) {
        console.log('Error in retrieveWeeklyCalenderShiftDetails() function main catch block.', retrieveWeeklyCalenderShiftDetailsMainCatch);
        let errResult;
        if(retrieveWeeklyCalenderShiftDetailsMainCatch === 'SS0155'){
            errResult = commonLib.func.getError('', 'SS0155');
        }else{
            errResult = commonLib.func.getError('', 'SS0141');
        }
        throw new ApolloError(errResult.message, errResult.code)
    } finally {
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
    }
};