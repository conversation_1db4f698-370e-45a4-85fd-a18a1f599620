// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const commonValidation=require('./commonvalidation');

// variable declarations
let validationError = {};

module.exports = {
    inputValidation : async(args,context) =>{
        // variable declaration
        let {Performance_Management_Mode,Maximum_Rating,GoalSettings_ReminderOne,GoalSettings_ReminderTwo,RatingUpdate_ReminderOne,RatingUpdate_ReminderTwo,Goal_Email_Subject,Goal_Email_Content,Rating_Email_Subject,Rating_Email_Content,Enable_Email_Notification,Present_Only_Team_Members_To_Managers} = args;
        
        // validate performance management mode
        if (!Performance_Management_Mode || !(Performance_Management_Mode.toLowerCase() === 'skills' || Performance_Management_Mode.toLowerCase() === 'goals')) {
            validationError['IVE0132'] = commonLib.func.getError('', 'IVE0132').message;
        }
        // validate performance ratings
        if (!Maximum_Rating || !(Maximum_Rating === 5 || Maximum_Rating === 10)) {
            validationError['IVE0133'] = commonLib.func.getError('', 'IVE0133').message;
        }
        // validate goal settings notification day
        if (!GoalSettings_ReminderOne || !(GoalSettings_ReminderOne > 0 && GoalSettings_ReminderOne <= 28)) {
            validationError['IVE0134'] = commonLib.func.getError('', 'IVE0134').message;
        }
        // validate goal settings reminder two
        if(GoalSettings_ReminderTwo > 0 ){
            if (!(GoalSettings_ReminderTwo <= 28)) {
                validationError['IVE0213'] = commonLib.func.getError('', 'IVE0213').message;
            }else{
                if(GoalSettings_ReminderOne){
                    if(GoalSettings_ReminderOne === GoalSettings_ReminderTwo){
                        validationError['IVE0215'] = commonLib.func.getError('', 'IVE0215').message;
                    }
                }
            }
        }
        
        // validate rating update reminder one
        if (!RatingUpdate_ReminderOne || !(RatingUpdate_ReminderOne > 0 && RatingUpdate_ReminderOne <= 28)) {
            validationError['IVE0135'] = commonLib.func.getError('', 'IVE0135').message;
        }
        // validate rating update reminder two
        if(RatingUpdate_ReminderTwo > 0 ){
            if (!(RatingUpdate_ReminderTwo  <= 28)) {
                validationError['IVE0214'] = commonLib.func.getError('', 'IVE0214').message;
            }else{
                if(RatingUpdate_ReminderOne){
                    if(RatingUpdate_ReminderOne === RatingUpdate_ReminderTwo){
                        validationError['IVE0216'] = commonLib.func.getError('', 'IVE0216').message;  
                    }
                }
            }
        }

        // validate goal email subject
        if(Goal_Email_Subject){
            let validate = commonValidation.checkLength(Goal_Email_Subject,5,150);
            if (!validate) {
                validationError['IVE0136'] = commonLib.func.getError('', 'IVE0136').message2;
            }
        }else{
            validationError['IVE0136'] = commonLib.func.getError('', 'IVE0136').message1;
        }
        // validate goal email content
        if(Goal_Email_Content){
            let validate = commonValidation.checkLength(Goal_Email_Content,5,500);
            if (!validate) {
                validationError['IVE0137'] = commonLib.func.getError('', 'IVE0137').message2;
            }
        }else{
            validationError['IVE0137'] = commonLib.func.getError('', 'IVE0137').message1;
        }
        // validate rating email subject
        if(Rating_Email_Subject){
            let validate = commonValidation.checkLength(Rating_Email_Subject,5,150);
            if (!validate) {
                validationError['IVE0138'] = commonLib.func.getError('', 'IVE0138').message2;
            }
        }else{
            validationError['IVE0138'] = commonLib.func.getError('', 'IVE0138').message1;
        }
        // validate rating email content
        if(Rating_Email_Content){
            let validate = commonValidation.checkLength(Rating_Email_Content,5,500);
            if (!validate) {
                validationError['IVE0139'] = commonLib.func.getError('', 'IVE0139').message2;
            }
        }else{
            validationError['IVE0139'] = commonLib.func.getError('', 'IVE0139').message1;
        }
        // validate enable email notification
        if (!Enable_Email_Notification || !(Enable_Email_Notification.toLowerCase() === 'yes' || Enable_Email_Notification.toLowerCase() === 'no')) {
            validationError['IVE0140'] = commonLib.func.getError('', 'IVE0140').message;
        }
        // validate present only team members to managers
        if (!Present_Only_Team_Members_To_Managers || !(['Yes','No'].includes(Present_Only_Team_Members_To_Managers))) {
            validationError['IVE0212'] = commonLib.func.getError('', 'IVE0212').message;
        }

        return validationError;
    },
    employeeGoalsAndRatingInputValidation : async(args,action) =>{
        if(action==='validateMonthYear'){
            let { year,month }=args;
            // validate month
            if (!month || !(month > 0 && month <= 12)) {
                validationError['IVE0144'] = commonLib.func.getError('', 'IVE0144').message;
            }
            // validate year
            if(year){
                let validate = commonValidation.yearValidation(year);
                if (!validate) {
                    validationError['IVE0143'] = commonLib.func.getError('', 'IVE0143').message;
                }
            }else{
                validationError['IVE0143'] = commonLib.func.getError('', 'IVE0143').message;
            }
        }
        else if(action==='validateMonthYearEmployee'){
            let { year,month,employeeId }=args;
            // validate employeeId
            if (!employeeId) {
                validationError['IVE0083'] = commonLib.func.getError('', 'IVE0083').message;
            }
            // validate month
            if (!month || !(month > 0 && month <= 12)) {
                validationError['IVE0144'] = commonLib.func.getError('', 'IVE0144').message;
            }
            // validate year
            if(year){
                let validate = commonValidation.yearValidation(year);
                if (!validate) {
                    validationError['IVE0143'] = commonLib.func.getError('', 'IVE0143').message;
                }
            }else{
                validationError['IVE0143'] = commonLib.func.getError('', 'IVE0143').message;
            }
        }
        else if(action==='listEmployeeAchievements'){
            let { startYear,endYear,startMonth,endMonth }=args;
            // validate start year
            if (startYear) {
                let validate = commonValidation.yearValidation(startYear);
                if (!validate) {
                    validationError['IVE0143'] = commonLib.func.getError('', 'IVE0143').message1;
                }
            }
            else{
                validationError['IVE0143'] = commonLib.func.getError('', 'IVE0143').message1;
            }
            // validate end year
            if (endYear) {
                let validate = commonValidation.yearValidation(endYear);
                if (!validate) {
                    validationError['IVE0143'] = commonLib.func.getError('', 'IVE0143').message2;
                }
            }
            else{
                validationError['IVE0143'] = commonLib.func.getError('', 'IVE0143').message2;
            }
            // validate start month
            if (startMonth) {
                if (!(startMonth > 0 && startMonth <= 12)) {
                    validationError['IVE0144'] = commonLib.func.getError('', 'IVE0144').message1;
                }
            }
            else{
                validationError['IVE0144'] = commonLib.func.getError('', 'IVE0144').message1;
            }
            // validate end month
            if (endMonth) {
                if (!(endMonth > 0 && endMonth <= 12)) {
                    validationError['IVE0144'] = commonLib.func.getError('', 'IVE0144').message2;
                }
            }
            else{
                validationError['IVE0144'] = commonLib.func.getError('', 'IVE0144').message2;
            }            
        }
        return validationError;
    },
    addEmployeeGoalsAndAchievementInputValidation : async(args,isAdmin,isManager,loginEmpId) =>{
        let validationError = {};
        try{
            // validate month
            if (!args.month || !(args.month >= 1 && args.month <= 12)) {
                validationError['IVE0144'] = commonLib.func.getError('', 'IVE0144').message;
            }

            // validate year
            if(args.year){
                let validate = commonValidation.yearValidation(args.year);
                if (!validate) {
                    validationError['IVE0143'] = commonLib.func.getError('', 'IVE0143').message;
                }
            }else{
                validationError['IVE0143'] = commonLib.func.getError('', 'IVE0143').message;
            }

            // validate employee id
            if(!args.employeeIds || !(args.employeeIds.length > 0)){
                validationError['IVE0083'] = commonLib.func.getError('', 'IVE0083').message;
            }

            // validate goal id
            if(!args.goalIds || !(args.goalIds.length > 0)){
                validationError['IVE0146'] = commonLib.func.getError('', 'IVE0146').message;
            }
            else {
                // max 25 goals can be set for the employee
                if(args.goalIds.length>25){
                    validationError['IVE0156'] = commonLib.func.getError('','IVE0156').message;
                }
            }

            // validate reviewer id
            if(!args.reviewerId){
                validationError['IVE0147'] = commonLib.func.getError('', 'IVE0147').message1;
            }else{
                // When the login employee id is manager but if the reviewer id does not equal to the login employee id
                if(isAdmin===0 && isManager===1 && (args.reviewerId!==loginEmpId)){
                    validationError['IVE0147'] = commonLib.func.getError('', 'IVE0147').message2;
                }
            }

            return validationError;
        }catch(addEmployeePerformanaceGoalsCatchError){
            console.log('Error in the addEmployeeGoalsAndAchievementInputValidation() function.',addEmployeePerformanaceGoalsCatchError);
            throw addEmployeePerformanaceGoalsCatchError;
        }
    },
    updateEmployeeGoalsAndAchievementInputValidation : async(args,action,isAdmin,logInEmpId) =>{
        let validationError = {};
        try{
            if(action.toLowerCase()==='updategoals'){
                // validate input goalId array
                if(!args.goalIdArray || !(args.goalIdArray.length>0)){
                    validationError['IVE0146'] = commonLib.func.getError('', 'IVE0146').message1;
                }
                else {
                    // max 25 goals can be set for the employee
                    if(args.goalIdArray.length>25){
                        validationError['IVE0156'] = commonLib.func.getError('','IVE0156').message;
                    }
                }
                // validate reviewer id
                if(!args.reviewerId){
                    validationError['IVE0147'] = commonLib.func.getError('', 'IVE0147').message1;
                }else{
                    // If loggedIn employee is manager then validate reviewerid should be equal to loggedIn employee else return error
                    if(isAdmin===0){
                        if(args.reviewerId!==logInEmpId){
                            validationError['IVE0147'] = commonLib.func.getError('', 'IVE0147').message2;
                        }
                    }
                }
            }
            else if(action.toLowerCase()==='addratings' || action.toLowerCase()==='updateratings'){
                // validate overallRating
                if(!args.overallRating){
                    validationError['IVE0148'] = commonLib.func.getError('', 'IVE0148').message1;
                }
                else{
                    if (!(args.overallRating >= 1 && args.overallRating <= 10)) {
                        validationError['IVE0148'] = commonLib.func.getError('', 'IVE0148').message2;
                    }
                }
                // validate comments
                if(args.comments){
                    let validate = commonLib.commonValidation.checkLength(args.comments,5,600);
                    let commentValidate=commonLib.commonValidation.commentValidation(args.comments)
                    if (!validate) {
                        validationError['IVE0037'] = commonLib.func.getError('', 'IVE0037').message4;
                    }
                    if(!commentValidate){
                        validationError['IVE0037'] = commonLib.func.getError('', 'IVE0037').message2;
                    }
                }
                // validate input rating & goal data
                if(!args.goalsAndRating || !(args.goalsAndRating.length>0)){
                    validationError['IVE0149'] = commonLib.func.getError('', 'IVE0149').message;
                }
                else {
                    // max 25 goals can be set for the employee
                    if(args.goalsAndRating.length>25){
                        validationError['IVE0156'] = commonLib.func.getError('','IVE0156').message;
                    }else{
                        //Get the goals and ratings array where the ratings are 0 and null
                        let invalidRatings = args.goalsAndRating.filter(goalRatingDetail => !(goalRatingDetail.rating))
                        if(invalidRatings.length > 0){
                            validationError['IVE0149'] = commonLib.func.getError('','IVE0149').message2;
                        }
                    }
                }
            }
            else{
                console.log('Invalid action');
                throw 'EPM0011';
            }
            return validationError;
        }
        catch(mainCatchError){
            console.log('Error in updateEmployeeGoalsAndAchievementInputValidation function main catch',mainCatchError);
            throw 'EPM0011';
        }
    }
};
