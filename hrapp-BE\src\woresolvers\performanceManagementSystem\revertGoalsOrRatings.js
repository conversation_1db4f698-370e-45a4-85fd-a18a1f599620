//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make a database connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require common constant files
const { formName,systemLogs } = require('../../../common/appconstants');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
//Require validation file
const {employeeGoalsAndRatingInputValidation} = require('../../../common/performanceManagementValidation');
//Require common function
const {goalsRatingUnPublishableEmpIds} = require('../../../common/performanceManagementCommonFunction');

//Revert goals or ratings based on the action
module.exports.revertGoalsOrRatings = async (parent, args, context, info) => {
    console.log("Inside revertGoalsOrRatings() function.");
    
    let organizationDbConnection;
    let errResult;
    let validationError = {};

    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const loginEmployeeId = context.Employee_Id;

        //Check Goals and achievement form access rights.
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.goalsAndAchievement, '', 'UI');

        //If the login employee is admin or manager and having update access for the 'Goals and achievement' form
        if ((Object.keys(checkRights).length >0 && checkRights.Role_Update===1) && (checkRights.Is_Manager===1 || checkRights.Employee_Role==='admin')) {
            //Function to validate inputs
            validationError=await employeeGoalsAndRatingInputValidation(args,'validateMonthYear');
            if(Object.keys(validationError).length > 0 || (!(args.employeeId) 
            || (args.employeeId.length===0) || args.employeeId<=0) || 
            (!(['revertGoals','revertRatings'].includes(args.action)))){
                console.log('Validation error in the revert goals or ratings function.',validationError);
                throw('_EC0007');
            }else{
                let employeeId = args.employeeId;
                let performanceAssessmentMonth = args.month;
                let performanceAssessmentYear = args.year;
                let action = args.action.toLowerCase();
                let validEmployeeIds = [];
                let unpublishableEmpIds = await goalsRatingUnPublishableEmpIds(organizationDbConnection,loginEmployeeId,checkRights.Employee_Role,action,args);
                if(unpublishableEmpIds && unpublishableEmpIds.length > 0){
                    return (
                    organizationDbConnection
                    .transaction(function (trx) {
                            //Get the employee id where the employee performance details should exist in the future
                            return(
                            organizationDbConnection(ehrTables.performanceGoalAchievement)
                            .distinct()
                            .pluck('Employee_Id')
                            .whereIn('Employee_Id',employeeId)
                            .where(qb => {
                                qb.where('Performance_Assessment_Month','>',performanceAssessmentMonth)
                                qb.where('Performance_Assessment_Year','=',performanceAssessmentYear)
                                qb.orWhere((qb1)=>{
                                    qb1.where('Performance_Assessment_Year','>',performanceAssessmentYear);
                                })
                            })
                            .transacting(trx)
                            .then(async(existDetails) => {
                                if(!(existDetails) || existDetails.length === 0){
                                    existDetails = [];
                                }
                                validEmployeeIds = employeeId.filter(function(obj) { return existDetails.indexOf(obj) == -1; });
                                if(validEmployeeIds && validEmployeeIds.length > 0){
                                    //Get the employee timezone based on location
                                    let loginEmployeeCurrentDateTime = await commonLib.func.getEmployeeTimeZone(loginEmployeeId, organizationDbConnection, 1);
                                    if(action === 'revertgoals'){
                                        //Revert the employee goals details
                                        return(
                                        organizationDbConnection(ehrTables.performanceGoalAchievement)
                                        .update({
                                            'Goal_Publish_Status' : 'Unpublished',
                                            'Updated_On': loginEmployeeCurrentDateTime,
                                            'Updated_By': loginEmployeeId
                                        })
                                        .whereIn('Employee_Id',validEmployeeIds)
                                        .where('Performance_Assessment_Month',performanceAssessmentMonth)
                                        .where('Performance_Assessment_Year',performanceAssessmentYear)
                                        .where('Assessment_Status','Goals Assigned')
                                        .where('Goal_Publish_Status','Published')
                                        .transacting(trx)
                                        .then((goalsRevertResult) => {
                                            if(goalsRevertResult){
                                                return 'success';
                                            }else{
                                                console.log('Goals status is not reverted.',goalsRevertResult);
                                                throw 'EPM0035'
                                            }
                                        }));
                                    }else{
                                        //Revert the employee rating details
                                        return(
                                        organizationDbConnection(ehrTables.performanceGoalAchievement)
                                        .update({
                                            'Rating_Publish_Status' : 'Unpublished',
                                            'Updated_On': loginEmployeeCurrentDateTime,
                                            'Updated_By': loginEmployeeId
                                        })
                                        .whereIn('Employee_Id',validEmployeeIds)
                                        .where('Performance_Assessment_Month',performanceAssessmentMonth)
                                        .where('Performance_Assessment_Year',performanceAssessmentYear)
                                        .where('Assessment_Status','Rated')
                                        .where('Rating_Publish_Status','Published')
                                        .transacting(trx)
                                        .then((ratingsRevertResult) => {
                                            if(ratingsRevertResult){
                                                return 'success';
                                            }else{
                                                console.log('Rating status is not reverted.',ratingsRevertResult);
                                                throw 'EPM0035'
                                            }
                                        }));
                                    }
                                }else{
                                    console.log('No employee(s) found to revert goals or ratings',validEmployeeIds,' and employee Id exist in future:',existDetails);
                                    throw 'EPM0034';
                                }
                            }))
                    })
                    .then(async() => {
                        let trackingMessage = '';
                        if(action === 'revertgoals'){
                            trackingMessage = 'Revert employee goals for month, '+performanceAssessmentMonth+', '+performanceAssessmentYear+' and for the employee id-'+validEmployeeIds;
                        }else{
                            trackingMessage = 'Revert employee ratings for month, '+performanceAssessmentMonth+', '+performanceAssessmentYear+' and for the employee id-'+validEmployeeIds;
                        }
                        
                        //System log inputs. Example system log message: Delete Goals And Achievement  - 50
                        let systemLogParams = {
                            action: systemLogs.roleUpdate,
                            userIp: context.User_Ip,
                            employeeId: loginEmployeeId,
                            formName: formName.goalsAndAchievement,
                            message: trackingMessage,
                            organizationDbConnection: organizationDbConnection,
                            uniqueId: validEmployeeIds
                        };

                        //call the function to add the system log
                        await commonLib.func.createSystemLogActivities(systemLogParams);

                        //Destroy database connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;

                        return { errorCode: '', message: 'Employee goals or ratings reverted successfully.'};
                    })
                    .catch(revertCatchError => {
                        console.log('Error occurred in the revertGoalsOrRatings() function .catch block. ',revertCatchError);
                        errResult = commonLib.func.getError(revertCatchError, 'EPM0127');
                        //Destroy database connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        //Return error response
                        throw new ApolloError(errResult.message,errResult.code);
                    }));
                }else{
                    console.log('Login employee is not the eligible approver or goals or ratings status is already updated,',args);
                    throw('EPM0037');
                }
            }
        }else{
            throw('_DB0102');
        }
    }
    catch(revertGoalsOrRatingsMainCatchError) {
        console.log('Error in the revertGoalsOrRatings() function main catch block. ',revertGoalsOrRatingsMainCatchError);
        //Destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(revertGoalsOrRatingsMainCatchError, 'EPM0036');
        //Return error response
        throw new ApolloError(errResult.message,errResult.code);
    }
};