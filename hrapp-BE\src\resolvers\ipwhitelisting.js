// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require validation file
const inputValidation = require('../resolvers/forminputvalidation/ipwhitelistingformvalidation');
// require common constant files
const constants = require('../../common/appconstants');
// Organization database connection
var knex = require('knex');
// require table alias
const tables = require('../../common/tablealias');
// require common function for ipwhitelisting
const commonFunction = require('../resolvers/commonFunctions/ipwhitelistingcommonfunction');
// variable declarations
var errResult,validationError,systemLogParams = {};
var organizationDbConnection, connection =  '';
var ehrTables = tables.ehrTables;
var checkRights = false;
// resolver definition
const resolvers = {
    Query: {
        listWhitelistedIp: async (parent, args, context, info) => {
            try{
                // Append the database name in connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                // to check form view access rights
                checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, context.Employee_Id, constants.formName.ipWhitelist, constants.roles.roleView);
                //check add rights exists
                if (checkRights === true) {
                    // get whitelisted IP address and its details
                    return(
                        organizationDbConnection(ehrTables.ipAddressWhitelist + ' as IPAW')                        
                        .select('IPAW.Location_Id', 'LOC.Location_Name','IPAW.Description',
                        organizationDbConnection.raw('GROUP_CONCAT(WIA.Ip_Address) as Ip_Address'))
                        .leftJoin(ehrTables.whitelistedIps + ' as WIA', 'WIA.Location_Id','IPAW.Location_Id')
                        .leftJoin(ehrTables.location + ' as LOC', 'LOC.Location_Id', 'IPAW.Location_Id')
                        .groupBy('IPAW.Location_Id')
                        .orderBy('IPAW.Added_On')
                        .then(whiteListedIps =>{
                            // get all configured IP Address
                            return(
                                organizationDbConnection(ehrTables.whitelistedIps + ' as WIA')
                                .select(organizationDbConnection.raw('GROUP_CONCAT(WIA.Ip_Address) as Ip_Addresses'))
                                .then(allIpAddress =>{
                                    // get the IP Restriction Flag from organization setting
                                    return(
                                        organizationDbConnection(ehrTables.orgDetails)
                                        .select('IP_Address_Restriction as IPAddressRestriction')
                                        .where('Org_Code',context.Org_Code)
                                        .then(ipRestriction =>{
                                            var isRestrict = ipRestriction[0] ? ipRestriction[0].IPAddressRestriction : 0;
                                            // check whiteListedIps array length
                                            if (whiteListedIps.length > 0) {
                                                for (var ipAddress of whiteListedIps) {
                                                    ipAddress.Ip_Address = ipAddress.Ip_Address.split(',');
                                                }
                                            }
                                            var allIPAddresses = [];
                                            // check allIpAddress array length
                                            if (allIpAddress.length > 0) {
                                                allIPAddresses = allIpAddress[0].Ip_Addresses ? allIpAddress[0].Ip_Addresses.split(',') : [];
                                            }
                                            //return success response to UI
                                            return { errorCode: '', message: 'Whitelisted IP Address listed successfully', whiteListedIpAddress: JSON.stringify(whiteListedIps), ipAddressResction: isRestrict,allConfiguredIPAddressed: allIPAddresses }
                                        })
                                    )                                    
                                })
                            )                            
                        }).catch(function (listInsError){
                            console.log('Error in listWhitelistedIp() function .cath block',listInsError);
                            errResult = commonLib.func.getError(listInsError, 'SE0101');
                            throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, whiteListedIpAddress: [], ipAddressResction :null, allConfiguredIPAddressed : [] }));
                        })
                        // close db connection
                        .finally(() => {
                            organizationDbConnection.destroy();
                        })
                    )
                } else if (checkRights === false) {
                    // throw error if view rights is not exists
                    throw ('_DB0100');
                } else {
                    // throw error
                    throw (checkRights);
                }
            } catch (listError) {
                console.log('Error in listWhitelistedIp() function main catch block',listError);
                // access denied error
                if (listError === '_DB0100') {
                    errResult = commonLib.func.getError('', '_DB0100');
                } else if (listError instanceof Object) { // is to check error object is json or not
                    errResult = listError;
                } else {
                    errResult = commonLib.func.getError('', 'SE0001');
                }
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                // return response
                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, whiteListedIpAddress: [], ipAddressResction:null, allConfiguredIPAddressed: [] }));
            }
        }
    },
    Mutation : {
        // Configure Ip Address to restrict the employees : TODO
        whitelistIPAddress: async (parent, args, context, info) => {
            try{ 
                // Append the database name in connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                // get employeeId from context object(Taken from token itself)
                let employeeId = context.Employee_Id; 
                let locationId = args.locationId;   
                // default check add rights   
                var rightsToCheck = constants.roles.roleAdd;
                // if isEdit is 1 then have to check the edit rights otherwise add rights
                if (args.isEdit){
                    rightsToCheck = constants.roles.roleUpdate;
                }        
                // to check form access rights
                checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, constants.formName.ipWhitelist, rightsToCheck);
                //check add rights exists
                if (checkRights === true) {  
                    // validate the inputs
                    validationError = inputValidation.validateIpWhitelistForm(args);
                    // check validation error exists or not
                    if (Object.keys(validationError).length === 0){
                        // Call function getEmployeeTimeZone() to get current date and time based on employee time zone 
                        // Send employeeId ,organizationDbConnection and another flag as 1(This flag is to get the time zone date with time)
                        let empTimeZone = await commonLib.func.getEmployeeTimeZone(employeeId,organizationDbConnection,1);
                        // get location name to update the system log activities
                        let locationName = await commonFunction.getLocation(locationId, organizationDbConnection);
                        // form inputs to update system log activities commonly
                        systemLogParams = {
                            action: (args.isEdit && args.isEdit ===1) ? constants.systemLogs.roleUpdate : constants.systemLogs.roleAdd,
                            userIp: context.User_Ip,
                            employeeId: employeeId,
                            formName: constants.formName.ipWhitelist,
                            trackingColumn: locationName,
                            organizationDbConnection: organizationDbConnection,
                            uniqueId: locationId
                        }
                        // Check if isEdit is 0 or 1. If 1 perform edit action otherwise add action
                        if(args.isEdit){
                            // form input params
                            let mainTableParams = {
                                Location_Id: locationId,
                                Description: args.description,
                                Updated_By: employeeId
                            };
                            // Check if empTimeZone is not empty/null/undefine
                            if (empTimeZone) {
                                mainTableParams.Updated_On = empTimeZone;
                            }  
                            // update the main and sub table record
                            return(
                                organizationDbConnection
                                .transaction(function(trx){
                                    // update details in main table (ip_address_whitelisting)
                                    return(
                                        organizationDbConnection(ehrTables.ipAddressWhitelist)
                                        .update(mainTableParams)
                                        .where('Location_Id',locationId)
                                        .transacting(trx)
                                        .then(updateMainTableRecord =>{
                                            // check record is exist or not
                                            if (updateMainTableRecord){
                                                // remove and re-insert sub table record because it is in one to many relationship
                                                return(
                                                    organizationDbConnection(ehrTables.whitelistedIps)
                                                    .del()
                                                    .where('Location_Id', locationId)
                                                    .transacting(trx)
                                                    .then(delSubTableRecord => {
                                                        let ipAddresses = [];
                                                        // form IP Address
                                                        ipAddresses = args.ipAddresses.map(field => ({
                                                            Location_Id: locationId,
                                                            Ip_Address: field
                                                        }))
                                                        // fre-insert records in subtable (whitelisted_ip_addresses)
                                                        return(
                                                            organizationDbConnection(ehrTables.whitelistedIps)
                                                            .insert(ipAddresses)
                                                            .transacting(trx)
                                                            .then(async (insSUbTableRecord) =>{
                                                                // call function createSystemLogActivities() to update system log activities
                                                                await commonLib.func.createSystemLogActivities(systemLogParams);
                                                                // return success response to UI
                                                                return { errorCode: '', message: 'IP Address configuration updated successfully', validationError: null }
                                                            })
                                                        )
                                                    })
                                                )
                                            }else{
                                                // throw error if record is doesnot exists
                                                throw ('_EC0001')
                                            }
                                        })
                                    )
                                })
                                //return the success result to user
                                .then(function (result) {
                                    return result;
                                })
                                //catch db-connectivity errors
                                .catch(function (addInsError) {
                                    console.log('Error in whitelistIPAddress edit function .catch() block', addInsError);
                                    if (addInsError.code === 'ER_DUP_ENTRY' && (addInsError.sqlMessage && addInsError.sqlMessage.search('Location_Id')) !== -1) {
                                        errResult = commonLib.func.getError('', 'SE0105');
                                    } else if (addInsError.code === 'ER_DUP_ENTRY' && (addInsError.sqlMessage && addInsError.sqlMessage.search('Ip_Address')) !== -1) {
                                        errResult = commonLib.func.getError('', 'SE0106');
                                    } 
                                    (addInsError.code == '_EC0001') 
                                    ?  
                                        errResult = commonLib.func.getError('', '_EC0001')  
                                    : 
                                        errResult = commonLib.func.getError(addInsError, 'SE0104')
                                    
                                    // return response to UI
                                    throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, validationError: null }));
                                })
                                /**close db connection */
                                .finally(() => {
                                    organizationDbConnection.destroy();
                                })
                            )
                        }else{                        
                            // form input params
                            let mainTableParams = {                            
                                Location_Id: locationId,
                                Description: args.description,
                                Added_By: employeeId
                            };
                            // Check if empTimeZone is not empty/null/undefine
                            if(empTimeZone){
                                mainTableParams.Added_On = empTimeZone;
                            }                        
                            // Insert records in both main and sub tables
                            return(
                                organizationDbConnection
                                .transaction(function(trx){
                                    // insert record in main table (ip_address_whitelisting)
                                    return(
                                        organizationDbConnection(ehrTables.ipAddressWhitelist)
                                        .insert(mainTableParams)
                                        .transacting(trx)
                                        .then(insIpAddressMain =>{
                                            // form and insert records in subtable (whitelisted_ip_addresses)
                                            let ipAddresses = [];
                                            // form IP Address
                                            ipAddresses = args.ipAddresses.map(field=>({
                                                Location_Id : locationId,
                                                Ip_Address : field
                                            }))
                                            return(
                                                organizationDbConnection(ehrTables.whitelistedIps)
                                                .insert(ipAddresses)
                                                .transacting(trx)
                                                .then(async (insIpAddressSub) => {                                                    
                                                    // call function createSystemLogActivities() to update system log activities
                                                    await commonLib.func.createSystemLogActivities(systemLogParams);
                                                    // return success response to UI
                                                    return { errorCode : '',message : 'IP Address configuration added successfully',validationError: null }
                                                })
                                            )
                                        })
                                    )
                                })
                                //return the success result to user
                                .then(function (result) {
                                    return result;
                                })
                                //catch db-connectivity errors
                                .catch(function (addInsError) { 
                                    console.log('Error in whitelistIPAddress add function .catch() block', addInsError);
                                    if (addInsError.code === 'ER_DUP_ENTRY' && (addInsError.sqlMessage && addInsError.sqlMessage.search('Location_Id')) !== -1){
                                        errResult = commonLib.func.getError('', 'SE0105');
                                    } else if (addInsError.code === 'ER_DUP_ENTRY' && (addInsError.sqlMessage && addInsError.sqlMessage.search('Ip_Address')) !== -1){
                                        errResult = commonLib.func.getError('', 'SE0106');
                                    }else{
                                        errResult = commonLib.func.getError(addInsError, 'SE0102');
                                    }                             
                                    // return response to UI
                                    throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, validationError: null }));
                                })
                                /**close db connection */
                                .finally(() => {
                                    organizationDbConnection.destroy();
                                })
                            )
                        }
                    } else {
                        // throw validation error
                        throw ('IVE0000');
                    }
                } else if (checkRights === false) {
                    // throw error if create/edit rights is not exists
                    throw (args.isEdit === 1 ? '_DB0102' : '_DB0101');
                    
                } else {
                    // throw error
                    throw (checkRights);
                }
            } catch (addError) {
                console.log('Error in whitelistIPAddress() function main catch block', addError);
                // access denied error
                if (addError === '_DB0101') { // add rights
                    errResult = commonLib.func.getError('', '_DB0101');
                } else if (addError === '_DB0102') { // Edit rights
                    errResult = commonLib.func.getError('', '_DB0102');
                }else if (addError === 'IVE0000') {
                    errResult = commonLib.func.getError('', 'IVE0000');
                    // return response
                    throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, validationError:validationError }));
                } else if (addError instanceof Object) { // is to check error object is json or not
                    errResult = addError;
                } else {
                    // check isEdit is 0 or 1. based on that return errorcode
                    errResult = (args.isEdit === 1) ? commonLib.func.getError('', 'SE0004') : commonLib.func.getError('', 'SE0002');
                   
                }
                // destroy database connection if exists
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                // return response
                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, validationError : null }));
            }
        },
        // delete the configured IP address
        deletewhitelistedIPAddress: async (parent, args, context, info) => {
            console.log('Inside deletewhitelistedIPAddress.js function.');
            try{
                // Append the database name in connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                // get employeeId from context object(Taken from token itself)
                let employeeId = context.Employee_Id;
                let locationId = args.locationId;
                // to check form access rights
                checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, constants.formName.ipWhitelist, constants.roles.roleDelete);
                //check add rights exists
                if (checkRights === true) {
                    // delete the record from main and sub tables
                    return(
                        organizationDbConnection
                        .transaction(function(trx){
                            // get the no ofconfiguration record in table
                            return(
                                organizationDbConnection(ehrTables.ipAddressWhitelist)
                                .count('Location_Id as recordCount')
                                .transacting(trx)
                                .then(ipConfigCount => {                                    
                                    // delete the record from main table ip_address_whitelisting
                                    return(
                                        organizationDbConnection(ehrTables.ipAddressWhitelist)
                                        .del()
                                        .where('Location_Id',locationId)
                                        .transacting(trx)
                                        .then(delMainTableRecord =>{
                                            // check record exists or not
                                            if (delMainTableRecord === 0 ){
                                                // throw error if record not exists
                                                throw ('_EC0001')
                                            }else{
                                                // delete records from sub table whitelisted_ip_addresses
                                                return(
                                                    organizationDbConnection(ehrTables.whitelistedIps)
                                                    .del()
                                                    .where('Location_Id', locationId)
                                                    .transacting(trx)
                                                    .then(async (delSubTableRecord) =>{
                                                        // get location name to update the system log activities
                                                        let locationName = await commonFunction.getLocation(locationId, organizationDbConnection);
                                                        // form inputs to update system log activities
                                                        systemLogParams = {
                                                            action: constants.systemLogs.roleDelete,
                                                            userIp: context.User_Ip,
                                                            employeeId: employeeId,
                                                            formName: constants.formName.ipWhitelist,
                                                            trackingColumn: locationName,
                                                            organizationDbConnection: organizationDbConnection,
                                                            uniqueId: locationId
                                                        }
                                                        // check if this record is final record or not
                                                        if (ipConfigCount[0] && ipConfigCount[0].recordCount === 1){
                                                            // disable the flag in org_details table. if it is final record
                                                            return (
                                                                organizationDbConnection(ehrTables.orgDetails)
                                                                .update({
                                                                    IP_Address_Restriction: 0
                                                                })
                                                                .where('Org_Code', context.Org_Code)
                                                                .transacting(trx)
                                                                .then(async (updateFlag) => {
                                                                    // call function createSystemLogActivities() to update system log activities
                                                                    await commonLib.func.createSystemLogActivities(systemLogParams);
                                                                    // return success response to UI
                                                                    return { errorCode: '', message: 'IP Address configuration deleted successfully' }
                                                                })
                                                            )
                                                        }else{
                                                            // call function createSystemLogActivities() to update system log activities
                                                            await commonLib.func.createSystemLogActivities(systemLogParams);
                                                            // return success response to UI
                                                            return { errorCode: '', message: 'IP Address configuration deleted successfully' }
                                                        }
                                                    })
                                                )
                                            }
                                        })
                                        //return the success result to user
                                        .then(function (result) {
                                            return result;
                                        })
                                        //catch db-connectivity errors
                                        .catch(function (deleteInsideCatch) {
                                            console.log('Error in deletewhitelistedIPAddress() function .catch() block', deleteInsideCatch);
                                            if (deleteInsideCatch === '_EC0001'){
                                                errResult = commonLib.func.getError('', '_EC0001');
                                            }else{
                                                errResult = commonLib.func.getError(deleteInsideCatch, 'SE0103');
                                            }
                                            // return response to UI
                                            throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message}));
                                        })
                                        /**close db connection */
                                        .finally(() => {
                                            organizationDbConnection.destroy();
                                        })
                                    )
                                })
                            )
                        })
                    )
                } else if (checkRights === false) {
                    // throw error if delete rights is not exists
                    throw ('_DB0103');
                } else {
                    // throw error
                    throw (checkRights);
                }
            } catch (deleteMainCatch){
                console.log('Error in deletewhitelistedIPAddress() function main catch block',deleteMainCatch);
                // access denied error
                if (deleteMainCatch === '_DB0103') {
                    errResult = commonLib.func.getError('', '_DB0103');
                } else if (deleteMainCatch instanceof Object) { // is to check error object is json or not
                    errResult = deleteMainCatch;
                } else {
                    errResult = commonLib.func.getError('', 'SE0003');
                }
                // destroy database connection if exists
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                // return response to UI
                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message }));
            }
        }
    }
};
exports.resolvers = resolvers;