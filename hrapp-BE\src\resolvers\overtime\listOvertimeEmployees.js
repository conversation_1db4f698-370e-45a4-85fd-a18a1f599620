module.exports.listOvertimeEmployees = async (parent, args, context, info) =>{
    console.log('Inside listOvertimeEmployees() function');
    const { ApolloError } = require('apollo-server-lambda');
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // Organization database connection
    const knex = require('knex');
    // require common constant files
    const { formName } = require('../../../common/appconstants');
    // get db connection
    const orgDb = knex(context.connection.OrganizationDb);
    try{
        let isAdmin = 0;
        // get login employee Id
        let loginEmpId = context.Employee_Id;
        // Check admin access rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(orgDb, loginEmpId, formName.overtime, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Employee_Role.toLowerCase() === 'admin'){
            isAdmin = 1;
        }
        // check logged-in employee is employee admin or not
        let checkEmpAdminRights = await commonLib.func.checkEmployeeAccessRights(orgDb, loginEmpId, formName.employeeAdmin, '', 'UI');
        // check employee admin rights
        if (Object.keys(checkEmpAdminRights).length > 0 && checkEmpAdminRights.Role_Update === 1) {
            isAdmin = 1;
        }
        args.employeeId = loginEmpId;
        args.isAdmin = isAdmin;
        // call common function to get the roster employee details
        let overtimeEmployees = await commonLib.func.getAllEmployeeDetails(orgDb, 'getOvertimeEmployees', context.Org_Code,
            process.env.domainName, process.env.region, process.env.hrappProfileBucket,args, null);
        // Iterate the overtimeEmployees and get exit date if exists
        for(let employees of overtimeEmployees){
            // check any(applied/approved/incomplete) resignation record exit for the roster employee
            employees.exitDate = await commonLib.payroll.getEmployeeResignationDate(orgDb,employees.employee_id);
        }
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
        // return response back to UI
        return { errorCode: '', message: 'Overtime employee details retrieved successfully', employeeDetails: overtimeEmployees}
    } catch (listOvertimeEmployeesMainCatchError){
        console.log('Error in listOvertimeEmployees() function main catch block', listOvertimeEmployeesMainCatchError);
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
        let errResult = commonLib.func.getError(listOvertimeEmployeesMainCatchError, 'OT0004');
        throw new ApolloError(errResult.message, errResult.code)
    }
};