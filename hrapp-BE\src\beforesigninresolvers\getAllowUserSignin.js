// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
// import validation error
const commonValidation = require('../../common/commonvalidation');
// requires signin common function
const commonFunction = require('./signInCommonFunction');

// variable declarations
let errResult = {};
let organizationDbConnection = '';

// resolver definition
const resolvers = {
    Query: {
        getAllowUserSignin: async (parent, args, context, info) => {
            console.log('Inside getAllowUserSignin.js function');
            try{
                // variable declarations
                let emailId = args.emailId;
                // check emailId validation
                if (emailId && (commonValidation.emailValidation(emailId) === true)){
                    // Create organizationDbConnection
                    organizationDbConnection = knex(context.connection.OrganizationDb);
                    // get allow user sign in flag
                    let employeeDetails = await commonFunction.checkAllowUserSignin(emailId,organizationDbConnection);
                    // check the response
                    if(employeeDetails.length > 0){
                        employeeDetails = employeeDetails[0];
                        // check whether the employee is active or not
                        if (employeeDetails.Emp_Status !== 'Active' || employeeDetails.Member_Status !== 'Active'){
                            // throw error
                            throw ('SIB0103');
                        } else if (employeeDetails.Allow_User_Signin === 0){
                            // throw error
                            throw ('SIB0104');
                        } else if (employeeDetails.Enable_Sign_In_With_Mobile_No === 1){
                            // throw error
                            throw ('SIB0105');
                        } else{
                            // destroy database connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            // return response to UI
                            return { errorCode:'',message:'Allow user signin flag retrieved successfully',allowUserSignin:true}
                        }
                    }else{
                        // throw error
                        throw ('SIB0102');
                    }
                }else{
                    // throw error
                    throw ('SIB0101');
                }
            }catch(getAllowUserSigninMainCatchError){
                console.log('Error in getAllowUserSigninMainCatchError() function main catch block',getAllowUserSigninMainCatchError);
                if (getAllowUserSigninMainCatchError === 'SIB0101' || getAllowUserSigninMainCatchError === 'SIB0102'
                || getAllowUserSigninMainCatchError === 'SIB0103' || getAllowUserSigninMainCatchError === 'SIB0104' || getAllowUserSigninMainCatchError === 'SIB0105'){
                    errResult = commonLib.func.getError(getAllowUserSigninMainCatchError, '');
                }else{
                    errResult = commonLib.func.getError(getAllowUserSigninMainCatchError, 'SIB0106');
                }
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, allowUserSignin: '' }));
            }
        }
    }
};
exports.resolvers = resolvers;