// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError,UserInputError } = require('apollo-server-lambda');
// require file to access constant values
const { formName,systemLogs } = require('../../../common/appconstants');
// require table alias function
const { ehrTables } = require('../../../common/tablealias');
// require validation file
const benefitsInputValidation=require('../../../common/benefitsInputValidation');
const moment = require('moment-timezone')
// variable declarations
let organizationDbConnection='';
let errResult={};

// resolver definition
const resolvers = {
    Mutation:{
        // function to update the employee allocate shares details
        updateEmployeeAllocatedShares: async (parent, args, context, info) => {
            let validationError={};
            try{
                console.log('Inside updateEmployeeAllocatedShares function');
                // variable declarations
                let logInEmpId=context.Employee_Id;
                let ipAddress=context.User_Ip;
                let updateParams={};
                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                // check whether employee have edit access for ESOP form
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.esop, '', 'UI');  
                if(Object.keys(checkRights).length>0 && (checkRights.Role_Update === 1)) {
                    // Check benefits admin form - edit access rights exist for employee or not
                    let checkBenefitsAdminRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.benefitsAdmin, '', 'UI');
                    if(Object.keys(checkBenefitsAdminRights).length>0 && checkBenefitsAdminRights.Role_Update === 1) {
                        // validate the input fields
                        validationError=await benefitsInputValidation.editFormInputValidation(args);
                        // Check validation error exist or not
                        if (Object.keys(validationError).length === 0) {
                            return (
                                organizationDbConnection
                                .transaction(function (trx) {
                                    // Get the employee share details based on input allocated shareId
                                    return(
                                        organizationDbConnection(ehrTables.employeeShareDetails)
                                        .select('Vested_Status','Employee_Id')
                                        .where('Employee_Allocated_Share_Id',args.allocatedShareId)
                                        .transacting(trx)
                                        .then(async (shareDetails) =>{
                                            //Allocated Date Validation
                                            let employeeIds = shareDetails.map((el)=>el.Employee_Id)
                                            validationError = await validateAllocatedDate(organizationDbConnection, employeeIds, args.allocatedDate)
                                            if (Object.keys(validationError).length) {
                                                throw 'IVE0000'
                                            }
                                            // check whether the allocated shares already vested or not
                                            if((shareDetails[0].Vested_Status)==='Vest Now'){
                                                // get the total share value from the share vest history table
                                                return(
                                                    organizationDbConnection(ehrTables.employeeShareVestHistory)
                                                    .select(organizationDbConnection.raw('SUM(Vested_Share) as totalVestedShares'))
                                                    .where('Employee_Allocated_Share_Id',args.allocatedShareId)
                                                    .transacting(trx)
                                                    .then(async (vestShareDetails) =>{
                                                        // throw error if input allocated share is less than the total vested shares
                                                        if((vestShareDetails.length>0) && (vestShareDetails[0].totalVestedShares>0) && (args.allocatedShare<vestShareDetails[0].totalVestedShares) ){
                                                            throw 'BES0110';
                                                        }
                                                        // if the input allocated shares is equal to the vested shares then update the status as 'All vested'
                                                        else if(args.allocatedShare===vestShareDetails[0].totalVestedShares){
                                                            console.log('Existing employee share is same as input allocated shares. So update the vested status as All Vested');
                                                            updateParams.Vested_Status='All Vested';
                                                        }
                                                        // form update params
                                                        updateParams.Allocated_Shares=args.allocatedShare;
                                                        updateParams. Allocated_Date=args.allocatedDate;
                                                        updateParams.Updated_On= await commonLib.func.getEmployeeTimeZone(logInEmpId, organizationDbConnection,1);
                                                        updateParams.Updated_By=logInEmpId;
                                                        // update the employee share details
                                                        return(
                                                            organizationDbConnection(ehrTables.employeeShareDetails)
                                                            .update(updateParams)
                                                            .where('Employee_Allocated_Share_Id',args.allocatedShareId)
                                                            .transacting(trx)
                                                            .then(async () =>{
                                                                let systemLogParams = {
                                                                    action: systemLogs.roleUpdate,
                                                                    userIp: ipAddress,
                                                                    employeeId: logInEmpId,
                                                                    formName: formName.esop,
                                                                    trackingColumn: '- Updated the shares details for the share Id ',
                                                                    organizationDbConnection: organizationDbConnection,
                                                                    uniqueId: args.allocatedShareId
                                                                }
                                                                // call function createSystemLogActivities() to update system log activities
                                                                await commonLib.func.createSystemLogActivities(systemLogParams);
                                                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                                return { errorCode:'', message:'Employee allocated shares updated successfully.'};
                                                            })
                                                        );
                                                    })
                                                );
                                            }
                                            else{
                                                throw 'BES0109';
                                            }
                                        })
                                    )
                                })
                                .catch(function (catchError) {
                                    console.log('Error in updateEmployeeAllocatedShares function .catch block',catchError);
                                    if(catchError==='IVE0000'){
                                        errResult = commonLib.func.getError('', 'IVE0000');
                                        throw new UserInputError(errResult.message, { validationError: validationError });
                                    }else{
                                        errResult = commonLib.func.getError(catchError, 'BES0108');
                                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                                        throw new ApolloError(errResult.message,errResult.code);
                                    }
                                })
                            );
                        }
                        else{
                            throw 'IVE0000';
                        }
                    }
                    else{
                        throw '_DB0109';
                    }
                }
                else if (checkRights === false) {
                    throw '_DB0102';
                } else {
                    throw (checkRights);
                }
            }
            catch(mainCatchError)
            {
                console.log('Error in updateEmployeeAllocatedShares function main catch block',mainCatchError);
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                if(mainCatchError==='IVE0000'){
                    errResult = commonLib.func.getError('', 'IVE0000');
                    throw new UserInputError(errResult.message, { validationError: validationError });
                }
                else{
                    errResult = commonLib.func.getError(mainCatchError, 'BES0009');
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        }
    }
};
    
exports.resolvers = resolvers;

async function validateAllocatedDate(organizationDbConnection, employeeIds, allocatedDate){
    try{
        let validationError = {}
        let maxDateOfJoin = await organizationDbConnection(ehrTables.empJob)
        .select(organizationDbConnection.raw('MAX(Date_Of_Join) as maxDateOfJoin'))
        .whereIn('Employee_Id', employeeIds)

        maxDateOfJoin = maxDateOfJoin[0].maxDateOfJoin;

        let resignationDates = await commonLib.payroll.getEmployeeResignationDate(organizationDbConnection, employeeIds);
        if(resignationDates && resignationDates.length){
            resignationDates = resignationDates.map((el) => moment(el.Resignation_Date));
            let minResignationDate = moment.min(resignationDates).format('YYYY-MM-DD');
    
            if(!moment(allocatedDate, 'YYYY-MM-DD').isSameOrBefore(minResignationDate)){
                validationError['IVE0418'] = commonLib.func.getError('', 'IVE0418').message;
            }
        }

        if(!moment(allocatedDate, 'YYYY-MM-DD').isSameOrAfter(maxDateOfJoin)){
            validationError['IVE0416'] = commonLib.func.getError('', 'IVE0416').message;
        }

        return validationError;

    }
    catch(err){
        console.log('Error in validateAllocatedDate function', err);
        throw err
    }
}
