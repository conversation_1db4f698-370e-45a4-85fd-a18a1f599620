// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require file to access constant values
const { formName } = require('../../common/appconstants');
// require validation file
const commonValidation = require('../../common/commonvalidation');
// require table alias
const { ehrTables } = require('../../common/tablealias');

// resolver definition
const resolvers = {
    Query:{
        // function to get the service provider details
        viewMoreServiceProviderDetails: async (parent, args, context, info) => {
            // variable declarations
            let organizationDb='';
            let validationError={};
            try{
                console.log('Inside viewMoreServiceProviderDetails function');
                // variable declarations
                let loggedInEmpId=context.Employee_Id;
                let outputResponse={};
                // get the organization database connection
                organizationDb = knex(context.connection.OrganizationDb);
                // check general form access rights
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDb, loggedInEmpId, formName.general,'','UI');
                if (Object.keys(checkRights).length === 0) {
                    throw '_DB0100'; // throw error if employee does not have access
                }
                else if(checkRights.Employee_Role.toLowerCase() !== 'admin'){
                    throw '_DB0109';
                }
                else {
                    // check whether view access to general form
                    if(checkRights.Role_View === 1)
                    {
                        // validate service providerId
                        if(args.serviceProviderId){
                            if (!commonValidation.numberValidation(args.serviceProviderId)) {
                                validationError['IVE0182'] = commonLib.func.getError('', 'IVE0182').message;
                            }
                        }
                        else{
                            validationError['IVE0182'] = commonLib.func.getError('', 'IVE0182').message;
                        }
                        if(Object.keys(validationError).length === 0){
                            return(
                                // Based on input service providerId get the details
                                organizationDb
                                .select('Added_On','Added_By','Updated_On','Updated_By')
                                .from(ehrTables.serviceProvider)
                                .where('Service_Provider_Id',args.serviceProviderId)
                                .then(async(getData) => {
                                    // check whether record exist or not
                                    if(getData.length>0){
                                        if(getData[0].Added_By)
                                        {
                                            // get the user details based on the addedBy employeeId
                                            let addedOnUserData=await commonLib.func.getUserDetailsBasedOnId(organizationDb,getData[0].Added_By);
                                            if(Object.keys(addedOnUserData).length > 0){
                                                outputResponse.addedByEmployeeId=addedOnUserData.userDefinedEmpId;
                                                outputResponse.addedOn=getData[0].Added_On;
                                                outputResponse.addedByUserName=addedOnUserData.employeeName;
                                                if(getData[0].Updated_By){
                                                    outputResponse.updatedOn=getData[0].Updated_On;
                                                    /** check whether addedBy employeeId is same as updatedBy employeeId. 
                                                     * If employeeId same then no need to get the user details else call function to get details*/
                                                    if(getData[0].Added_By !== getData[0].Updated_By){
                                                        // get the user details based on the updatedBy employeeId
                                                        let updatedUserData=await commonLib.func.getUserDetailsBasedOnId(organizationDb,getData[0].Updated_By);
                                                        if(Object.keys(updatedUserData).length > 0){
                                                            outputResponse.updatedByUserName =updatedUserData.employeeName;
                                                            outputResponse.updatedByEmployeeId =updatedUserData.userDefinedEmpId;
                                                        }
                                                    }
                                                    else{
                                                        outputResponse.updatedByUserName=addedOnUserData.employeeName;
                                                        outputResponse.updatedByEmployeeId=addedOnUserData.userDefinedEmpId;
                                                    }
                                                }
                                            }
                                            else{
                                                throw 'DB0016';
                                            }
                                        }
                                        organizationDb ? organizationDb.destroy() : null;
                                        return { errorCode:'',message:'Service provider details retrieved successfully.',providerDetails:outputResponse};
                                    }
                                    else{
                                        throw '_EC0001';
                                    }
                                })
                                .catch(error=>{
                                    console.log('Error in viewMoreServiceProviderDetails function .catch block',error);
                                    organizationDb ? organizationDb.destroy() : null;
                                    let errResult = commonLib.func.getError(error, 'SGE0109');
                                    throw new ApolloError(errResult.message,errResult.code);
                                })
                            );
                        }
                        else{
                            throw 'IVE0000';
                        }
                    }
                    else{
                        throw '_DB0100';
                    }
                }
            }catch(mainCatchError)
            {
                console.log('Error in viewMoreServiceProviderDetails function main catch block',mainCatchError);
                // destroy database connection
                organizationDb ? organizationDb.destroy() : null;
                if (mainCatchError === 'IVE0000') {
                    console.log('Validation error in viewMoreServiceProviderDetails function - ',validationError);
                    let errResult = commonLib.func.getError(mainCatchError, 'IVE0000');
                    throw new UserInputError(errResult.message, { validationError: validationError });
                }
                else{
                    let errResult = commonLib.func.getError(mainCatchError, 'SGE0005');
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        }
    }
};

exports.resolvers = resolvers;
