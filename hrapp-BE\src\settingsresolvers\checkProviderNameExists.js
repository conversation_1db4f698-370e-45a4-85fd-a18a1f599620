// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require file to access constant values
const { formName } = require('../../common/appconstants');
// require validation file
const commonValidation = require('../../common/commonvalidation');
// require common function
const { validateServiceProviderNameExist } =require('../../common/commonFunction');

// resolver definition
const resolvers = {
    Query:{
        // function to check service provider name already exists
        checkProviderNameExists: async (parent, args, context, info) => {
            // variable declarations
            let organizationDb='';
            let validationError={};
            try{
                console.log('Inside checkProviderNameExists function',args);
                // variable declarations
                let loggedInEmpId=context.Employee_Id;
                // get the organization database connection
                organizationDb = knex(context.connection.OrganizationDb);
                // check general form access rights
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDb, loggedInEmpId, formName.general,'','UI');
                if (Object.keys(checkRights).length === 0) {
                    throw '_DB0100'; // throw error if employee does not have access
                }
                else if(checkRights.Employee_Role.toLowerCase() !== 'admin'){
                    throw '_DB0109';
                }
                else {
                    if(checkRights.Role_View === 1)
                    {
                        if(args.serviceProviderName){
                            let lengthValidate = commonValidation.checkLength(args.serviceProviderName,1,50);
                            let nameValidate=commonValidation.commentValidation(args.serviceProviderName)
                            if (!lengthValidate || !nameValidate) {
                                validationError['IVE0183'] = commonLib.func.getError('', 'IVE0183').message;
                            }
                        }
                        else{
                            validationError['IVE0183'] = commonLib.func.getError('', 'IVE0183').message;
                        }
                        if(Object.keys(validationError).length === 0){
                            // function to validate service provider name existence
                            await validateServiceProviderNameExist(organizationDb,args.serviceProviderName);
                            organizationDb ? organizationDb.destroy() : null;
                            return { errorCode:'',message:'Service provider name does not exist.'};
                        }
                        else{
                            throw 'IVE0000';
                        }
                    }
                    else{
                        throw '_DB0100';
                    }
                }
            }
            catch(mainCatchError)
            {
                console.log('Error in checkProviderNameExists function main catch block',mainCatchError);
                // destroy database connection
                organizationDb ? organizationDb.destroy() : null;
                if (mainCatchError === 'IVE0000') {
                    console.log('Validation error in checkProviderNameExists function - ',validationError);
                    let errResult = commonLib.func.getError(mainCatchError, 'IVE0000');
                    throw new UserInputError(errResult.message, { validationError: validationError });
                }
                else{
                    let errResult = commonLib.func.getError(mainCatchError, 'SGE0004');
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        }
    }
};

exports.resolvers = resolvers;
