// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');

// variable declaration
let organizationDb='';
let workPlaceDetails=[];

// resolver definition
const resolvers = {
    Query: {
        // function to check geo enforce and work place details
        checkGeoEnforceAndWorkPlace: async (parent, args, context, info) => {
            try{   
                // make database connection
                organizationDb = knex(context.connection.OrganizationDb);

                // function to check whether geo enforced for that employee or not
                let geoEnabled = await commonLib.employees.checkGeoEnforce(context.Employee_Id, organizationDb);

                // Check whether success response returned or not
                if(geoEnabled!=='')
                {
                    // function to check whether work place enabled or not
                    let isWorkPlaceEnabled = await commonLib.employees.isEnableWorkPlace(context.Employee_Id, organizationDb);

                    // Check whether success response returned or not
                    if(isWorkPlaceEnabled!=='')
                    {
                        // If work place enabled get the work place details
                        if(parseInt(isWorkPlaceEnabled)===1)
                        {
                            // function to get work place details
                            // since work place is enabled if error in fetching work place details throw error
                            workPlaceDetails = await commonLib.employees.listWorkPlace(organizationDb);
                            if(workPlaceDetails.length<0) throw 'DB0007';
                        }
                        // return success response
                        return { errorCode: '', message: 'Geo enforced and work place details retrieved successfully.',
                        isGeoEnable : (parseInt(geoEnabled)===1)?true:false, isWorkPlaceEnable:isWorkPlaceEnabled,
                        workPlaceDetails: workPlaceDetails };
                    }
                    else{
                        throw 'DB0006';
                    }
                }
                else{
                    throw 'DB0005';
                }
            }
            catch(mainCatchError)
            {
                console.log("Error in checkGeoEnforceAndWorkPlace function catch bloack",mainCatchError);
                // destroy database connection
                organizationDb ? organizationDb.destroy() : null;
                // get the code and message from common function based on returned error code
                let errResult = commonLib.func.getError(mainCatchError, 'DB0004');
                // return response
                throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message, isGeoEnable : null , isWorkPlaceEnabledEnable: null , workPlaceDetails:''}));                
            }
        }
    }
};

exports.resolvers = resolvers;
