// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require apollor server errors
const { ApolloError, UserInputError } = require('apollo-server-lambda');
// require common constant files
const constants = require('../../common/appconstants');
// Organization database connection
const knex = require('knex');
// require table alias
const tables = require('../../common/tablealias');
// require validation file
const { validateUpdateITDeclarationSettingsInputs } = require('./settingsFormInputValidations/incomeTaxDeclarationSettingsValidation');

// variable declarations
let ehrTables = tables.ehrTables;

// resolver definition
const resolvers = {
    Query: {
        // retrieve the income tax declaration settings
        retrieveIncomeTaxDeclarationSettings: async (parent, args, context, info) => {
            console.log("Inside retrieveIncomeTaxDeclarationSettings() function");

            let errResult ={};
            let organizationDbConnection = '';
            let checkRights = false;
            let fiscalStartDate = fiscalEndDate ='';

            try{
                // get db connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                /** For the login employee id, check view rights exists for the tax configuration form to retrieve IT Declaration settings. Because for all the sub tabs
                 *  which are under the tax settings, we should use tax configuration form access */
                checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, context.Employee_Id, constants.formName.tax, constants.roles.roleView);

                //check view rights exist
                if (checkRights === true) {
                    // get Income tax declaration settings details
                    return(
                        organizationDbConnection(ehrTables.incomeTaxDeclarationSettings)                        
                        .select('Lock_Date','New_Joiners_Grace_Days','Notify_IT_Declaration_Release','Notify_IT_Declaration_Lock','Notify_IT_Declaration_Submission_Before_Lock_Date','Remind_Five_Days_Before','Remind_One_Day_Before','Email_Subject','Email_Content')
                        .then(async(incomeTaxDeclarationSettingsResponse) =>{
                            // check if income tax declaration settings exists
                            if(incomeTaxDeclarationSettingsResponse && incomeTaxDeclarationSettingsResponse[0]) {
                                // Get the fiscal details
                                let fiscalDetails = await commonLib.payroll.getFiscalDetails(context.Org_Code,organizationDbConnection);

                                console.log("FiscalDetails response in the retrieveIncomeTaxDeclarationSettings() function:",fiscalDetails);
                                // If the financial start date and end date exist
                                if(fiscalDetails && Object.keys(fiscalDetails).length > 0 && fiscalDetails.fiscalStartDate && fiscalDetails.fiscalEndDate){
                                    fiscalStartDate = fiscalDetails.fiscalStartDate;
                                    fiscalEndDate = fiscalDetails.fiscalEndDate;

                                    return { errorCode: '', message: 'Income tax declaration settings retrieved successfully', incomeTaxDeclarationSettings: incomeTaxDeclarationSettingsResponse[0], fiscalStartDate: fiscalStartDate, fiscalEndDate: fiscalEndDate };
                                }else{
                                    // If income tax declaration settings not exists throw no record found
                                    throw('SE0014');
                                }
                            }
                            else {
                                // If income tax declaration settings not exists throw no record found
                                throw('_EC0001');
                            }
                        }).catch(function (retrieveITDeclarationSettingsError){
                            console.log('Error in retrieveIncomeTaxDeclarationSettings() function .catch block',retrieveITDeclarationSettingsError);
                            errResult = commonLib.func.getError(retrieveITDeclarationSettingsError, 'SE0111');
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                        // close db connection
                        .finally(() => {
                            organizationDbConnection.destroy();
                        })
                    )
                }else if (checkRights === false) {
                    // throw error if view rights is not exists
                    throw('_DB0100');
                } else {
                    // throw error
                    throw(checkRights);
                }
            }catch(retrieveITDecSettingsMainCatchError){
                console.log('Error in retrieveIncomeTaxDeclarationSettings() function main catch block',retrieveITDecSettingsMainCatchError);

                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;

                // get error result message and code
                let errResult = commonLib.func.getError(retrieveITDecSettingsMainCatchError, 'SE0012');
                throw new ApolloError(errResult.message, errResult.code);
            }
        }
    },
    Mutation : {
        // update the income tax declaration settings
        updateIncomeTaxDeclarationSettings: async (parent, args, context, info) => {
            console.log("Inside updateIncomeTaxDeclarationSettings() function");

            let errResult ={};
            let organizationDbConnection = '';
            let checkRights = false;
            let systemLogParams = {};
            let validationError = {};

            try{
                // get db connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                
                // get login employeeId from context object(Taken from token itself)
                let loginEmployeeId = context.Employee_Id; 

                /** For the login employee id, check update rights exists for the tax configuration form to update IT Declaration settings. Because for all the sub tabs
                 *  which are under the tax settings, we should use tax configuration form access */
                checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, constants.formName.tax, constants.roles.roleUpdate);

                //If update access exists
                if (checkRights === true) {
                    // validate inputs
                    validationError = await validateUpdateITDeclarationSettingsInputs(args,context.Org_Code,organizationDbConnection);
                    
                    // check validation error exists or not
                    if (Object.keys(validationError).length === 0){
                        let updateITDeclarationSettingsDetails = {};
                        updateITDeclarationSettingsDetails.Lock_Date = args.lockDate;
                        updateITDeclarationSettingsDetails.New_Joiners_Grace_Days = args.newJoinersGraceDays;
                        updateITDeclarationSettingsDetails.Notify_IT_Declaration_Release = args.notifyITDeclarationRelease;
                        updateITDeclarationSettingsDetails.Notify_IT_Declaration_Lock = args.notifyITDeclarationLock;
                        updateITDeclarationSettingsDetails.Notify_IT_Declaration_Submission_Before_Lock_Date = args.notifyITDeclarationSubmissionBeforeLockDate;

                        /** If the 'email reminder for IT Declaration before the lock date' flag is enabled */
                        if(args.notifyITDeclarationSubmissionBeforeLockDate === 'Yes'){
                            updateITDeclarationSettingsDetails.Remind_Five_Days_Before = (args.remindFiveDaysBefore) ? (args.remindFiveDaysBefore) : 'No';
                            updateITDeclarationSettingsDetails.Remind_One_Day_Before = (args.remindOneDayBefore) ? (args.remindOneDayBefore) : 'No';
                            updateITDeclarationSettingsDetails.Email_Subject = (args.emailSubject) ? (args.emailSubject) : null;
                            updateITDeclarationSettingsDetails.Email_Content = (args.emailContent) ? args.emailContent : null;
                        }else{
                            /** If the 'email reminder for IT Declaration before the lock date' flag is not enabled then set the default values */
                            updateITDeclarationSettingsDetails.Remind_Five_Days_Before = 'No';
                            updateITDeclarationSettingsDetails.Remind_One_Day_Before = 'No';
                            updateITDeclarationSettingsDetails.Email_Subject = null;
                            updateITDeclarationSettingsDetails.Email_Content = null;
                        }

                        return(
                            organizationDbConnection
                            .transaction(function(trx){                            
                                // retrieve Income tax declaration settings
                                return (
                                    organizationDbConnection(ehrTables.incomeTaxDeclarationSettings)
                                    .transacting(trx)
                                    .then(incomeTaxDeclarationSettingsRes =>{
                                        console.log("Get the incomeTaxDeclarationSettings in the updateIncomeTaxDeclarationSettings() function", incomeTaxDeclarationSettingsRes);

                                        /** If the income tax declaration settings exist, update the settings */
                                        if(incomeTaxDeclarationSettingsRes && incomeTaxDeclarationSettingsRes[0]) {
                                            return(
                                            organizationDbConnection(ehrTables.incomeTaxDeclarationSettings)
                                            .update(updateITDeclarationSettingsDetails)
                                            .transacting(trx)
                                            .then(async (incomeTaxDeclarationSettingsUpdateRes) =>{
                                                // form inputs to update system log activities commonly. Example log message: Update Tax Configuration - IT Declaration settings
                                                systemLogParams = {
                                                    action: constants.systemLogs.roleUpdate,
                                                    userIp: context.User_Ip,
                                                    employeeId: loginEmployeeId,
                                                    formName: constants.formName.tax,
                                                    trackingColumn: 'IT Declaration Settings',
                                                    organizationDbConnection: organizationDbConnection
                                                }

                                                // call function createSystemLogActivities() to update system log activities.
                                                await commonLib.func.createSystemLogActivities(systemLogParams);

                                                return incomeTaxDeclarationSettingsUpdateRes;
                                            })
                                            )                                 
                                        }
                                        else {
                                            // throw error if income tax declaration settings does not exists
                                            // Need to discuss and insert if the settings not exist
                                            throw('_EC0001');
                                        }
                                    })   
                                )                             
                            })
                            //return the success result to userawait
                            .then(function (result) {
                                // return success response to UI
                                return { errorCode: '', message: 'Income tax declaration settings updated successfully', validationError: '' };
                            })
                            /** catch db-connectivity errors */
                            .catch(updateITDeclarationSettingsCatchError => {
                                console.log('Error while updating the records in updateIncomeTaxDeclarationSettings() function',updateITDeclarationSettingsCatchError);

                                // get error and return it to UI
                                errResult = commonLib.func.getError(updateITDeclarationSettingsCatchError, 'SE0112');
                                throw new ApolloError(errResult.message, errResult.code);
                            })
                            /** close db connection */
                            .finally(() => {
                                organizationDbConnection.destroy();
                            })
                        )
                    } else {
                        // throw validation error
                        throw('IVE0000'); 
                    }
                }  else {
                    // throw error if edit rights is not exists
                    throw('_DB0102');
                }
            } catch (updateIncomeTaxDeclarationSettingsMainCatchError) {
                console.log("Error in updateIncomeTaxDeclarationSettings() function main catch block", updateIncomeTaxDeclarationSettingsMainCatchError);

                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;

                // check input validation error or not
                if (updateIncomeTaxDeclarationSettingsMainCatchError === 'IVE0000') {
                    console.log("Validation error in updateIncomeTaxDeclarationSettings() function".validationError);

                    errResult = commonLib.func.getError('', updateIncomeTaxDeclarationSettingsMainCatchError);
                    // return response
                    throw new UserInputError(errResult.message, { validationError: validationError });
                } else {
                    // get error and return it to UI
                    errResult = commonLib.func.getError(updateIncomeTaxDeclarationSettingsMainCatchError, 'SE0013');
                    throw new ApolloError(errResult.message, errResult.code);
                }
            }
        }
    }
};
exports.resolvers = resolvers;