//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make database connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
//Require form Ids
const { formIds } = require('../../../common/appconstants');

//List the probation employees in the team dashboard
let organizationDbConnection;
module.exports.checkAttendanceConfiguration = async (parent, args, context, info) => {
    try {
        console.log("Inside checkAttendanceConfiguration function.");
        let employeeId
        if (args.employeeId) {
            employeeId = args.employeeId
        } else {
            employeeId = context.Employee_Id;
        }
        organizationDbConnection = knex(context.connection.OrganizationDb);
        if (employeeId) {
            return (
                organizationDbConnection(ehrTables.cusEmpGroupEmp)
                    .select("AGFC.Attendance_Type_Id as attendanceTypeId", "AGFC.Attendance_Configuration_Name as attendanceConfigurationName", "AGFC.Geo_Fencing_Enabled as geoFencingEnabled", "AGFC.Facial_Recognition_Enabled as facialRecognitionEnabled", "AGFC.Enable_Liveness_Detection as enableLivenessDetection", "AGFC.No_Of_Challenges as noOfChallenges", "AGFC.Force_Fencing as forceFencing", "AGFC.Force_Faical_Recognition as forceFaicalRecognition", "AGFC.Center_Point as centerPoint", "AGFC.Radius as radius")
                    .from(ehrTables.cusEmpGroupEmp + " as CEGE")
                    .innerJoin(ehrTables.customGroupAssociated + " as CGA", "CGA.Custom_Group_Id", "CEGE.Group_Id")
                    .innerJoin(ehrTables.attendanceGeoFacialConfiguration + " as AGFC", "AGFC.Attendance_Type_Id", "CGA.Parent_Id")
                    .where("CEGE.Employee_Id", employeeId)
                    .whereIn('CEGE.Type', ['Default', 'AdditionalInclusion'])
                    .andWhere("CGA.Form_Id", formIds.geoFencingSelfieAttendance)
                    .then(data => {
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Attendance configuration retrieved successfully.", attendanceConfigurationDetails: data };
                    })
                    .catch(e => {
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        console.log('Error in checkAttendanceConfiguration function .catch block.', e);
                        let errResult = commonLib.func.getError(e, 'EM00112');
                        // throw error response to UI
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        }
        else {
            throw ("SIB0131");
        }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in checkAttendanceConfiguration function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'EM00112');
        // throw error response to UI
        throw new ApolloError(errResult.message, errResult.code);
    }
}