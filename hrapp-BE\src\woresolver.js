//Require one ro resolver file as query is required to be defined in woschema.graphql
const listProbationEmployees = require('./roresolvers/dashboard/listProbationEmployees');

//Require wo resolver files
const updateProbationAndConfirmation = require('./woresolvers/dashboard/updateProbationAndConfirmation');
const updateGoalStatus = require('./woresolvers/performanceManagementSystem/updateGoalStatus');
const deleteEmployeeGoalsAndAchievement = require('./woresolvers/performanceManagementSystem/deleteEmployeeGoalsAndAchievement');
const revertGoalsOrRatings = require('./woresolvers/performanceManagementSystem/revertGoalsOrRatings');
const generateReportBasedOnInputs = require('./woresolvers/reportGenerator/generateReportBasedOnInputs');
const addUpdateShiftRotation = require('./woresolvers/shiftRotation/addUpdateShiftRotation');
const deleteShiftRotation = require('./woresolvers/shiftRotation/deleteShiftRotation');
const addUpdateEmployeeShiftSwap=require('./woresolvers/shiftSwap/addUpdateEmployeeShiftSwap');
const evaluateAndProcessShiftSwapStatus=require('./woresolvers/shiftSwap/evaluateAndProcessShiftSwapStatus');
const rejectShiftSwap=require('./woresolvers/shiftSwap/rejectShiftSwap');
const approveMathchingSwapRequest=require('./woresolvers/shiftSwap/approveMathchingSwapRequest');

//Define resolver
const resolvers = {
    Query: Object.assign({},
        listProbationEmployees
    ),
    Mutation: Object.assign({},
        updateProbationAndConfirmation,
        updateGoalStatus,
        deleteEmployeeGoalsAndAchievement,
        revertGoalsOrRatings,
        generateReportBasedOnInputs,
        addUpdateShiftRotation,
        deleteShiftRotation,
        addUpdateEmployeeShiftSwap,
        evaluateAndProcessShiftSwapStatus,
        rejectShiftSwap,
        approveMathchingSwapRequest
    )
}

exports.resolvers = resolvers;