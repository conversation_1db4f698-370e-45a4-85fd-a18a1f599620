
module.exports.bulkImport = async (parent, args, context, info) => {
        
    let orgDb;
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    try {
        args = args.data;

        let { updateShiftScheduling } = require("./updateShiftScheduling");

        // require common constant files
        const { formName, systemLogs } = require('../../../common/appconstants');

        // Organization database connection
        const knex = require('knex');

        orgDb = knex(context.connection.OrganizationDb);

        let errorMessage = {};
        let length = args.length;
    
        for(let i = 0; i < length; ++i) {
            args[i].source = 'bulk import';
            let res = await updateShiftScheduling( "", args[i], context, "" )
    
            if(res.errorCode) {
                errorMessage[i] = res.message;
            }
        }

        // form inputs to update system log activities commonly
        let systemLogParams = {
            action: '',
            userIp: context.User_Ip,
            employeeId: context.Employee_Id,
            formName: '',
            trackingColumn: '',
            organizationDbConnection: orgDb,
            uniqueId: '',
            message: systemLogs.roleAdd + ' ' + formName.shiftScheduling + ' ' + ' - Bulk import Shift mapping'
        };

        // call function createSystemLogActivities() to update system log activities
        await commonLib.func.createSystemLogActivities(systemLogParams);
        
        return {
            success : true,
            errorCode : null,
            message : Object.keys(errorMessage).length ? JSON.stringify(errorMessage) : null
        }
    } catch(err) {
        console.log('Error in bulkImport function',err);
        let errResult = commonLib.func.getError('', 'SS0136');
        throw new ApolloError(errResult.message,errResult.code )
    } finally {
        // destroy database connection
        orgDb ? orgDb.destroy() : null;
    }
}
