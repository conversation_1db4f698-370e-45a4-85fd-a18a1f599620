// Organization database connection
var knex = require('knex');
// resolver definition
const resolvers = {
    Query: {
        // check whether the orgCode is exist or not
        checkOrganizationExists: async (parent, args, context, info) => {
            console.log('Inside checkOrganizationExists.js function.')
            try {
                // variable declaration
                let orgCode = context.Org_Code;
                // Get App-manager database connection
                var appManagerConnection = knex(context.connection.AppManagerDb);
                // variable declaration
                var organizationDbConnection = '';
                // check organization exist in the appmanager database
                return(
                    appManagerConnection('hrapp_registeruser')
                    .count('Org_Code as Count')
                    .where('Org_Code', orgCode)
                    .then(isOrgExists =>{
                        //  check if isOrgExists array is empty or not
                        if (isOrgExists.length > 0 && isOrgExists[0].Count === 1){
                            organizationDbConnection = knex(context.connection.OrganizationDb);
                            return (
                                organizationDbConnection
                                .select('SCHEMATA.SCHEMA_NAME')
                                .from('INFORMATION_SCHEMA.SCHEMATA')
                                .where('SCHEMA_NAME', process.env.dbPrefix + orgCode)
                                .then(checkDbExists => {
                                    // check database exist or not
                                    if (checkDbExists.length > 0){
                                        return { errorCode: '', message: 'Organisation exist.', isOrgExists : true }
                                    }else{
                                        return { errorCode: '', message: 'Organisation does not exist.', isOrgExists: false }
                                    }
                                })
                                /**return the success result to user */
                                .then(function (result) {
                                    return result;
                                })
                            )
                        }else{
                            return { errorCode: '', message: 'Organisation does not exist.', isOrgExists: false }
                        }                        
                    })
                    /**return the success result to user */
                    .then(function (result) {
                        return result;
                    })
                    /**catch organizationDbConnection connectivity errors */
                    .catch(function (err) {
                        console.log('error while checking the organization exist or not', err);
                        if (err.code === 'ECONNREFUSED') {
                            throw new Error(JSON.stringify({ errorCode: "SSO0000", message: 'Error in database connection.', isOrgExists: false }));
                        }else {
                            throw new Error(JSON.stringify({ errorCode: "SSO0002", message: 'Error while checking the organization exist or not.', isOrgExists: false }));
                        }
                    })
                    /**close db connection */
                    .finally(() => {
                        appManagerConnection.destroy();
                    })
                )
            }catch(error){
                console.log('error while checking the organization exist or not', error);
                if (error.message.toLowerCase() === 'missing database connection') {
                    throw new Error(JSON.stringify({ errorCode: "SSO0001", message: 'Database connection missing.', isOrgExists: false}));
                }
                else if (error.message.toLowerCase() === 'error in database connection') {
                    throw new Error(JSON.stringify({ errorCode: "SSO0000", message: 'Error in database connection', isOrgExists: false}));
                }
                else {
                    throw new Error(JSON.stringify({ errorCode: "SSO0002", message: 'Error while checking the organization exist or not.', isOrgExists: false}));
                }
            }
        }
    }
};

module.exports.resolvers = resolvers;