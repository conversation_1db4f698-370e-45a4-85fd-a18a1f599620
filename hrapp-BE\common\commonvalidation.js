/** IP Address validation **/
const validateIp = require('is-ip');
const emailValidation = /^[\w-\.]+@([\w-]+\.)+[\w-]{2,24}$/; /**email id validation */
const commentRegex = /^[\w\.\,\#\+\&\/\-\(\)\:\'\ ]*$/; /** Only alphanumeric, spaces and symbols + , / . # & : () ' - allowed. */
const yearValidation=/^\d{4}$/;
const decimal=/^\d{1,13}(\.\d{0,2})?$/;
const numberValidation = /^[0-9]+$/; /**only numbers allowed */
const nameValidation = /^[a-zA-Z\.\ ]+$/; /**name validation */
const mobileNumberValidation = /^([\+][0-9]{1,3}[ \.\-])?([\(]{1}[0-9]{2,6}[\)])?([0-9 \+\.\-\/]{5,15})((x|ext|extension)[ ]?[0-9]{1,4})?$/;//validate mobile number

module.exports = {
    ipAddressValidation: function (input) {
        return (result = validateIp(input) ? true : false);
    },
    checkLength: function (input, minLength, maxLength) {
        return (result = (input.length < minLength || input.length > maxLength) ? false : true);
    },
    emailValidation: function (input) {
        return (result = emailValidation.test(input) ? true : false);
    },
    commentValidation: function (input) {
        return (result = commentRegex.test(input) ? true : false);
    },
    checkMinMaxValue: function (input, minValue, maxValue) {
        return (result = (input < minValue || input > maxValue) ? false : true);
    },
    yearValidation:function(input){
        return (result = yearValidation.test(input) ? true : false);
    },
    decimalValidation : function(input){
        return (result = decimal.test(input) ? true : false);
    },
    numberValidation: function (input) {
        return (result = numberValidation.test(input) ? true : false);
    },
    nameValidation: function(input) {
        return (result = nameValidation.test(input) ? true : false);
    },
    mobileNumberValidation: function(input) {
        return (result = mobileNumberValidation.test(input) ? true : false);
    },
    booleanNumberValidation: function (input) {
        return (result = (input === 1 || input === 0) ? true : false);
    }
};