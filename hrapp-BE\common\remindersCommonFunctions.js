//Require tables
const {ehrTables} = require('./tablealias');

//Get the employee list for the alerts based on the alert settings
async function getAlertCondition(orgDb, query, alertName, todayDate, isEmailNotification=0){
    try{
        //Require moment package
        const moment = require('moment-timezone');

        /** get the number of days & before/after(period) for award alert**/
        let alertSettingsRow = await orgDb.select('AS.No_Of_Days','AS.Period','AS.Present_Until_Actioned')
                                .from(ehrTables.alertSettings + ' as AS')
                                .innerJoin(ehrTables.alertTypes + ' as AT', 'AT.Alert_Type_Id', 'AS.Alert_Type_Id')
                                .where('AT.Alert_Type', alertName)
                                .then((res)=>{
                                    return res[0] ? res[0] : null;
                                });

        if(alertSettingsRow && Object.keys(alertSettingsRow).length > 0)
        {
            /* based on the alert period, alert date will be calculated. 
            * For ex, for alert period before, add number of days to the current date,
            * For alert period after, subtract number of days to the current date
            */
            let alertDate;

            if(alertSettingsRow.Period === 'Before')
            {
                alertDate = moment(todayDate).add(alertSettingsRow.No_Of_Days, 'days').format("YYYY-MM-DD");
            }
            else
            {
                alertDate =  moment(todayDate).subtract(alertSettingsRow.No_Of_Days,'days').format('YYYY-MM-DD');
            }

            // Find the field name in the table based on the alert name
            let fieldLabel;
            switch(alertName)
            {
                case 'Birthday':            fieldLabel = 'DOB'; break;
                case 'New Hires':
                case 'Work Anniversary':    fieldLabel = 'Date_Of_Join'; break;
                case 'Award Announcement':  fieldLabel = 'Award_Date'; break;
                case 'License Expiry':      fieldLabel = 'License_Expiry_Date'; break;
                case 'Passport Expiry':     fieldLabel = 'Expiry_Date'; break;
                case 'Warnings':            fieldLabel = 'Warning_Date'; break;
                case 'Probation End Date':  fieldLabel = 'J.Probation_Date'; break;
                case 'Employees On Notice': fieldLabel = 'Resignation_Date'; break;
                case 'Confirmation Of Employment':  fieldLabel = 'Confirmation_Date'; break;
                case 'Long Leave Notification':  fieldLabel = 'L.End_Date'; break;
                default: return null;
            }

            if(alertName === 'Confirmation Of Employment' ||alertName === 'Award Announcement'|| 
                alertName === 'Probation End Date' || alertName === 'License Expiry' || alertName === 'Passport Expiry' ||
                alertName === 'Warnings' || alertName  === 'Employees On Notice' || alertName  === 'New Hires'
                || alertName === 'Long Leave Notification')
            {
                if(isEmailNotification === 0){
                    //If the alerts should not be presented till the action is taken
                    if(alertSettingsRow.Present_Until_Actioned === 'No'){
                        if(alertDate <= todayDate)
                        {
                            // If alert period is after
                            query = query.whereRaw(fieldLabel + '>= ?', [alertDate])
                                        .whereRaw(fieldLabel + '<= ?', [todayDate])
                        }
                        else
                        {
                            // If alert period is before
                            query = query.whereRaw(fieldLabel + '<= ?', [alertDate])
                                        .whereRaw(fieldLabel + '>= ?', [todayDate])
                        }
                    }else{
                        //If alert period is after
                        if(alertDate <= todayDate)
                        {
                            /** Consider today's date as 10 Aug 2022. The alert period is 5 days. The alert date will be 5 Aug 2022. So we should present 
                             * the record which exists on or before 5 Aug 2022 till the action is taken.*/
                            query = query.whereRaw(fieldLabel + '<= ?', [alertDate]);
                        }
                        else
                        {
                            // If alert period is before
                            
                            /** Consider today's date is 10 Aug 2022. The alert period is 5 days. The alert date will be 15 Aug 2022. 
                             * So we should present the record which exists on or before 15 Aug 2022 till the action is taken.*/
                            query = query.whereRaw(fieldLabel + '<= ?', [alertDate]);

                        }
                    }
                }else{
                    query = query.whereRaw(fieldLabel + '= ?', [alertDate]);
                }
            }
            else if(alertName === 'Work Anniversary')
            {
                if(alertDate <= todayDate)
                {
                    // If alert period is after
                    query = query.whereRaw("Year(Current_DATE()) > Year("+fieldLabel+")")
                                .whereRaw("CONCAT(Year(Current_DATE()),'-', Date_format("+fieldLabel+",'%m-%d')) >= ?", [alertDate])
                                .whereRaw("CONCAT(Year(Current_DATE()),'-', Date_format("+fieldLabel+", '%m-%d')) <= ?", [todayDate])
                }
                else
                {
                    // If alert period is before
                    query = query.whereRaw("Year(Current_DATE()) > Year("+fieldLabel+")")
                                .whereRaw("CONCAT(Year(Current_DATE()),'-', Date_format("+fieldLabel+",'%m-%d')) <= ?", [alertDate])
                                .whereRaw("CONCAT(Year(Current_DATE()),'-', Date_format("+fieldLabel+", '%m-%d')) >= ?", [todayDate])
                }
            }
            else
            {//If the alertName is Birthday
                if(alertDate <= todayDate)
                {
                    // If alert period is after
                    query = query.whereRaw("CONCAT(Year(Current_DATE()),'-', Date_format("+fieldLabel+",'%m-%d')) >= ?", [alertDate])
                                .whereRaw("CONCAT(Year(Current_DATE()),'-', Date_format("+fieldLabel+", '%m-%d')) <= ?", [todayDate])
                }
                else
                {
                    // If alert period is after
                    query = query.whereRaw("CONCAT(Year(Current_DATE()),'-', Date_format("+fieldLabel+",'%m-%d')) <= ?", [alertDate])
                                .whereRaw("CONCAT(Year(Current_DATE()),'-', Date_format("+fieldLabel+", '%m-%d')) >= ?", [todayDate])
                }
            }
            query = query.whereRaw(fieldLabel + ' != ?', ['0000-00-00'])
            return await query.select();
        }
        else
        {
            return null;
        }
    }catch(mainCatchError){
        console.log('Error in the getAlertCondition() function main catch block.',mainCatchError);
        throw mainCatchError;
    }
}

//Function to get the probation employees list
async function getProbationEmployeesList(organizationDbConnection,isTeamDashboard,loginEmployeeId,isEmailNotification=0,todayDate){
    try{
        let selectFields = ['J.Employee_Id as employeeId',
        organizationDbConnection.raw('CONCAT_WS(" ",P.Emp_First_Name, P.Emp_Middle_Name, P.Emp_Last_Name) as employeeName'),
        'J.Date_Of_Join as dateOfJoin','J.Probation_Date as probationDate','D.Probation_Days as probationDays',
        organizationDbConnection.raw('CASE WHEN J.User_Defined_EmpId IS NOT NULL THEN J.User_Defined_EmpId ELSE J.Employee_Id END as userDefinedEmployeeId')];
        let emailSelectFields = [];

        //If the email has to be sent, get the additional fields
        if(isEmailNotification === 1){
            emailSelectFields = [organizationDbConnection.raw('DATE_FORMAT(J.Probation_Date, "%D %M %Y") as displayProbationDate'),
            organizationDbConnection.raw('CONCAT_WS(" ",MPI.Emp_First_Name, MPI.Emp_Middle_Name, MPI.Emp_Last_Name) as managerName'),
            'EJ.Emp_Email as managerEmailAddress','J.Manager_Id as managerId'];//Get the manager email address
            selectFields = selectFields.concat(emailSelectFields);
        }
        let getProbationEmpQuery = organizationDbConnection.select(selectFields)
        .from(ehrTables.empJob + ' as J')
        .innerJoin(ehrTables.empPersonalInfo + ' as P', 'P.Employee_Id', 'J.Employee_Id')
        .innerJoin(ehrTables.designation + ' as D', 'D.Designation_Id', 'J.Designation_Id');
        //Join to get manager email address
        if(isEmailNotification === 1){
            getProbationEmpQuery = getProbationEmpQuery
                                    .leftJoin(ehrTables.empPersonalInfo + ' as MPI', 'J.Manager_Id', 'MPI.Employee_Id')
                                    .leftJoin(ehrTables.empJob + ' as EJ', 'J.Manager_Id', 'EJ.Employee_Id');
        }
        getProbationEmpQuery = getProbationEmpQuery
        .where('P.Form_Status', 1)
        .where('J.Emp_Status', 'Active')
        .where(qb =>{
            qb.where('J.Confirmed', '!=', 1)
        })
        .where(qb1 =>{
            qb1.where('J.Confirmation_Date', '0000-00-00')
            .orWhereNull('J.Confirmation_Date')
        })
        .orderBy('J.Probation_Date', 'asc');

        if(isTeamDashboard === 1){
            getProbationEmpQuery = getProbationEmpQuery
            .where(qb2 =>{
                qb2.where('J.Manager_Id',loginEmployeeId)
                .orWhere('J.Employee_Id',loginEmployeeId)
            });
        }
        //Call the function to get the probation employee details based on the alert settings
        let probationEmployeesDetails = await getAlertCondition(organizationDbConnection, getProbationEmpQuery, 'Probation End Date', todayDate, isEmailNotification);
        return probationEmployeesDetails;
    }catch(listCatchError){
        console.log('Error in the getProbationEmployeesList() function main catch block.',listCatchError);
        throw listCatchError;
    }
}

//Get the pending resignation approval employees list
async function getPendingApprovalEmployeesOnNotice(organizationDbConnection, todayDate)
{
    try
    {
        let selectFields = ['RP.Employee_Id as employeeId','CG.Group_Name as groupName','UTJ.Emp_Email as managerEmailAddress',
        organizationDbConnection.raw('CASE WHEN RJ.User_Defined_EmpId IS NOT NULL THEN RJ.User_Defined_EmpId ELSE RJ.Employee_Id END as userDefinedEmployeeId'),
        organizationDbConnection.raw('CONCAT_WS(" ",RP.Emp_First_Name, RP.Emp_Middle_Name, RP.Emp_Last_Name) as employeeName'),
        organizationDbConnection.raw('CONCAT_WS(" ",UTP.Emp_First_Name, UTP.Emp_Middle_Name, UTP.Emp_Last_Name) as assigneeName'),
        organizationDbConnection.raw('DATE_FORMAT(R.Resignation_Date, "%D %M %Y") as displayResignationDate')];
        
        let query = organizationDbConnection.select(selectFields)
                    .from(ehrTables.resignation + ' as R')
                    .innerJoin(ehrTables.taUserTask + ' as UT', 'R.Workflow_Instance_Id', 'UT.process_instance_id')
                    .leftJoin(ehrTables.empPersonalInfo + ' as UTP', 'UT.assignee', 'UTP.Employee_Id')
                    .leftJoin(ehrTables.empJob + ' as UTJ', 'UT.assignee', 'UTJ.Employee_Id')
                    .leftJoin(ehrTables.cusEmpGroup + ' as CG', 'UT.custom_group_id', 'CG.Group_Id')
                    .innerJoin(ehrTables.empPersonalInfo + ' as RP', 'R.Employee_Id', 'RP.Employee_Id')
                    .innerJoin(ehrTables.empJob + ' as RJ', 'R.Employee_Id', 'RJ.Employee_Id')
                    .where('RP.Form_Status', 1)
                    .where('RJ.Emp_Status', 'Active')
                    .where('R.Approval_Status', 'Applied')
                    .orderBy('R.Employee_Id', 'asc');
        let employeesOnNoticeDetails = await getAlertCondition(organizationDbConnection, query, 'Employees On Notice', todayDate,1);
        return employeesOnNoticeDetails;
    }
    catch(catchError){
        console.log("Error in the getPendingApprovalEmployeesOnNotice function main catch block.", catchError);
        throw catchError;
    }
}



//Function to get the employee details for long leave notification
async function getLongLeaveEmployeeDetails(organizationDbConnection,leaveTypeIds,todayDate,longLeaveTotalDays,isEmailNotification){
    try{
        let leaveApprovalStatus = ['Applied', 'Approved'];
        let selectFields = ['J.Employee_Id as employeeId','LT.Leave_Name as leaveName',
        organizationDbConnection.raw('CONCAT_WS(" ",P.Emp_First_Name, P.Emp_Middle_Name, P.Emp_Last_Name) as employeeName'),
        'L.End_Date as endDate',organizationDbConnection.raw('CASE WHEN J.User_Defined_EmpId IS NOT NULL THEN J.User_Defined_EmpId ELSE J.Employee_Id END as userDefinedEmployeeId')];
        let emailSelectFields = [];

        //If the email has to be sent, get the additional fields
        if(isEmailNotification === 1){
            emailSelectFields = [organizationDbConnection.raw('DATE_FORMAT(L.End_Date, "%D %M %Y") as displayLeaveEndDate'),
            organizationDbConnection.raw('CONCAT_WS(" ",MPI.Emp_First_Name, MPI.Emp_Middle_Name, MPI.Emp_Last_Name) as managerName'),
            'EJ.Emp_Email as managerEmailAddress'];//Get the manager email address
            selectFields = selectFields.concat(emailSelectFields);
        }

        let longLeaveEmpQuery = organizationDbConnection.select(selectFields)
        .from(ehrTables.empLeaves + ' as L')
        .innerJoin(ehrTables.leavetype + ' as LT', 'L.LeaveType_Id', 'LT.LeaveType_Id')
        .innerJoin(ehrTables.empPersonalInfo + ' as P', 'L.Employee_Id', 'P.Employee_Id')
        .innerJoin(ehrTables.empJob + ' as J', 'L.Employee_Id', 'J.Employee_Id');

        //Join to get manager email address
        if(isEmailNotification === 1){
            longLeaveEmpQuery = longLeaveEmpQuery
                                    .leftJoin(ehrTables.empPersonalInfo + ' as MPI', 'J.Manager_Id', 'MPI.Employee_Id')
                                    .leftJoin(ehrTables.empJob + ' as EJ', 'J.Manager_Id', 'EJ.Employee_Id');
        }
        longLeaveEmpQuery = longLeaveEmpQuery
        .whereIn('L.LeaveType_Id',leaveTypeIds)
        .where('L.Total_Days','>',longLeaveTotalDays)
        .whereIn('L.Approval_Status',leaveApprovalStatus)
        .where('P.Form_Status', 1)
        .where('J.Emp_Status', 'Active');

        //Call the function to get the long leave employee details based on the alert settings
        let employeesDetails = await getAlertCondition(organizationDbConnection, longLeaveEmpQuery, 'Long Leave Notification', todayDate, isEmailNotification);
        return employeesDetails;
    }catch(getEmployeeMainCatchError){
        console.log('Error in the getLongLeaveEmployeeDetails function main catch block.',getEmployeeMainCatchError);
        throw getEmployeeMainCatchError;
    }
}

module.exports = {
    getAlertCondition,
    getProbationEmployeesList,
    getPendingApprovalEmployeesOnNotice,
    getLongLeaveEmployeeDetails
}