const { ApolloError,UserInputError} = require('apollo-server-lambda');
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
// require table names
const { ehrTables } = require('../../common/tablealias');

module.exports.getEmployeeDetailsBasedOnGroup = async (parent, args, context, info) => {
    let organizationDbConnection;
    try{
        let customGroupId=args.customGroupId;
        let validationError={}
        if(!customGroupId.length)
        {
            validationError['IVE0273']=commonLib.func.getError('', 'IVE0273').message;
            throw("IVE0000")
        }
        // get the organization database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);
        return(
           organizationDbConnection(ehrTables.cusEmpGroupEmp)
           .select('CEGE.Employee_Id as employeeId','EJ.User_Defined_EmpId as userDefinedEmpId', 'DES.Designation_Name as designationName', 'DEP.Department_Name as departmentName','CEG.Group_Id as groupId','CEG.Group_Name as groupName',
           organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as employeeName"))
           .from(ehrTables.cusEmpGroupEmp + ' as CEGE')
           .leftJoin(ehrTables.empJob + ' as EJ','EJ.Employee_Id','CEGE.Employee_Id')
           .leftJoin(ehrTables.empPersonalInfo + ' as EPI','EPI.Employee_Id','EJ.Employee_Id')
           .leftJoin(ehrTables.designation + ' as DES', 'DES.Designation_Id','EJ.Designation_Id')
           .leftJoin(ehrTables.department + ' as DEP', 'DEP.Department_Id','EJ.Department_Id')
           .leftJoin(ehrTables.cusEmpGroup + ' as CEG', 'CEG.Group_Id','CEGE.Group_Id')
           .where('EJ.Emp_Status','Active')
           .whereIn('CEGE.Group_Id',customGroupId)
           .whereIn('CEGE.Type',['Default','AdditionalInclusion'])
           .then(data=>{
               organizationDbConnection?organizationDbConnection.destroy():null;
               return {errorCode:"",message:"Employee details retrieved successfully based on group.",employeeDetails:data};
           })
           .catch(e=>{
               console.log("Error in getEmployeeDetailsBasedOnGroup() function .catch block.",e);
               throw('EE00102')
           })
        )
    }
    catch(e)
    {
        organizationDbConnection?organizationDbConnection.destroy():null;
        console.log("Error in getEmployeeDetailsBasedOnGroup() function main catch block.",e);
        if(e=='IVE0000')
        {
            let errResult = commonLib.func.getError(e, 'IVE0000');
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        }
        let errResult = commonLib.func.getError(e,'EE00102');
        throw new ApolloError(errResult.message, errResult.code);
    }
}