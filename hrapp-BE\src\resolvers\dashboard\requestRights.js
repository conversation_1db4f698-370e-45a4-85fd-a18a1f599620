
module.exports.requestRights = async(parent, args, context, info) => {
    console.log("In requestRights function");
    const { ehrTables } = require('../../../common/tablealias');
    const { formIds, awsSesTemplates } = require('../../../common/appconstants');
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // Organization database connection
    const knex = require('knex');
    // get the organization data base connection
    let orgDb = knex(context.connection.OrganizationDb);

    try
    {
        let logInEmpId = context.Employee_Id;

        // Retrieve all the employee ids which are in active state
        let activeEmpIds = await orgDb.pluck('P.Employee_Id')
                                        .from(ehrTables.empPersonalInfo + ' as P')
                                        .leftJoin(ehrTables.empJob + ' as J', 'J.Employee_Id', 'P.Employee_Id')
                                        .where('P.Form_Status', 1)
                                        .where('J.Emp_Status', 'Active')

        // Retrieve the employee ids which are configure under employee level access rights
        let empLevelIds = await orgDb.pluck('Employee_Id')
                                            .distinct('Employee_Id')
                                            .from(ehrTables.empAccessRights)
                                            .whereIn('Employee_Id',activeEmpIds)

        let empLevelRightsIds = await orgDb.pluck('Employee_Id')
                                            .distinct('Employee_Id')
                                            .from(ehrTables.empAccessRights)
                                            .where('Form_Id', formIds.superAdmin)
                                            .where('Role_Optional_Choice', 1)
                                            .whereIn('Employee_Id', empLevelIds)

        // Retrieve the employee ids which are configure under designation level access rights
        let desgLevelRightsIds = await orgDb.pluck('J.Employee_Id')
                                            .distinct('J.Employee_Id')
                                            .from(ehrTables.ehrRoles + ' as ER')
                                            .leftJoin(ehrTables.designation + ' as D', 'ER.Designation_Id', 'D.Designation_Id')
                                            .leftJoin(ehrTables.empJob + ' as J', 'J.Designation_Id', 'D.Designation_Id')
                                            .where('ER.Role_Optional_Choice', 1)
                                            .where('ER.Form_Id', formIds.superAdmin)
                                            .whereIn('J.Employee_Id', activeEmpIds)
                                            .whereNotIn('J.Employee_Id', empLevelIds)

        // Merge two arrays
        let superAdminIds = [...empLevelRightsIds, ...desgLevelRightsIds];

        // Get the email id for the employee ids in superAdminIds
        if(superAdminIds.length)
        {
            // Get the email id which is associated with emlpoyee id's
            let emailIds = await orgDb.pluck('Emp_Email')
                                        .from(ehrTables.empJob)
                                        .whereIn("Employee_Id", superAdminIds)


            emailIds = emailIds.filter(id => id ? true : false);

            if(emailIds.length)
            {
                let empDetails = await commonLib.func.getAllEmployeeDetails(orgDb,'listemployeedetails',context.Org_Code,null,null,null,{employeeId:[logInEmpId]},null)
                
                if(empDetails[0])
                {
                    let orgDetails = await commonLib.func.getOrgDetails(context.Org_Code, orgDb);

                    // Form the parameters for sending email
                    let params = {
                        'Source': process.env.emailFrom,
                        'Template': awsSesTemplates.requestRights,
                        'Destination': {
                            'ToAddresses': emailIds
                        },
                        'ReplyToAddresses': empDetails[0].emp_email ? [empDetails[0].emp_email] : [],
                        'TemplateData': JSON.stringify({
                            orgLogo: orgDetails.logoPath ? orgDetails.logoPath : '',
                            empName: empDetails[0].employee_name,
                            empShortName: (empDetails[0].first_name.charAt(0) + empDetails[0].last_name.charAt(0)).toUpperCase()  ,
                            empId: empDetails[0].user_defined_empid,
                            domainUrl: context.Org_Code + '.' + process.env.domainName + process.env.webAddress,
                            redirectUrl: 'https://' + context.Org_Code + '.' + process.env.domainName + process.env.webAddress + '/employees/employees'
                        })
                    };

                    await commonLib.func.sendEmailNotifications(params, process.env.sesTemplatesRegion)

                    return{
                        errorCode: '',
                        message: 'Request sent successfully'
                    }
                }
                else
                {
                    return{
                        errorCode: '',
                        message: 'Could not find the employee details'
                    }
                }
            }
            else
            {
                return{
                    errorCode: '',
                    message: 'Email id not found for super admin to send request'
                }
            }
        }
        else
        {
            return{
                errorCode: '',
                message: 'No super admin found to send request'
            }
        }
    }
    catch(requestRightsErr)
    {
        console.log('Error in requestRights() function main catch block.',requestRightsErr);

        errResult = commonLib.func.getError('', 'DB0025');

        // return response
        throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message}));
    }
}
