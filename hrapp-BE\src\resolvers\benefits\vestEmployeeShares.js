// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require apollo server to return error message
const { ApolloError,UserInputError } = require('apollo-server-lambda');
// require file to access constant values
const { formName,systemLogs } = require('../../../common/appconstants');
// require table alias function
const { ehrTables } = require('../../../common/tablealias');
// require validation file
const benefitsInputValidation=require('../../../common/benefitsInputValidation');

// variable declarations
let organizationDbConnection='';
let errResult={};

// resolver definition
const resolvers = {
    Mutation:{
        // function to vest the employee shares
        vestEmployeeShares: async (parent, args, context, info) => {
            let validationError={};
            try{
                console.log('Inside vestEmployeeShares function');
                // variable declarations
                let logInEmpId=context.Employee_Id;
                let ipAddress=context.User_Ip;
                let remainingShares;
                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                // check whether employee have update access for ESOP form
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.esop, '', 'UI');  
                if(Object.keys(checkRights).length>0 && (checkRights.Role_Update === 1)) {
                    // Check benefits admin form - update access rights exist for employee or not
                    let checkBenefitsAdminRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId , formName.benefitsAdmin, '', 'UI');
                    if(Object.keys(checkBenefitsAdminRights).length>0 && checkBenefitsAdminRights.Role_Update === 1) {
                        // validate the input fields
                        validationError=await benefitsInputValidation.editFormInputValidation(args,'','vest');
                        // Check validation error exist or not
                        if (Object.keys(validationError).length === 0) {
                            return (
                                organizationDbConnection
                                .transaction(function (trx) {
                                    // Get the employee share details based on input allocated shareId
                                    return(
                                        organizationDbConnection(ehrTables.employeeShareDetails)
                                        .select('Vested_Status','Allocated_Shares','Employee_Id')
                                        .where('Employee_Allocated_Share_Id',args.allocatedShareId)
                                        .transacting(trx)
                                        .then(async (getShareDetails) =>{
                                            // check whether the allocated shares already vested or not
                                            if((getShareDetails[0].Vested_Status)==='Vest Now'){
                                                // check whether input vested shares less than the allocated shares
                                                if(getShareDetails[0].Allocated_Shares<args.vestedShare){
                                                    throw 'BES0112';
                                                }
                                                else{
                                                    // get the total vested share value from the history table
                                                    return(
                                                        organizationDbConnection(ehrTables.employeeShareVestHistory)
                                                        .select(organizationDbConnection.raw('SUM(Vested_Share) as totalVestedShares'))
                                                        .where('Employee_Allocated_Share_Id',args.allocatedShareId)
                                                        .transacting(trx)
                                                        .then(async (vestShareDetails) =>{
                                                            let empTimeZone=await commonLib.func.getEmployeeTimeZone(logInEmpId, organizationDbConnection,1);
                                                            if(vestShareDetails[0].totalVestedShares>0){
                                                                remainingShares=(getShareDetails[0].Allocated_Shares - vestShareDetails[0].totalVestedShares);
                                                                // vested shares should be less than the remaining shares
                                                                if(args.vestedShare>remainingShares){
                                                                    throw 'BES0113';
                                                                }
                                                            }
                                                            // If shares are not vested then remaining shares will be same as allocated shares
                                                            else{
                                                                remainingShares=getShareDetails[0].Allocated_Shares;
                                                            }
                                                            // form input params
                                                            let inputParams={
                                                                Employee_Allocated_Share_Id:args.allocatedShareId,
                                                                Vested_Share:args.vestedShare,
                                                                Vested_Date:args.vestedDate,
                                                                Added_On:empTimeZone,
                                                                Added_By:logInEmpId
                                                            }
                                                            // insert into the employee share vesting history
                                                            return(
                                                                organizationDbConnection(ehrTables.employeeShareVestHistory)
                                                                .insert(inputParams)
                                                                .transacting(trx)
                                                                .then(async () =>{
                                                                    // If all the remaining shares are vested then update the status as 'All Vested'
                                                                    if(remainingShares===args.vestedShare){
                                                                        await organizationDbConnection(ehrTables.employeeShareDetails)
                                                                        .update({
                                                                            Vested_Status:'All Vested',
                                                                            Updated_On: empTimeZone,
                                                                            Updated_By:logInEmpId
                                                                        })
                                                                        .where('Employee_Allocated_Share_Id',args.allocatedShareId)
                                                                        .transacting(trx)
                                                                        .then(async () =>{
                                                                            console.log('Updated the vesting status in share details table');
                                                                        });
                                                                    }
                                                                    let systemLogParams = {
                                                                        action: systemLogs.roleUpdate,
                                                                        userIp: ipAddress,
                                                                        employeeId: logInEmpId,
                                                                        formName: formName.esop,
                                                                        trackingColumn: '- Shares vested for the share Id',
                                                                        organizationDbConnection: organizationDbConnection,
                                                                        uniqueId: args.allocatedShareId
                                                                    }
                                                                    // call function createSystemLogActivities() to update system log activities
                                                                    await commonLib.func.createSystemLogActivities(systemLogParams);
                                                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                                    return { errorCode:'', message:'Employee shares vested successfully.'};
                                                                })
                                                            );
                                                        })
                                                    );
                                                }
                                            }
                                            else{
                                                throw 'BES0109';
                                            }
                                        })
                                    );
                                })
                                .catch(function (catchError) {
                                    console.log('Error in vestEmployeeShares function .catch block',catchError);
                                    errResult = commonLib.func.getError(catchError, 'BES0111');
                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                    throw new ApolloError(errResult.message,errResult.code);
                                })
                            );
                        }
                        else{
                            throw 'IVE0000';
                        }
                    }
                    else{
                        throw '_DB0109';
                    }
                }
                else if (checkRights === false) {
                    throw '_DB0102';
                } else {
                    throw (checkRights);
                }
            }
            catch(mainCatchError)
            {
                console.log('Error in vestEmployeeShares function main catch block',mainCatchError);
                // destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                if(mainCatchError==='IVE0000'){
                    errResult = commonLib.func.getError('', 'IVE0000');
                    throw new UserInputError(errResult.message, { validationError: validationError });
                }
                else{
                    errResult = commonLib.func.getError(mainCatchError, 'BES0010');
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        }
    }
};
    
exports.resolvers = resolvers;
