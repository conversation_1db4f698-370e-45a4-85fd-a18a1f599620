//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make database connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../../common/tablealias');
//Require common constant files
const { formName,defaultValues } = require('../../../../common/appconstants');
//Require common function
const { getPmsAlreadyExistEmpIds } = require('../../../../common/performanceManagementCommonFunction');
//Require pms validation function
const performanceManagementValidation = require('../../../../common/performanceManagementValidation');
//Require moment
const moment = require('moment');

//Get the goals and achievement non compliance report
module.exports.retrievePmsNonComplianceReport = async (parent, args, context, info) => {
    console.log("Inside retrievePmsNonComplianceReport() function.");
    let organizationDbConnection;
    let errResult;
    let validationError = {};
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const orgCode = context.Org_Code;

        // Check Goals and achievement form access rights.
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, context.Employee_Id, formName.goalsAndAchievement, '', 'UI');

        // If the login employee is admin and having view access for the 'Goals and achievement' form
        if ((Object.keys(checkRights).length >0 && checkRights.Role_View===1) && checkRights.Employee_Role==='admin') {
            //Function to validate inputs
            validationError=await performanceManagementValidation.employeeGoalsAndRatingInputValidation(args,'validateMonthYear');
            //Check whether there is no validation error
            if(Object.keys(validationError).length ===0){
                let nonComplianceReportDetails=[];
                /** Call the function to get the employee ids that have to be excluded. When the 
                 * employee - performance goal achievement record exists in the performance_goal_achievement 
                 * table except with the Assessment_Status "Rated" for the previous assessment month then
                 * those employees will be excluded. Or when the employee performance goal achievement
                 * record exists for the given(input) assessment month and year in the 
                 * 'performance_goal_achievement' table then those employees will be excluded. */
                let alreadyExistEmpIds = await getPmsAlreadyExistEmpIds(organizationDbConnection,args.month,args.year);
                args.alreadyExistEmpIds = alreadyExistEmpIds;
                let salaryStartDate = moment(args.year+'-'+args.month+'-01').format('YYYY-MM-DD');
                let lastSalaryDate = moment(salaryStartDate,'YYYY-MM-DD').endOf('month').format('YYYY-MM-DD');

                //Get the previous month goals and achievement records
                if(args.month === 1){
                    args.previousMonth = 12;
                    args.previousYear = args.year-1;
                }else{
                    args.previousMonth = args.month-1;
                    args.previousYear = args.year;
                }
                return(
                organizationDbConnection(ehrTables.performanceGoalAchievement+' as PGA')
                .select(organizationDbConnection.raw("(MONTHNAME(STR_TO_DATE(PGA.Performance_Assessment_Month,'%m'))) as performanceMonthName"),
                organizationDbConnection.raw("(CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name)) as employeeName"),
                organizationDbConnection.raw("(CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name)) as reviewerName"),
                organizationDbConnection.raw('(CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE EJ.Employee_Id END) as userDefinedEmployeeId'),
                organizationDbConnection.raw(`(CASE WHEN PGA.Goal_Publish_Status = 'Unpublished' THEN 'Unpublished Goals' `+ 
                `WHEN PGA.Assessment_Status != 'Rated' THEN 'Rating Incomplete' ELSE 'Unpublished Rating' END) as status`),
                'EJ.Date_Of_Join as dateOfJoin','DES.Designation_Name as designationName','DEP.Department_Name as departmentName',
                'PGA.Performance_Assessment_Year as performanceYear','LOC.Location_Name as locationName')
                .innerJoin(ehrTables.empPersonalInfo+' as EPI1','EPI1.Employee_Id', 'PGA.Employee_Id')
                .innerJoin(ehrTables.empJob+' as EJ','EPI1.Employee_Id', 'EJ.Employee_Id')
                .innerJoin(ehrTables.designation+' as DES','EJ.Designation_Id','DES.Designation_Id')
                .innerJoin(ehrTables.department+' as DEP','EJ.Department_Id','DEP.Department_Id')
                .innerJoin(ehrTables.location+' as LOC','EJ.Location_Id','LOC.Location_Id')
                .leftJoin(ehrTables.empPersonalInfo+' as EPI2','EPI2.Employee_Id', 'EJ.Manager_Id')
                .where('PGA.Performance_Assessment_Month', args.previousMonth)
                .where('PGA.Performance_Assessment_Year', args.previousYear)
                .where(qb => {
                    qb.whereNot('PGA.Rating_Publish_Status', defaultValues.goalsAndAchievementGoalPublishStatus)
                    qb.orWhere('PGA.Rating_Publish_Status','')
                    qb.orWhereNull('PGA.Rating_Publish_Status')
                })
                .then((previousPerformanceMonthResponse) =>{
                    if(previousPerformanceMonthResponse.length >0){
                        nonComplianceReportDetails = nonComplianceReportDetails.concat(previousPerformanceMonthResponse);
                    }

                    //Get the employee details for whom the performance is not added for the given assessment month and assessment year
                    return(
                    organizationDbConnection(ehrTables.empPersonalInfo)
                    .select('EPI1.Employee_Id as employee_id', 'EJ.Date_Of_Join as dateOfJoin','DES.Designation_Name as designationName','LOC.Location_Name as locationName',
                    'DEP.Department_Name as departmentName', organizationDbConnection.raw("(CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name)) as employeeName"),
                    organizationDbConnection.raw("(CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name)) as reviewerName"),
                    organizationDbConnection.raw('(CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE EJ.Employee_Id END) as userDefinedEmployeeId'))
                    .from(ehrTables.empPersonalInfo+' as EPI1')
                    .innerJoin(ehrTables.empJob+' as EJ','EPI1.Employee_Id', 'EJ.Employee_Id')
                    .innerJoin(ehrTables.designation+' as DES','EJ.Designation_Id','DES.Designation_Id')
                    .innerJoin(ehrTables.department+' as DEP','EJ.Department_Id','DEP.Department_Id')
                    .innerJoin(ehrTables.location+' as LOC','EJ.Location_Id','LOC.Location_Id')
                    .leftJoin(ehrTables.empPersonalInfo+' as EPI2','EPI2.Employee_Id', 'EJ.Manager_Id')
                    .whereNotIn('EPI1.Employee_Id', alreadyExistEmpIds)
                    .whereNot('EJ.Date_Of_Join', '>',lastSalaryDate)
                    .then(async(notAppliedListResponse)=>{
                        if(notAppliedListResponse.length>0){
                            let performanceNotAppliedList = notAppliedListResponse.map(v => ({...v, status: 'Unassigned Goals',
                            performanceMonthName: defaultValues.monthsArray[args.month-1],performanceYear: args.year}));
                            //Form inputs to get the employees list based on the resignation
                            let activeInActiveListArgs = {
                                filterMonthYear: args.month+','+args.year,
                                salaryStartDate: salaryStartDate
                            };
                            let notAppliedEmployeeDetails = await commonLib.func.listInactiveEmployeeTillFinalSettlement(organizationDbConnection,orgCode,activeInActiveListArgs,performanceNotAppliedList);
                            nonComplianceReportDetails = nonComplianceReportDetails.concat(notAppliedEmployeeDetails);
                        }
    
                        //Get the given performance month goals and achievement records
                        let performanceEmployeeQuery =
                        organizationDbConnection(ehrTables.performanceGoalAchievement+' as PGA')
                        .select(organizationDbConnection.raw("(MONTHNAME(STR_TO_DATE(PGA.Performance_Assessment_Month,'%m'))) as performanceMonthName"),
                        organizationDbConnection.raw("(CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name)) as employeeName"),
                        organizationDbConnection.raw("(CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name)) as reviewerName"),
                        organizationDbConnection.raw('(CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE EJ.Employee_Id END) as userDefinedEmployeeId'),
                        organizationDbConnection.raw(`(CASE WHEN PGA.Goal_Publish_Status = 'Unpublished' THEN 'Unpublished Goals' `+ 
                        `WHEN PGA.Assessment_Status != 'Rated' THEN 'Rating Incomplete' ELSE 'Unpublished Rating' END) as status`),
                        'EJ.Date_Of_Join as dateOfJoin','DES.Designation_Name as designationName','DEP.Department_Name as departmentName','LOC.Location_Name as locationName',
                        'PGA.Performance_Assessment_Year as performanceYear')
                        .innerJoin(ehrTables.empPersonalInfo+' as EPI1','EPI1.Employee_Id', 'PGA.Employee_Id')
                        .innerJoin(ehrTables.empJob+' as EJ','EPI1.Employee_Id', 'EJ.Employee_Id')
                        .innerJoin(ehrTables.designation+' as DES','EJ.Designation_Id','DES.Designation_Id')
                        .innerJoin(ehrTables.department+' as DEP','EJ.Department_Id','DEP.Department_Id')
                        .innerJoin(ehrTables.location+' as LOC','EJ.Location_Id','LOC.Location_Id')
                        .leftJoin(ehrTables.empPersonalInfo+' as EPI2','EPI2.Employee_Id', 'EJ.Manager_Id')
                        .where('PGA.Performance_Assessment_Month', args.month)
                        .where('PGA.Performance_Assessment_Year', args.year);
    
                        // get the current date based based on employee location
                        let employeeCurrentDateTime = await commonLib.func.getEmployeeTimeZone(context.Employee_Id, organizationDbConnection, 1);
                        let currentDate = moment(employeeCurrentDateTime).format('YYYY-MM-DD');
                        let splitCurrentDate = currentDate.split('-');
                        let currentDay = splitCurrentDate[2];
                        /** If the current day is less than or equal to 15 we need to get the unpublished goals
                         * of the given month. */
                        if(currentDay <= 15){
                            performanceEmployeeQuery = performanceEmployeeQuery
                            .where('PGA.Goal_Publish_Status','Unpublished');
                        }else{
                            /** If the current day is greater than or equal to 15 we need to get the unpublished goals
                            * and rating incomplete records and unpublished rating records of the given month. */
                            performanceEmployeeQuery = performanceEmployeeQuery
                            .where(qb => {
                                qb.where('PGA.Goal_Publish_Status','Unpublished')
                                qb.orWhere( qb1 => {
                                    qb1.whereNot('PGA.Rating_Publish_Status', defaultValues.goalsAndAchievementGoalPublishStatus)
                                    qb1.orWhere('PGA.Rating_Publish_Status','')
                                    qb1.orWhereNull('PGA.Rating_Publish_Status')
                                })
                            });
                        }
    
                        return(
                        performanceEmployeeQuery
                        .then((currentPerformanceMonthResponse) =>{
                            if(currentPerformanceMonthResponse.length >0){
                                nonComplianceReportDetails = nonComplianceReportDetails.concat(currentPerformanceMonthResponse);
                            }

                            //Destroy database connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return {
                                errorCode : '',
                                message : "Non compliance report details are retrieved successfully.",
                                employees: (nonComplianceReportDetails && nonComplianceReportDetails.length > 0) ? nonComplianceReportDetails : []
                            };
                            
                        })
                        )
                    })
                    );
                })
                .catch(catchError => {
                    console.log('Error in the retrievePmsNonComplianceReport() .catch block.',catchError);
                    //Destroy database connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    errResult = commonLib.func.getError(catchError, 'EPM0132');
                    //Return error response
                    throw new ApolloError(errResult.message,errResult.code);
                })
                )
            }else{
                throw 'IVE0000';// throw validation error
            }
        }else{
            throw '_DB0100';// throw employee does not have view access
        }
    }
    catch(retrievePmsNonComplianceReportMainCatchErr) {
        console.log('Error in the retrievePmsNonComplianceReport() function main catch block. ',retrievePmsNonComplianceReportMainCatchErr);
        //Destroy database connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (retrievePmsNonComplianceReportMainCatchErr === 'IVE0000') {
            console.log('Validation error in the retrievePmsNonComplianceReport() function. ',validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            //Return error response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else{
            errResult = commonLib.func.getError(retrievePmsNonComplianceReportMainCatchErr, 'EPM0033');
            //Return error response
            throw new ApolloError(errResult.message,errResult.code);
        }
    }
};